#!/usr/bin/env python3
"""
Test script to debug the actual script generation process
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_script_generation():
    """Test the actual script generation process"""
    try:
        print("🧪 Testing actual script generation process...")
        
        # Import the script generator
        from src.script_service.services.script_generator import ScriptGenerator
        from src.script_service.utils.generators.scene_processor import SceneProcessor
        
        # Create instances
        script_generator = ScriptGenerator()
        scene_processor = SceneProcessor()
        
        # Test data (same as in the logs)
        script_data = {
            'text_input': 'Create a sleek, modern commercial showcasing a premium work bag designed for professionals. Highlight its stylish design, smart compartments for laptops and documents, durable materials, and how it complements a polished business look in office and travel settings. Tone: sophisticated, confident, and aspirational.',
            'video_style': 'Minimal',
            'duration': '30 seconds',
            'brand_name': '',
            'product_name': '',
            'brand_description': ''
        }
        
        print(f"📝 Generating script with data:")
        print(f"   - Text input: {script_data['text_input'][:100]}...")
        print(f"   - Video style: {script_data['video_style']}")
        print(f"   - Duration: {script_data['duration']}")
        
        # Generate the script content
        print("🤖 Calling script generator...")
        script_content = script_generator.generate_initial_script(
            text_input=script_data['text_input'],
            video_style=script_data['video_style'],
            duration=script_data['duration'],
            brand_name=script_data['brand_name'],
            product_name=script_data['product_name'],
            brand_description=script_data['brand_description']
        )
        
        print(f"✅ Script generated successfully!")
        print(f"📊 Script length: {len(script_content)} characters")
        print(f"📝 Script preview (first 500 chars):")
        print("-" * 50)
        print(script_content[:500])
        print("-" * 50)
        
        # Now test scene processing on the actual generated content
        print("\n🎬 Processing scenes from generated script...")
        scenes = scene_processor.process_script_content(script_content)
        
        print(f"✅ Scene processing completed!")
        print(f"📊 Scenes extracted: {len(scenes)}")
        
        if scenes:
            for i, scene in enumerate(scenes):
                print(f"📋 Scene {i+1}: {scene.get('title', 'No title')}")
                print(f"   - Number: {scene.get('scene_number', 'N/A')}")
                print(f"   - Visual: {scene.get('visual_description', 'N/A')[:50]}...")
                print(f"   - Narration: {scene.get('narration', 'N/A')[:50]}...")
        else:
            print("❌ No scenes were extracted from the generated script!")
            print("\n🔍 Let's analyze the script content...")
            
            # Check for common scene patterns
            patterns_to_check = [
                r"Scene\s+\d+",
                r"SCENE\s+\d+",
                r"\d+\.\s*Scene",
                r"Scene\s*\d+:",
                r"SCENE\s*\d+:",
            ]
            
            import re
            for pattern in patterns_to_check:
                matches = re.findall(pattern, script_content, re.IGNORECASE)
                print(f"   Pattern '{pattern}': {len(matches)} matches")
                if matches:
                    print(f"     Examples: {matches[:3]}")
            
            # Show some lines that might contain scenes
            lines = script_content.split('\n')
            print(f"\n📄 Script has {len(lines)} lines. Sample lines:")
            for i, line in enumerate(lines[:20]):
                if line.strip():
                    print(f"   Line {i+1}: {line.strip()[:80]}...")
        
        return len(scenes) > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("🚀 Starting script generation debugging...")
    print("=" * 60)
    
    success = test_script_generation()
    
    print("=" * 60)
    if success:
        print("🎉 Test passed - scenes were extracted!")
        return 0
    else:
        print("❌ Test failed - no scenes extracted!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
