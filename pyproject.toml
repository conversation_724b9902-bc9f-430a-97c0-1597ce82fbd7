[project]
name = "vidflux-backend"
version = "0.1.0"
description = "VidFlux Backend - AI-powered video generation platform with agentic workflows"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "alembic>=1.12.0",
    "asyncio>=3.4.3",
    "asyncpg>=0.28.0",
    "boto3>=1.39.4",
    "celery>=5.3.0",
    "email-validator>=2.0.0",
    "fal-client>=0.7.0",
    "fastapi>=0.104.0",
    "google-auth>=2.23.0",
    "google-generativeai>=0.3.0",
    "gradio-client>=1.10.4",
    "greenlet>=3.2.3",
    "httpx>=0.25.0",
    "librosa>=0.11.0",
    "loguru>=0.7.0",
    "moviepy>=1.0.3",
    "numpy>=1.24.0",
    "opencv-python>=4.8.0",
    "passlib[bcrypt]>=1.7.4",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",
    "pydub>=0.25.1",
    "pyjwt>=2.8.0",
    "redis>=5.0.0",
    "requests>=2.31.0",
    "resend>=0.8.0",
    "runpod>=1.7.9",
    "sqlalchemy>=2.0.0",
    "uvicorn[standard]>=0.24.0",
    "langchain>=0.1.0",
    "langchain-core>=0.1.0",
    "langchain-google-genai>=1.0.0",
    "langgraph>=0.1.0",
    "anthropic>=0.25.0",
]

[tool.hatch.build.targets.wheel]
packages = ["src"]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]

[tool.black]
line-length = 100
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
