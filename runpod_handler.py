"""
Simplified RunPod Serverless Handler for VidFlux Video Stitching
Handles both video stitching and audio mixing operations
"""

import runpod
import os
import sys
import asyncio
import traceback
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# Add the src directory to Python path for imports
sys.path.append("/app/src")

# Set minimal environment defaults
os.environ.setdefault("DEBUG", "False")
os.environ.setdefault("APP_NAME", "VidFlux RunPod Worker")
os.environ.setdefault("APP_VERSION", "1.0.0")

try:
    # Import required modules
    from src.video_service.services.video_stitcher import VideoStitcher
    from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer
    from src.shared.utils.s3_client import S3Client
    from src.shared.models.database_models import VideoAsset, Scene, AudioAsset, TaskQueue, Script
    from src.shared.config.database import db_manager
    from src.audio_service.services.background_sound import EnhancedBackgroundSoundGenerator
    from sqlalchemy import and_, select, desc
    import uuid

    print("✅ All imports successful")

except Exception as e:
    print(f"❌ Import error: {str(e)}")
    print(f"Traceback: {traceback.format_exc()}")
    raise

# Initialize services
video_stitcher = VideoStitcher()
audio_mixer = EnhancedVideoAudioMixer(output_dir="/tmp/final_videos")
s3_client = S3Client(os.getenv("AWS_DEFAULT_REGION", "us-east-2"))


async def stitch_videos_with_proper_timing(
    video_paths: List[str], output_path: str, scenes_data: List = None
) -> str:
    """
    Improved video stitching that maintains scene timing for proper audio sync.
    Uses MoviePy for better control over timing and transitions.
    """
    if not video_paths:
        raise Exception("No video paths provided for stitching")

    if len(video_paths) == 1:
        # If only one video, just copy it
        import shutil

        shutil.copy2(video_paths[0], output_path)
        return output_path

    try:
        from moviepy import VideoFileClip, concatenate_videoclips

        print(f"🎞️ Loading {len(video_paths)} video clips for stitching...")

        # Load all video clips
        clips = []
        total_duration = 0

        for i, video_path in enumerate(video_paths):
            print(f"📹 Loading video {i+1}/{len(video_paths)}: {video_path}")
            clip = VideoFileClip(video_path)

            # Normalize properties for consistent stitching
            if clip.size != (1920, 1080):
                clip = clip.resized((1920, 1080))
            if abs(clip.fps - 30) > 0.1:
                clip = clip.with_fps(30)

            clips.append(clip)
            total_duration += clip.duration
            print(f"   Duration: {clip.duration:.2f}s, Size: {clip.size}, FPS: {clip.fps}")

        print(f"🎬 Total video duration: {total_duration:.2f}s")

        # Concatenate clips with proper timing preservation
        print("🔗 Concatenating video clips...")
        final_video = concatenate_videoclips(clips, method="compose")

        # Write the final video
        print(f"💾 Writing stitched video to: {output_path}")
        final_video.write_videofile(
            output_path,
            fps=30,
            codec="libx264",
            audio_codec="aac",
            temp_audiofile="temp-audio.m4a",
            remove_temp=True,
            logger=None,
        )

        # Clean up clips
        for clip in clips:
            clip.close()
        final_video.close()

        if not os.path.exists(output_path):
            raise Exception("Output file was not created")

        print(f"✅ Video stitching completed: {output_path}")
        return output_path

    except Exception as e:
        print(f"❌ Error in video stitching: {str(e)}")
        # Fallback to simple ffmpeg concat if MoviePy fails
        return await stitch_videos_simple_fallback(video_paths, output_path)


async def stitch_videos_simple_fallback(video_paths: List[str], output_path: str) -> str:
    """Fallback simple video stitching using ffmpeg"""
    import subprocess

    print("⚠️ Using fallback ffmpeg concatenation...")

    # Create a temporary file list for ffmpeg concat
    concat_file = f"/tmp/concat_list_{int(datetime.now().timestamp())}.txt"

    try:
        # Write file list
        with open(concat_file, "w") as f:
            for video_path in video_paths:
                f.write(f"file '{video_path}'\n")

        # Run ffmpeg concat
        cmd = [
            "ffmpeg",
            "-y",  # -y to overwrite output file
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            concat_file,
            "-c",
            "copy",  # Copy without re-encoding for speed
            output_path,
        ]

        print(f"🔧 Running ffmpeg command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        if not os.path.exists(output_path):
            raise Exception("Output file was not created")

        return output_path

    except subprocess.CalledProcessError as e:
        error_msg = f"FFmpeg error: {e.stderr}"
        print(f"❌ {error_msg}")
        raise Exception(error_msg)
    except Exception as e:
        raise Exception(f"Video stitching error: {str(e)}")
    finally:
        # Clean up temp file
        if os.path.exists(concat_file):
            os.remove(concat_file)


def safe_progress_update(task_id: str, message: str):
    """Safely update RunPod progress without throwing exceptions"""
    try:
        runpod.serverless.progress_update({"id": task_id}, message)
    except Exception as e:
        print(f"⚠️ Progress update failed: {str(e)}")


async def update_task_in_db(
    task_id: str, progress: int, status: str = None, error_message: str = None, result: dict = None
):
    """Update task progress in database"""
    try:
        async for session in db_manager.get_session():
            query = select(TaskQueue).where(TaskQueue.task_id == task_id)
            db_result = await session.execute(query)
            task = db_result.scalars().first()
            
            if task:
                task.progress = progress
                if status:
                    task.status = status
                if error_message:
                    task.error_message = error_message
                if result:
                    task.result = result
                if status == "completed":
                    task.completed_at = datetime.utcnow()
                elif status == "processing" and not task.started_at:
                    task.started_at = datetime.utcnow()

                await session.commit()
                print(f"✅ Updated task {task_id}: progress={progress}, status={status}")
            break
    except Exception as e:
        print(f"❌ Failed to update task progress: {str(e)}")


async def generate_background_audio_if_needed(scene, org_id, task_id):
    """
    Generate background audio for a scene if it doesn't exist.
    Returns the S3 URL of the video with background audio, or None if generation fails.
    """
    try:
        print(f"🎵 Generating background audio for scene {scene.scene_number}")
        safe_progress_update(task_id, f"Generating background audio for scene {scene.scene_number}")

        # Get the video asset for this scene
        async for session in db_manager.get_session():
            video_query = (
                select(VideoAsset)
                .where(
                    and_(
                        VideoAsset.scene_id == scene.id,
                        VideoAsset.org_id == org_id,
                        VideoAsset.deleted_at.is_(None),
                    )
                )
                .order_by(desc(VideoAsset.created_at))
            )

            video_result = await session.execute(video_query)
            video_asset = video_result.scalars().first()
            
            if not video_asset or not video_asset.s3_url:
                print(f"❌ No video asset found for scene {scene.scene_number}")
                return None

            # Get video URL for background sound generation
            video_url = video_asset.s3_url
            if video_url.startswith("http") and "amazonaws.com/" in video_url:
                video_key = video_url.split("amazonaws.com/")[-1].split("?")[0]
            else:
                video_key = video_url

            # Generate presigned URL for video access
            s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
            video_presigned_url = s3_client.get_presigned_url(
                s3_bucket, video_key, timedelta(hours=2)
            )

            # Create AudioAsset entry first
            asset_id = str(uuid.uuid4())
            audio_asset = AudioAsset(
                org_id=org_id,
                asset_id=asset_id,
                s3_url="",  # Will be updated after generation
                local_path=None,
                source_type="background_sound",
                scene_id=scene.id,
                generation_status="in_progress",
            )
            session.add(audio_asset)
            await session.commit()

            break

        # Generate background sound
        generator = EnhancedBackgroundSoundGenerator()

        # Generate or use existing prompt
        scene_description = scene.description or scene.visual_description or "ambient scene"
        base_sound_prompt = generator.generate_base_sound_prompt(scene_description)
        unique_seed = abs(hash(str(scene.id))) % 1000000

        print(f"🎵 Using prompt: {base_sound_prompt}")

        result = generator.generate_background_sound_for_video_sync(
            video_path=video_presigned_url,
            base_sound_prompt=base_sound_prompt,
            scene_id=str(scene.id),
            asset_id=asset_id,
            seed=unique_seed,
        )

        if result.get("success"):
            # Update the AudioAsset with the results
            async for session in db_manager.get_session():
                audio_asset = await session.get(AudioAsset, asset_id)
                if audio_asset:
                    audio_asset.s3_url = result.get("s3_key", "")
                    audio_asset.generation_status = "completed"
                    audio_asset.generation_metadata = {
                        "sound_prompt": base_sound_prompt,
                        "duration": result.get("duration"),
                        "settings": result.get("settings", {}),
                        "file_size": result.get("file_size"),
                    }
                    await session.commit()

                    print(f"✅ Background audio generated for scene {scene.scene_number}")
                    return result.get("s3_key")
                break
        else:
            print(
                f"❌ Background audio generation failed for scene {scene.scene_number}: {result.get('error', 'Unknown error')}"
            )
            # Update status to failed
            async for session in db_manager.get_session():
                audio_asset = await session.get(AudioAsset, asset_id)
                if audio_asset:
                    audio_asset.generation_status = "failed"
                    await session.commit()
                break
            return None

    except Exception as e:
        print(f"❌ Error generating background audio for scene {scene.scene_number}: {str(e)}")
        return None


async def process_video_stitching(
    script_id: str,
    org_id: str,
    task_id: str,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    auto_audio_stitch: bool = True,
) -> Dict[str, Any]:
    """Main video stitching process"""

    try:
        print(f"🎬 Starting video stitching for script: {script_id}")

        # Update task status
        await update_task_in_db(task_id, 10, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Initializing video stitching...")

        video_paths = []
        scenes_data = []
        s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")

        # Get data from database and build background_sound_results structure (matching local implementation)
        background_sound_results = []

        async for session in db_manager.get_session():
            # Get script
            script_query = select(Script).where(
                and_(Script.id == script_id, Script.org_id == org_id)
            )
            script_result = await session.execute(script_query)
            script = script_result.scalars().first()
            
            if not script:
                raise Exception(f"Script {script_id} not found")

            # Get scenes
            scenes_query = (
                select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
            )
            scenes_result = await session.execute(scenes_query)
            scenes = scenes_result.scalars().all()

            if not scenes:
                raise Exception("No scenes found for script")

            print(f"📋 Found {len(scenes)} scenes to process")
            await update_task_in_db(task_id, 20, "processing")
            runpod.serverless.progress_update({"id": task_id}, f"Found {len(scenes)} scenes")

            # Build background_sound_results structure (matching local implementation)
            for i, scene in enumerate(scenes):
                # Try to find video with background audio first (matching local implementation)
                video_query = (
                    select(VideoAsset)
                    .where(
                        and_(
                            VideoAsset.scene_id == scene.id,
                            VideoAsset.org_id == org_id,
                            VideoAsset.deleted_at.is_(None),
                        )
                    )
                    .order_by(desc(VideoAsset.created_at))
                )

                video_result = await session.execute(video_query)
                video_asset = video_result.scalars().first()

                if video_asset:
                    # Convert S3 key to presigned URL (matching local implementation)
                    video_url = video_asset.s3_url
                    print(f"🎬 Found video asset for scene {scene.scene_number}: {video_url}")

                    if not video_url.startswith(("http://", "https://")):
                        # S3 key - convert to presigned URL
                        try:
                            video_url = s3_client.get_presigned_url(
                                bucket=s3_bucket,
                                key=video_asset.s3_url,
                                ttl=timedelta(hours=2),  # 2 hours should be enough for processing
                            )
                            print(
                                f"Generated presigned URL for scene {scene.scene_number}: {video_asset.s3_url}"
                            )
                        except Exception as e:
                            print(f"Failed to generate presigned URL for {video_asset.s3_url}: {e}")
                            video_url = video_asset.s3_url  # Fallback to original

                    # Create background sound result format expected by VideoStitcher (matching local)
                    scene_data = {
                        "scene_id": str(scene.id),
                        "scene_number": scene.scene_number,
                        "success": True,
                        "video_with_audio_url": video_url,
                        "local_path": video_asset.local_path,
                        "asset_id": video_asset.asset_id,
                    }
                    background_sound_results.append(scene_data)
                    scenes_data.append(scene)

                    print(f"✅ Added scene {scene.scene_number} to background_sound_results")
                else:
                    print(f"❌ No video asset found for scene {scene.scene_number}")
                    background_sound_results.append(
                        {
                            "scene_id": str(scene.id),
                            "scene_number": scene.scene_number,
                            "success": False,
                            "error": "No video asset found",
                        }
                    )

                # Update progress
                progress = 20 + (i + 1) * 30 // len(scenes)
                await update_task_in_db(task_id, progress, "processing")
                runpod.serverless.progress_update(
                    {"id": task_id}, f"Processed {i+1}/{len(scenes)} scenes"
                )

            break  # Exit session

        # Validate background_sound_results
        if not any(result["success"] for result in background_sound_results):
            raise Exception("No valid video assets found for any scenes in this script")

        # Get background music (matching local implementation)
        additional_background_music = None
        async for session in db_manager.get_session():
            music_query = (
                select(AudioAsset)
                .where(
                    and_(
                        AudioAsset.script_id == script_id,
                        AudioAsset.source_type.in_(["background_music", "user_uploaded"]),
                        AudioAsset.deleted_at.is_(None),
                    )
                )
                .order_by(desc(AudioAsset.created_at))
            )

            music_result = await session.execute(music_query)
            background_music = music_result.scalars().first()

            if background_music:
                music_url = background_music.s3_url
                if not music_url.startswith(("http://", "https://")):
                    # S3 key - convert to presigned URL
                    try:
                        additional_background_music = s3_client.get_presigned_url(
                            bucket=s3_bucket, key=background_music.s3_url, ttl=timedelta(hours=2)
                        )
                        print(
                            f"Generated presigned URL for background music: {background_music.s3_url}"
                        )
                    except Exception as e:
                        print(
                            f"Failed to generate presigned URL for background music {background_music.s3_url}: {e}"
                        )
                        additional_background_music = music_url  # Fallback to original
                else:
                    additional_background_music = music_url
            break

        # Step 2: Video stitching using VideoStitcher service (matching local implementation)
        print("🎞️ Starting video stitching with VideoStitcher service...")
        await update_task_in_db(task_id, 50, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Stitching videos together...")

        # Call the video stitcher service (matching local implementation)
        stitch_result = video_stitcher.stitch_videos(
            background_sound_results=background_sound_results,
            script_id=script_id,
            org_id=org_id,
            additional_background_music=additional_background_music,
            session=None,  # Pass None since we're not in an active session
        )

        if not stitch_result.get("success"):
            raise Exception(
                f"Video stitching failed: {stitch_result.get('error', 'Unknown error')}"
            )

        print(f"✅ Video stitching completed: {stitch_result.get('s3_url')}")
        s3_url = stitch_result.get("s3_url")

        await update_task_in_db(task_id, 60, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Uploaded stitched video")

        # Save to database
        async for session in db_manager.get_session():
            import uuid

            asset_id = str(uuid.uuid4())
            video_asset = VideoAsset(
                asset_id=asset_id,
                org_id=org_id,
                s3_url=s3_url,
                generation_method="rendered",
                script_id=script_id,
                local_path=None,
            )
            session.add(video_asset)
            await session.commit()
            break

        result_data = {
            "video_stitching": {
                "status": "completed",
                "video_url": s3_url,
                "scenes_count": len(scenes_data),
            }
        }

        # Step 3: Audio stitching (if enabled) using local implementation approach
        if auto_audio_stitch:
            print("🎵 Starting audio stitching using local approach...")
            await update_task_in_db(task_id, 70, "processing")
            runpod.serverless.progress_update({"id": task_id}, "Processing audio...")

            # Use the local audio stitching implementation
            async for session in db_manager.get_session():
                try:
                    # Import and use the local audio stitching function
                    from src.video_service.api.routes.video_stitching_old import (
                        perform_audio_stitching,
                    )

                    audio_result = await perform_audio_stitching(
                        script_id=script_id,
                        org_id=org_id,
                        enable_ducking=enable_ducking,
                        enable_ai_enhancement=enable_ai_enhancement,
                        session=session,
                    )

                    if audio_result.get("success"):
                        final_s3_url = audio_result.get("final_video_url") or audio_result.get(
                            "s3_url"
                        )
                        print(f"✅ Audio stitching completed: {final_s3_url}")

                        # Ensure final video is saved to final-videos with deterministic name
                        if final_s3_url:
                            try:
                                s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
                                # Derive the source key from URL or key
                                if (
                                    final_s3_url.startswith("http")
                                    and "amazonaws.com/" in final_s3_url
                                ):
                                    src_key = final_s3_url.split("amazonaws.com/")[-1].split("?")[0]
                                else:
                                    src_key = final_s3_url

                                dest_key = (
                                    f"Vidflux-Assets/final-videos/final_video_{script_id}.mp4"
                                )

                                # Copy within S3 if source and destination differ
                                if src_key != dest_key:
                                    try:
                                        s3_client.s3_client.copy(
                                            {"Bucket": s3_bucket, "Key": src_key},
                                            s3_bucket,
                                            dest_key,
                                        )
                                        print(
                                            f"✅ Copied final video in S3: {src_key} -> {dest_key}"
                                        )
                                        final_s3_url = dest_key
                                    except Exception as copy_err:
                                        print(
                                            f"⚠️ S3 copy failed ({src_key} -> {dest_key}), keeping original: {copy_err}"
                                        )
                                        # Fall back to original src_key if copy fails
                                        final_s3_url = src_key
                                else:
                                    final_s3_url = dest_key

                                # Update the database record to point to the final-videos path
                                async for session in db_manager.get_session():
                                    try:
                                        video_query = (
                                            select(VideoAsset)
                                            .where(
                                                and_(
                                                    VideoAsset.script_id == script_id,
                                                    VideoAsset.generation_method == "audio_mixed",
                                                    VideoAsset.org_id == org_id,
                                                )
                                            )
                                            .order_by(desc(VideoAsset.created_at))
                                            .limit(1)
                                        )
                                        video_result = await session.execute(video_query)
                                        video_asset = video_result.scalars().first()
                                        if video_asset:
                                            video_asset.s3_url = final_s3_url
                                            await session.commit()
                                            print(
                                                f"✅ Updated video asset S3 URL to: {final_s3_url}"
                                            )
                                        break
                                    except Exception as e:
                                        print(f"⚠️ Failed to update video asset S3 URL: {e}")
                            except Exception as e:
                                print(f"⚠️ Failed to ensure final video in final-videos path: {e}")

                        result_data["audio_stitching"] = {
                            "status": "completed",
                            "final_video_url": final_s3_url,
                            "details": audio_result,
                        }
                    else:
                        print(
                            f"❌ Audio stitching failed: {audio_result.get('error', 'Unknown error')}"
                        )
                        result_data["audio_stitching"] = {
                            "status": "failed",
                            "error": audio_result.get("error", "Unknown error"),
                        }

                    break

                except Exception as e:
                    print(f"❌ Audio stitching exception: {str(e)}")
                    result_data["audio_stitching"] = {"status": "failed", "error": str(e)}
                    break
        else:
            result_data["audio_stitching"] = {"status": "skipped"}

        # Final update with progress
        await update_task_in_db(task_id, 95, "processing")
        safe_progress_update(task_id, "Finalizing and cleaning up...")

        # Final completion update
        await update_task_in_db(task_id, 100, "completed", result=result_data)

        print("🎉 Processing completed successfully!")

        # Return minimal success result to avoid serialization issues
        print("📤 Returning minimal success result")
        return {
            "success": True,
            "status": "completed",
            "message": "Video processing completed successfully",
        }
    except Exception as e:
        error_msg = f"Processing failed: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")

        await update_task_in_db(task_id, 0, "failed", error_message=error_msg)
        return {"success": False, "status": "failed", "error": error_msg}


# Handler function for RunPod
async def handler(job):
    """RunPod serverless handler - async version"""
    try:
        print("🚀 VidFlux RunPod Handler Started")

        job_input = job["input"]

        # Extract parameters
        task_id = job_input.get("task_id")
        script_id = job_input.get("script_id")
        org_id = job_input.get("org_id")
        enable_ducking = job_input.get("enable_ducking", True)
        enable_ai_enhancement = job_input.get("enable_ai_enhancement", False)
        auto_audio_stitch = job_input.get("auto_audio_stitch", True)

        # Validate inputs
        if not all([task_id, script_id, org_id]):
            raise ValueError("Missing required parameters: task_id, script_id, org_id")
        
        print(f"📋 Processing - Task: {task_id}, Script: {script_id}")
        
        # Run the processing
        result = await process_video_stitching(
            script_id=script_id,
            org_id=org_id,
            task_id=task_id,
            enable_ducking=enable_ducking,
            enable_ai_enhancement=enable_ai_enhancement,
            auto_audio_stitch=auto_audio_stitch
        )
        
        print("✅ Handler completed successfully")
        return result

    except Exception as e:
        error_msg = f"Handler error: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")

        # Try to update database with error
        try:
            asyncio.run(update_task_in_db(task_id, 0, "failed", error_message=error_msg))
        except Exception as db_error:
            print(f"⚠️ Failed to update database with error: {str(db_error)}")

        # Return consistent error format
        return {"success": False, "status": "failed", "error": str(e), "task_id": task_id}

# Start the serverless function

if __name__ == '__main__':
    print("🎬 Starting VidFlux RunPod Serverless Worker...")
    runpod.serverless.start({"handler": handler})
