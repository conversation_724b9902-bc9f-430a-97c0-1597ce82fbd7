#!/bin/bash

# VidFlux Environment Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to show usage
show_usage() {
    print_header "VidFlux Environment Management"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  local-start     Start local development environment with Redis"
    echo "  local-stop      Stop local development environment"
    echo "  local-logs      Show logs for local environment"
    echo "  prod-start      Start production environment (for EC2)"
    echo "  prod-stop       Stop production environment"
    echo "  prod-logs       Show logs for production environment"
    echo "  status          Show status of all containers"
    echo "  clean           Clean up all containers and volumes"
    echo ""
}

# Function to start local development environment
start_local() {
    print_header "🚀 Starting Local Development Environment"
    print_status "Using local Redis for Celery broker..."
    
    # Build and start services
    docker-compose up -d --build
    
    print_status "Waiting for services to be ready..."
    sleep 5
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_status "✅ Local environment started successfully!"
        print_status "API: http://localhost:8000"
        print_status "Redis: localhost:6379"
        echo ""
        print_status "Services running:"
        docker-compose ps
    else
        print_error "❌ Failed to start local environment"
        docker-compose logs
        exit 1
    fi
}

# Function to stop local development environment
stop_local() {
    print_header "🛑 Stopping Local Development Environment"
    docker-compose down
    print_status "✅ Local environment stopped"
}

# Function to show local logs
logs_local() {
    print_header "📋 Local Environment Logs"
    docker-compose logs -f
}

# Function to start production environment
start_prod() {
    print_header "🚀 Starting Production Environment"
    print_status "Using PostgreSQL for Celery broker..."
    
    # Build and start services
    docker-compose -f docker-compose.production.yml up -d --build
    
    print_status "Waiting for services to be ready..."
    sleep 5
    
    # Check if services are running
    if docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
        print_status "✅ Production environment started successfully!"
        print_status "API: http://localhost:8000"
        echo ""
        print_status "Services running:"
        docker-compose -f docker-compose.production.yml ps
    else
        print_error "❌ Failed to start production environment"
        docker-compose -f docker-compose.production.yml logs
        exit 1
    fi
}

# Function to stop production environment
stop_prod() {
    print_header "🛑 Stopping Production Environment"
    docker-compose -f docker-compose.production.yml down
    print_status "✅ Production environment stopped"
}

# Function to show production logs
logs_prod() {
    print_header "📋 Production Environment Logs"
    docker-compose -f docker-compose.production.yml logs -f
}

# Function to show status
show_status() {
    print_header "📊 Container Status"
    echo ""
    print_status "Local Development Containers:"
    docker-compose ps 2>/dev/null || echo "No local containers running"
    echo ""
    print_status "Production Containers:"
    docker-compose -f docker-compose.production.yml ps 2>/dev/null || echo "No production containers running"
}

# Function to clean up
clean_all() {
    print_header "🧹 Cleaning Up All Containers and Volumes"
    print_warning "This will remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v --remove-orphans 2>/dev/null || true
        docker-compose -f docker-compose.production.yml down -v --remove-orphans 2>/dev/null || true
        print_status "✅ Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Main script logic
case "${1:-}" in
    "local-start")
        start_local
        ;;
    "local-stop")
        stop_local
        ;;
    "local-logs")
        logs_local
        ;;
    "prod-start")
        start_prod
        ;;
    "prod-stop")
        stop_prod
        ;;
    "prod-logs")
        logs_prod
        ;;
    "status")
        show_status
        ;;
    "clean")
        clean_all
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
