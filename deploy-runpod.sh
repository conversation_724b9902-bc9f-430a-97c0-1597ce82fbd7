#!/bin/bash

# VidFlux RunPod Deployment Script
# This script helps you build and deploy the video stitching service to RunPod

set -e  # Exit on any error

echo "🚀 VidFlux RunPod Deployment Script"
echo "==================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if required environment variables are set
if [ -z "$DOCKER_USERNAME" ]; then
    echo "⚠️  DOCKER_USERNAME not set. Please set your Docker Hub username:"
    read -p "Docker Username: " DOCKER_USERNAME
    export DOCKER_USERNAME
fi

# Default values
IMAGE_NAME="${DOCKER_USERNAME}/vidflux-runpod"
TAG="latest"
PLATFORM="linux/amd64"

echo ""
echo "📦 Building Docker image..."
echo "Image: $IMAGE_NAME:$TAG"
echo "Platform: $PLATFORM"
echo ""

# Build the Docker image
docker build \
    --platform $PLATFORM \
    --tag $IMAGE_NAME:$TAG \
    --file Dockerfile.runpod \
    .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully!"
else
    echo "❌ Docker build failed!"
    exit 1
fi

echo ""
echo "🔑 Logging into Docker Hub..."
docker login

if [ $? -eq 0 ]; then
    echo "✅ Docker login successful!"
else
    echo "❌ Docker login failed!"
    exit 1
fi

echo ""
echo "📤 Pushing image to Docker Hub..."
docker push $IMAGE_NAME:$TAG

if [ $? -eq 0 ]; then
    echo "✅ Image pushed successfully!"
else
    echo "❌ Image push failed!"
    exit 1
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Go to RunPod console: https://www.runpod.io/console/serverless"
echo "2. Click 'New Endpoint'"
echo "3. Select 'Docker Image' as source"
echo "4. Enter image URL: docker.io/$IMAGE_NAME:$TAG"
echo "5. Configure worker settings (recommend 16GB+ GPU)"
echo "6. Set environment variables:"
echo "   - DATABASE_URL (your PostgreSQL connection string)"
echo "   - AWS_S3_BUCKET (your S3 bucket name)"
echo "   - AWS_ACCESS_KEY_ID (your AWS access key)"
echo "   - AWS_SECRET_ACCESS_KEY (your AWS secret key)"
echo "   - AWS_DEFAULT_REGION (your AWS region)"
echo "7. Create the endpoint and copy the endpoint ID"
echo "8. Set RUNPOD_API_KEY and RUNPOD_ENDPOINT_ID in your FastAPI backend"
echo ""
echo "🔧 Environment variables needed in RunPod:"
echo "   DATABASE_URL=postgresql+asyncpg://user:pass@host:port/dbname"
echo "   AWS_S3_BUCKET=your-bucket-name"
echo "   AWS_ACCESS_KEY_ID=your-aws-key"
echo "   AWS_SECRET_ACCESS_KEY=your-aws-secret"
echo "   AWS_DEFAULT_REGION=us-east-2"
echo ""
echo "📖 For more information, see: https://docs.runpod.io/serverless/workers/handlers"
