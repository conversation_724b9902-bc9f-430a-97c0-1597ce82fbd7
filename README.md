# 🎬 VidFlux Backend

<div align="center">

![VidFlux Logo](https://img.shields.io/badge/VidFlux-AI%20Video%20Generation-blue?style=for-the-badge&logo=video)

**🚀 AI-Powered Video Generation Platform Backend**

[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.0-009688?style=flat&logo=fastapi)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3.10+-3776AB?style=flat&logo=python)](https://python.org)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15-4169E1?style=flat&logo=postgresql)](https://postgresql.org)
[![Redis](https://img.shields.io/badge/Redis-7-DC382D?style=flat&logo=redis)](https://redis.io)
[![Docker](https://img.shields.io/badge/Docker-Containerized-2496ED?style=flat&logo=docker)](https://docker.com)

</div>

## 📖 Table of Contents

- [🎯 Overview](#-overview)
- [✨ Features](#-features)
- [🚀 RunPod Integration](#-runpod-integration)
- [🏗️ Architecture](#️-architecture)
- [🔧 Technology Stack](#-technology-stack)
- [📁 Project Structure](#-project-structure)
- [🚀 Quick Start](#-quick-start)
- [🐳 Docker Setup](#-docker-setup)
- [⚙️ Configuration](#️-configuration)
- [🔗 API Services](#-api-services)
- [📊 Database Schema](#-database-schema)
- [🎨 AI Integrations](#-ai-integrations)
- [🔒 Authentication & Security](#-authentication--security)
- [📤 File Storage & Management](#-file-storage--management)
- [⚡ Background Processing](#-background-processing)
- [📋 API Documentation](#-api-documentation)
- [🧪 Testing](#-testing)
- [🚀 Deployment](#-deployment)
- [🔍 Monitoring & Logging](#-monitoring--logging)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)

## 🎯 Overview

VidFlux Backend is a comprehensive AI-powered video generation platform that transforms text scripts into professional-quality videos through advanced artificial intelligence technologies. The platform orchestrates multiple AI services to generate scenes, images, audio, and videos, culminating in polished final video productions.

### 🌟 Key Capabilities

- **🤖 AI Script Generation**: Intelligent script creation using Google Gemini
- **🎨 Image Generation**: High-quality visuals via FLUX AI
- **🎵 Audio Synthesis**: Background music and voiceovers with Stable Audio & TTS
- **🎬 Video Creation**: Professional video generation using LTX Video
- **✂️ Automated Editing**: Scene stitching and post-production
- **🎭 Visual Overlays**: Dynamic text and image overlays
- **☁️ Cloud Storage**: Seamless AWS S3 integration
- **🔄 Async Processing**: Background task processing for optimal performance

## ✨ Features

### 🎬 Content Generation
- **Script-to-Video Pipeline**: Complete automated workflow from concept to final video
- **Multi-Scene Management**: Organize complex narratives with scene-based structure
- **AI-Powered Creativity**: Leverage cutting-edge AI for content enhancement
- **Flexible Output Formats**: Support for various video formats and resolutions

### 🎨 Visual & Audio Production
- **Dynamic Image Generation**: Create contextual visuals for each scene
- **Professional Audio**: Background music, sound effects, and voiceovers
- **Text Overlays**: Animated titles, captions, and informational text
- **Image Overlays**: Logos, watermarks, and brand elements

### 🔧 Platform Features
- **RESTful API**: Comprehensive FastAPI-based REST interface
- **Real-time Processing**: WebSocket support for live updates
- **Background Tasks**: Asynchronous processing with Celery
- **Scalable Architecture**: Microservices-based design
- **Multi-tenant Support**: Organization-based user management

## 🏗️ Architecture

VidFlux follows a microservices architecture with specialized services for different aspects of video production:

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 FastAPI Gateway                       │
├─────────────────────────────────────────────────────────────┤
│  🔐 Auth    📝 Script   🎨 Image   🎵 Audio   🎬 Video      │
│  Service    Service     Service    Service    Service       │
├─────────────────────────────────────────────────────────────┤
│           🎭 Overlay Services (Text & Image)                │
├─────────────────────────────────────────────────────────────┤
│     ⚡ Background Task Processing (Celery Workers)          │
├─────────────────────────────────────────────────────────────┤
│  🗄️ PostgreSQL  |  🔄 Redis  |  ☁️ AWS S3  |  🤖 AI APIs   │
└─────────────────────────────────────────────────────────────┘
```

### 🔄 Video Generation Workflow

```mermaid
graph TD
    A[📝 Script Creation] --> B[🎬 Scene Generation]
    B --> C[🎨 Image Generation]
    B --> D[🎵 Audio Generation]
    B --> E[🎬 Video Generation]
    C --> F[🎭 Image Overlays]
    D --> G[🎵 Audio Mixing]
    E --> H[📹 Scene Videos]
    F --> I[✂️ Video Stitching]
    G --> I
    H --> I
    I --> J[🎉 Final Video]
```

## 🔧 Technology Stack

### 🚀 Core Framework
- **FastAPI** - High-performance async web framework
- **Python 3.10+** - Modern Python with async/await support
- **Pydantic V2** - Data validation and settings management
- **SQLAlchemy 2.0** - Async ORM with relationship management

### 🗄️ Data & Storage
- **PostgreSQL 15** - Primary database with JSONB support
- **Redis 7** - Caching and task queue backend
- **AWS S3** - Object storage for media files
- **Alembic** - Database migration management

### ⚡ Processing & Tasks
- **Celery** - Distributed task queue for background processing
- **Asyncio** - Asynchronous I/O operations
- **HTTPX** - Modern async HTTP client

### 🤖 AI & Media Processing
- **Google Gemini** - Advanced language model for script generation
- **FLUX AI** - High-quality image generation
- **Stable Audio** - AI-powered audio synthesis
- **LTX Video** - Professional video generation
- **MoviePy** - Video editing and processing
- **OpenCV** - Computer vision and image processing

### 🔒 Security & Authentication
- **JWT** - JSON Web Tokens for stateless authentication
- **Passlib** - Password hashing with bcrypt
- **OAuth 2.0** - Industry-standard authorization

### 🐳 DevOps & Deployment
- **Docker** - Containerization platform
- **Docker Compose** - Multi-container orchestration
- **Uvicorn** - ASGI server for production deployment

## 🚀 RunPod Integration

VidFlux now supports **RunPod** for high-performance video processing, offloading heavy computational work from your local server to powerful GPU instances in the cloud.

### 🌟 Benefits
- **⚡ Performance**: GPU-accelerated video stitching and audio mixing
- **📈 Scalability**: Automatic scaling based on processing demand
- **💰 Cost-effective**: Pay only for actual processing time
- **🛡️ Reliability**: Professional cloud infrastructure

### 🔄 Processing Modes
- **Local Processing**: Traditional server-side processing (fallback)
- **RunPod Processing**: Cloud-based GPU processing (recommended)

### 📖 Quick Setup
1. **Deploy to RunPod**: Use `./deploy-runpod.sh` to build and push Docker image
2. **Create Endpoint**: Set up RunPod serverless endpoint with your image
3. **Configure Backend**: Set `RUNPOD_API_KEY` and `RUNPOD_ENDPOINT_ID`
4. **Start Processing**: Video stitching automatically uses RunPod when configured

For detailed setup instructions, see **[RUNPOD_SETUP.md](./RUNPOD_SETUP.md)**

## 📁 Project Structure

```
vidflux_backend/
├── 📋 README.md                     # This comprehensive documentation
├── 🐳 Dockerfile                    # Docker container configuration
├── 🐳 docker-compose.yml            # Multi-service orchestration
├── 📦 pyproject.toml                # Python project configuration
├── 🔧 requirements.txt              # Python dependencies
├── 🚀 main.py                       # FastAPI application entry point
├── 🗄️ init_db.py                    # Database initialization script
├── 📊 vidflux.db                    # SQLite database (development)
├── 🏃‍♂️ Scripts/
│   ├── run_local.sh                 # Local development startup
│   ├── run_docker_local.sh          # Docker local environment
│   └── run_dev.sh                   # Development mode runner
├── 📂 output/                       # Generated media files
│   ├── 🎵 audio/                    # Generated audio files
│   ├── 🎼 background_music/         # Background music tracks
│   ├── 🔊 background_sounds/        # Ambient sound effects
│   ├── 🖼️ images/                   # Generated images
│   ├── 🎬 videos/                   # Scene videos
│   ├── ✂️ stitched_videos/          # Final combined videos
│   └── 🎭 overlays/                 # Text and image overlays
├── 📚 docs/                         # API documentation
│   ├── 🔐 auth_service_api_documentation.md
│   ├── 📝 script_service_api_documentation.md
│   ├── 🎨 image_service_api_documentation.md
│   ├── 🎵 audio_service_api_documentation.md
│   ├── 🎬 video_service_api_documentation.md
│   └── 📋 api_documentation_index.md
├── 📝 logs/                         # Application logs
└── 🏗️ src/                          # Source code directory
    ├── 🔐 auth_service/             # Authentication & user management
    │   ├── 🚪 api/routes/           # Auth API endpoints
    │   ├── 🛡️ core/                 # Auth business logic
    │   └── 📊 schemas/              # Auth data models
    ├── 📝 script_service/           # Script generation & management
    │   ├── 🚪 api/routes/           # Script API endpoints
    │   ├── 🤖 core/                 # Script generation logic
    │   └── 📊 schemas/              # Script data models
    ├── 🎨 image_service/            # AI image generation
    │   ├── 🚪 api/routes/           # Image API endpoints
    │   ├── 🎨 core/                 # Image generation logic
    │   └── 📊 schemas/              # Image data models
    ├── 🎵 audio_service/            # Audio generation & processing
    │   ├── 🚪 api/routes/           # Audio API endpoints
    │   ├── 🎼 core/                 # Audio generation logic
    │   └── 📊 schemas/              # Audio data models
    ├── 🎬 video_service/            # Video generation & stitching
    │   ├── 🚪 api/routes/           # Video API endpoints
    │   ├── 🎬 core/                 # Video generation logic
    │   └── 📊 schemas/              # Video data models
    ├── 🎭 image_overlay_service/    # Image overlay processing
    ├── 📝 text_overlay_service/     # Text overlay processing
    ├── ⚡ worker/                   # Background task workers
    │   ├── 🔄 celery_app.py         # Celery configuration
    │   └── 📋 tasks/                # Background task definitions
    └── 🤝 shared/                   # Shared utilities & configurations
        ├── ⚙️ config/               # Application configuration
        │   ├── 🔧 settings.py       # Environment settings
        │   └── 🗄️ database.py       # Database configuration
        ├── 📊 models/               # Database models
        │   └── database_models.py   # SQLAlchemy models
        ├── 📋 schemas/              # Pydantic schemas
        ├── 🛠️ core/                 # Core utilities
        │   ├── 🔒 security.py       # Security utilities
        │   ├── 🛡️ middleware.py     # Custom middleware
        │   └── 🔧 utils.py          # Helper functions
        └── 📁 static/               # Static files
```

## 🚀 Quick Start

### 📋 Prerequisites

- **Python 3.10+** 🐍
- **PostgreSQL 15+** 🗄️
- **Redis 7+** 🔄
- **Docker & Docker Compose** 🐳 (optional)
- **Git** 📦

### 🔧 Local Development Setup

1. **📥 Clone the Repository**
   ```bash
   git clone <repository-url>
   cd vidflux_backend
   ```

2. **🐍 Create Python Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **📦 Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **⚙️ Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **🗄️ Database Setup**
   ```bash
   python init_db.py
   ```

6. **🚀 Start the Application**
   ```bash
   python main.py
   # Or use the convenience script
   ./run_local.sh
   ```

7. **🌐 Access the API**
   - **API Documentation**: http://localhost:8000/docs
   - **Alternative Docs**: http://localhost:8000/redoc
   - **Health Check**: http://localhost:8000/

## 🐳 Docker Setup

### 🚀 Quick Docker Start

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 🔧 Development Mode

```bash
# Start with development configuration
docker-compose -f docker-compose.dev.yml up -d

# Rebuild and start
docker-compose up --build
```

### 📊 Service Management

```bash
# Scale Celery workers
docker-compose up --scale celery_worker=3

# Access container shell
docker-compose exec app bash

# View specific service logs
docker-compose logs -f app
docker-compose logs -f celery_worker
```

## ⚙️ Configuration

### 🔐 Environment Variables

Create a `.env` file with the following configuration:

```env
# 🚀 Application Settings
APP_NAME=VidFlux Backend
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-super-secret-key-here

# 🗄️ Database Configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/vidflux
DATABASE_URL_SYNC=postgresql://user:password@localhost:5432/vidflux

# 🔄 Redis Configuration
REDIS_URL=redis://localhost:6379/0

# ⚡ Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 🤖 AI Service Keys
GEMINI_API_KEY=your-gemini-api-key
FLUX_API_KEY=your-flux-api-key
STABLE_AUDIO_API_KEY=your-stable-audio-key
LTX_VIDEO_API_KEY=your-ltx-video-key

# ☁️ AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket

# 🔒 JWT Configuration
JWT_SECRET_KEY=your-jwt-secret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
```

### 📊 Database Configuration

The application uses PostgreSQL with async SQLAlchemy. Database models are defined in `src/shared/models/database_models.py`:

```python
# Key database entities
- 🏢 Organizations: Multi-tenant organization management
- 👤 Users: User accounts with authentication
- 📝 Scripts: Video project scripts
- 🎬 Scenes: Individual scenes within scripts
- 🎨 ImageAssets: Generated images for scenes
- 🎵 AudioAssets: Generated audio content
- 🎬 VideoAssets: Generated video content
- 📋 TaskQueue: Background task tracking
```

## 🔗 API Services

### 🔐 Authentication Service (`/auth`)

Handles user registration, authentication, and profile management:

- **POST /auth/register** - User registration with OTP verification
- **POST /auth/verify-otp** - Email verification
- **POST /auth/login** - User authentication
- **POST /auth/refresh** - Token refresh
- **GET /auth/profile** - User profile retrieval

**🔑 Features:**
- JWT-based authentication
- Email OTP verification
- Secure password hashing
- Token refresh mechanism

### 📝 Script Service (`/script`)

Manages script creation and scene generation:

- **POST /script/generate** - AI-powered script generation
- **GET /script/{script_id}/scenes** - Retrieve script scenes

**🤖 AI Integration:**
- Google Gemini for intelligent script creation
- Scene breakdown and optimization
- Context-aware content generation

### 🎨 Image Service (`/images`)

Handles AI-powered image generation:

- **POST /images/generate** - Generate images for scenes
- **GET /images/{scene_id}** - Retrieve scene images
- **PUT /images/regenerate/{scene_id}** - Regenerate images
- **GET /images/status/{script_id}** - Check generation status
- **DELETE /images/{scene_id}** - Delete scene images

**🎨 Features:**
- FLUX AI integration for high-quality images
- Scene-specific visual generation
- Batch processing capabilities
- Image regeneration and optimization

### 🎵 Audio Service (`/audio`)

Comprehensive audio generation and management:

- **POST /audio/background-music** - Generate background music
- **POST /audio/background-sounds** - Create ambient sounds
- **POST /audio/voiceover** - Text-to-speech conversion
- **GET /audio/{scene_id}** - Retrieve scene audio
- **PUT /audio/regenerate/{scene_id}** - Regenerate audio
- **GET /audio/status/{script_id}** - Check audio status
- **POST /audio/stitch-background** - Combine background audio
- **POST /audio/stitch-voiceover** - Merge voiceover audio
- **POST /audio/stitch-combined** - Complete audio mixing

**🎼 Features:**
- Stable Audio for music generation
- Advanced TTS for voiceovers
- Multi-track audio mixing
- Professional audio processing

### 🎬 Video Service (`/videos`)

Professional video generation and editing:

- **POST /videos/generate** - Generate videos for scenes
- **GET /videos/{scene_id}** - Retrieve scene videos
- **PUT /videos/update-prompt/{scene_id}** - Update generation prompts
- **GET /videos/status/{script_id}** - Check video status
- **POST /stitching/stitch** - Combine scene videos

**🎬 Features:**
- LTX Video for high-quality generation
- Scene-based video creation
- Professional video stitching
- Multiple format support

### 🎭 Overlay Services

**Text Overlay Service (`/text-overlay`)**
- **POST /text-overlay/add** - Add text overlays to videos

**Image Overlay Service (`/image-overlay`)**
- **POST /image-overlay/add** - Add logos and watermarks
- **PUT /image-overlay/update** - Update overlay properties
- **DELETE /image-overlay/remove** - Remove overlays

## 📊 Database Schema

### 🏗️ Core Entities

```sql
-- 🏢 Organizations (Multi-tenancy)
organizations {
  org_id: VARCHAR PRIMARY KEY
  org_name: VARCHAR NOT NULL
  created_at: TIMESTAMP
  updated_at: TIMESTAMP
}

-- 👤 Users (Authentication)
users {
  org_id: VARCHAR FK
  user_id: UUID PRIMARY KEY
  user_email: VARCHAR UNIQUE
  username: VARCHAR
  password_hash: VARCHAR
  is_active: BOOLEAN
  is_verified: BOOLEAN
}

-- 📝 Scripts (Video Projects)
scripts {
  org_id: VARCHAR FK
  script_id: UUID PRIMARY KEY
  user_id: UUID FK
  title: VARCHAR
  description: TEXT
  content: JSONB
  status: ENUM
}

-- 🎬 Scenes (Script Components)
scenes {
  org_id: VARCHAR FK
  scene_id: UUID PRIMARY KEY
  script_id: UUID FK
  scene_number: INTEGER
  visual_description: TEXT
  audio_description: TEXT
  duration: INTEGER
}

-- 🎨 Media Assets
image_assets {
  asset_id: UUID PRIMARY KEY
  scene_id: UUID FK
  s3_key: VARCHAR
  file_size: BIGINT
  status: ENUM
  generation_metadata: JSONB
}

audio_assets {
  asset_id: UUID PRIMARY KEY
  scene_id: UUID FK
  audio_source: ENUM
  s3_key: VARCHAR
  duration: FLOAT
  file_size: BIGINT
}

video_assets {
  asset_id: UUID PRIMARY KEY
  scene_id: UUID FK
  s3_key: VARCHAR
  duration: FLOAT
  resolution: VARCHAR
  file_size: BIGINT
  generation_method: ENUM
}
```

## 🎨 AI Integrations

### 🤖 Google Gemini (Script Generation)

```python
# Script generation with context awareness
gemini_service.generate_script(
    topic="Product Demo",
    style="Professional",
    duration=60,
    target_audience="Business Professionals"
)
```

**🌟 Features:**
- Context-aware script creation
- Multi-style support
- Scene optimization
- Content personalization

### 🎨 FLUX AI (Image Generation)

```python
# High-quality image generation
flux_service.generate_image(
    prompt="Modern office workspace with laptop",
    style="photorealistic",
    resolution="1920x1080",
    quality="high"
)
```

**🎨 Capabilities:**
- Photorealistic image generation
- Style consistency across scenes
- High-resolution output
- Batch processing

### 🎵 Stable Audio (Music & Sounds)

```python
# Background music generation
stable_audio.generate_music(
    genre="Corporate",
    mood="Uplifting",
    duration=120,
    tempo="Moderate"
)
```

**🎼 Features:**
- Genre-specific music creation
- Mood-based composition
- Custom duration support
- Professional audio quality

### 🎬 LTX Video (Video Generation)

```python
# Professional video generation
ltx_video.generate_video(
    visual_prompt="Office meeting presentation",
    duration=30,
    resolution="1080p",
    style="documentary"
)
```

**🎬 Capabilities:**
- High-definition video creation
- Style-consistent generation
- Scene-specific optimization
- Professional quality output

## 🔒 Authentication & Security

### 🛡️ JWT Authentication

```python
# Token structure
{
  "user_id": "uuid",
  "org_id": "string",
  "email": "<EMAIL>",
  "exp": timestamp,
  "iat": timestamp
}
```

### 🔐 Security Features

- **🔑 Password Hashing**: Bcrypt with salt
- **🛡️ JWT Tokens**: Stateless authentication
- **📧 Email Verification**: OTP-based verification
- **🔄 Token Refresh**: Secure token rotation
- **🚫 Rate Limiting**: API abuse prevention
- **🛡️ CORS**: Cross-origin request security
- **🔒 Input Validation**: Pydantic model validation

### 🚪 Authorization Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth Service
    participant D as Database
    participant E as Email Service

    U->>A: Register (email, password)
    A->>D: Create pending user
    A->>E: Send OTP email
    U->>A: Verify OTP
    A->>D: Activate user account
    A->>U: Return JWT tokens
    U->>A: Access protected endpoint
    A->>A: Validate JWT
    A->>U: Return requested data
```

## 📤 File Storage & Management

### ☁️ AWS S3 Integration

**📁 Storage Organization:**
```
s3://vidflux-bucket/
├── 🏢 {org_id}/
│   ├── 📝 scripts/{script_id}/
│   │   ├── 🎨 images/{scene_id}/
│   │   │   ├── image_001.jpg
│   │   │   └── image_002.jpg
│   │   ├── 🎵 audio/{scene_id}/
│   │   │   ├── background_music.wav
│   │   │   ├── background_sound.wav
│   │   │   └── voiceover.mp3
│   │   ├── 🎬 videos/{scene_id}/
│   │   │   └── scene_video.mp4
│   │   └── 🎬 final/
│   │       └── final_video.mp4
```

**🔧 Features:**
- **🔗 Presigned URLs**: Secure file access
- **📊 Metadata Storage**: File information tracking
- **🔄 Automatic Cleanup**: Storage optimization
- **📈 CDN Integration**: Fast content delivery
- **💾 Backup Strategy**: Data redundancy
- **🔒 Access Control**: IAM-based permissions

### 📂 File Management

```python
# S3 service integration
s3_service = S3Service()

# Upload with metadata
await s3_service.upload_file(
    file_path="local/path/video.mp4",
    s3_key="org_id/scripts/script_id/videos/scene_id/video.mp4",
    metadata={
        "content_type": "video/mp4",
        "duration": 30,
        "resolution": "1920x1080"
    }
)

# Generate presigned URL (2-hour expiry)
url = await s3_service.generate_presigned_url(s3_key)
```

## ⚡ Background Processing

### 🔄 Celery Task Queue

**⚙️ Configuration:**
```python
# Celery settings
CELERY_BROKER_URL = "redis://localhost:6379/0"
CELERY_RESULT_BACKEND = "redis://localhost:6379/0"
CELERY_WORKER_CONCURRENCY = 4
CELERY_TASK_ROUTES = {
    'video_generation.*': {'queue': 'video_generation'},
    'image_generation.*': {'queue': 'image_generation'},
    'audio_generation.*': {'queue': 'audio_generation'},
}
```

### 📋 Task Categories

**🎨 Image Generation Tasks**
```python
@celery_app.task(bind=True)
def generate_scene_images(self, scene_id: str, prompt: str):
    """Generate AI images for a scene"""
    # FLUX API integration
    # S3 upload
    # Database update
```

**🎵 Audio Generation Tasks**
```python
@celery_app.task(bind=True)
def generate_background_music(self, scene_id: str, mood: str):
    """Generate background music for a scene"""
    # Stable Audio API
    # Audio processing
    # S3 upload
```

**🎬 Video Generation Tasks**
```python
@celery_app.task(bind=True)
def generate_scene_video(self, scene_id: str, visual_prompt: str):
    """Generate video for a scene"""
    # LTX Video API
    # Video processing
    # Quality optimization
```

**✂️ Video Stitching Tasks**
```python
@celery_app.task(bind=True)
def stitch_final_video(self, script_id: str):
    """Combine scene videos into final production"""
    # Download scene videos
    # Audio synchronization
    # Transition effects
    # Final rendering
```

### 📊 Task Monitoring

```python
# Task status tracking
task_status = {
    "task_id": "uuid",
    "task_type": "video_generation",
    "status": "processing",
    "progress": 75,
    "estimated_completion": "2024-01-15T10:30:00Z",
    "result": null,
    "error": null
}
```

## 📋 API Documentation

### 📚 Interactive Documentation

- **🌐 Swagger UI**: http://localhost:8000/docs
- **📖 ReDoc**: http://localhost:8000/redoc
- **📄 OpenAPI Schema**: http://localhost:8000/openapi.json

### 📊 Documentation Structure

```
docs/
├── 📋 api_documentation_index.md      # Complete API overview
├── 🔐 auth_service_api_documentation.md
├── 📝 script_service_api_documentation.md
├── 🎨 image_service_api_documentation.md
├── 🎵 audio_service_api_documentation.md
├── 🎬 video_service_api_documentation.md
├── 🎭 image_overlay_service_api_documentation.md
└── 📝 text_overlay_service_api_documentation.md
```

### 🔍 API Examples

**🔐 Authentication Example**
```bash
# Register new user
curl -X POST "http://localhost:8000/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "first_name": "John",
    "last_name": "Doe"
  }'

# Login
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'
```

**📝 Script Generation Example**
```bash
# Generate script
curl -X POST "http://localhost:8000/script/generate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "Product Demo Video",
    "duration": 60,
    "style": "Professional",
    "target_audience": "Business Professionals"
  }'
```

## 🧪 Testing

### 🔬 Test Structure

```bash
tests/
├── 🔧 conftest.py                    # Test configuration
├── 🔐 test_auth_service.py           # Authentication tests
├── 📝 test_script_service.py         # Script generation tests
├── 🎨 test_image_service.py          # Image generation tests
├── 🎵 test_audio_service.py          # Audio generation tests
├── 🎬 test_video_service.py          # Video generation tests
├── 🧩 test_integration.py            # Integration tests
└── 📊 test_database.py               # Database tests
```

### 🚀 Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test category
pytest tests/test_auth_service.py -v

# Run integration tests
pytest tests/test_integration.py -v
```

### 🔍 Test Examples

```python
# Authentication test example
@pytest.mark.asyncio
async def test_user_registration():
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post("/auth/register", json={
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "first_name": "Test",
            "last_name": "User"
        })
        assert response.status_code == 201
        assert "pending_verification" in response.json()
```

## 🧹 Code Quality & Cleanup

### 🔍 Dead Code Detection with Vulture

VidFlux backend uses **Vulture** for automatic dead code detection to maintain a clean, efficient codebase.

**🔧 Setup & Usage:**

```bash
# Install Vulture (included in dev dependencies)
pip install vulture

# Run dead code detection
vulture src/ --min-confidence 80

# Use the provided scripts for convenience
# Linux/macOS:
./run_vulture.sh

# Windows PowerShell:
.\run_vulture.ps1
```

**📋 Configuration:**

Vulture is configured in `pyproject.toml`:
```toml
[tool.vulture]
min_confidence = 80
paths = ["src/"]
ignore_decorators = ["@validator", "@field_validator"]
ignore_names = ["cls"]
```

**📄 Whitelist File:**

The `.vulture_whitelist.py` file suppresses false positives, particularly:
- `cls` parameters in Pydantic validators (required by framework)
- Decorator-based usage patterns
- Dynamic imports and task registrations

**✅ Recent Cleanup Results:**

Our latest Vulture scan successfully removed:
- ❌ 15+ unused imports across services
- ❌ Redundant variables and parameters  
- ❌ Obsolete migration imports
- ❌ Unused utility functions
- ✅ Clean codebase with only 4 false positives (Pydantic validators)

**🔄 Regular Maintenance:**

```bash
# Add to CI/CD pipeline
vulture src/ .vulture_whitelist.py --min-confidence 80

# Run before commits
git add . && vulture src/ --min-confidence 80 && git commit
```

### 🎯 Code Quality Tools

**📏 Formatting & Linting:**
```bash
# Code formatting with Black
black src/ tests/

# Import sorting with isort  
isort src/ tests/

# Type checking with mypy
mypy src/

# Dead code detection with Vulture
vulture src/ --min-confidence 80
```

**🔧 Pre-commit Hooks:**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    hooks:
      - id: black
  - repo: https://github.com/jendrikseipp/vulture
    hooks:
      - id: vulture
        args: [--min-confidence=80]
```

## 🚀 Deployment

### 🐳 Production Docker Deployment

**📄 docker-compose.prod.yml**
```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - DEBUG=false
      - WORKERS=4
    ports:
      - "80:8000"
    restart: always

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - app
```

### ☁️ Cloud Deployment Options

**🚀 AWS Deployment**
- **ECS/Fargate**: Containerized deployment
- **RDS**: Managed PostgreSQL
- **ElastiCache**: Managed Redis
- **S3**: Media file storage
- **CloudFront**: CDN for media delivery
- **ALB**: Load balancing

**🔧 Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vidflux-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: vidflux-backend
  template:
    metadata:
      labels:
        app: vidflux-backend
    spec:
      containers:
      - name: app
        image: vidflux/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

### 🔧 Environment-Specific Configuration

**🚀 Production Settings**
```env
DEBUG=false
LOG_LEVEL=INFO
WORKERS=4
DATABASE_POOL_SIZE=20
CELERY_WORKER_CONCURRENCY=8
REDIS_MAX_CONNECTIONS=100
```

## 🔍 Monitoring & Logging

### 📊 Application Monitoring

**📈 Metrics Tracking**
```python
# Performance metrics
metrics = {
    "request_count": Counter,
    "request_duration": Histogram,
    "active_users": Gauge,
    "task_queue_size": Gauge,
    "generation_success_rate": Counter
}
```

### 📝 Logging Configuration

```python
# Structured logging with Loguru
logger.add(
    "logs/app.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}"
)
```

**📋 Log Categories**
- **🔐 Authentication**: Login attempts, token validation
- **🎬 Generation**: AI service interactions, processing status
- **💾 Database**: Query performance, connection pool status
- **🔄 Tasks**: Background job execution, errors
- **🌐 API**: Request/response logging, error tracking

### 🚨 Error Tracking

```python
# Comprehensive error handling
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "request_id": str(uuid.uuid4())}
    )
```

## 🤝 Contributing

### 📋 Development Guidelines

1. **🔀 Fork the Repository**
2. **🌟 Create Feature Branch**: `git checkout -b feature/amazing-feature`
3. **✅ Write Tests**: Ensure code coverage
4. **📝 Update Documentation**: Keep docs current
5. **🔍 Code Review**: Submit pull request

### 📏 Code Standards

```python
# Code formatting with Black
black src/ tests/

# Import sorting with isort
isort src/ tests/

# Type checking with mypy
mypy src/

# Linting with flake8
flake8 src/ tests/
```

### 📊 Commit Convention

```bash
# Commit message format
<type>(<scope>): <description>

# Examples
feat(auth): add JWT token refresh endpoint
fix(video): resolve stitching quality issues
docs(api): update authentication documentation
test(image): add FLUX integration tests
```

## 📊 Performance Optimization

### ⚡ Database Optimization

```python
# Query optimization
query = (
    select(Scene)
    .options(joinedload(Scene.image_assets))
    .options(joinedload(Scene.audio_assets))
    .where(Scene.script_id == script_id)
    .order_by(Scene.scene_number)
)
```

### 🔄 Caching Strategy

```python
# Redis caching for frequent queries
@cached(ttl=300)  # 5-minute cache
async def get_script_scenes(script_id: str):
    return await database.fetch_script_scenes(script_id)
```

### 📈 Scaling Considerations

- **🔄 Horizontal Scaling**: Multiple API instances
- **📊 Database Sharding**: Organization-based partitioning
- **⚡ Task Queue Scaling**: Dedicated worker pools
- **☁️ CDN Integration**: Global media delivery
- **🔧 Load Balancing**: Traffic distribution

## 🔐 Security Best Practices

### 🛡️ Security Checklist

- ✅ **Input Validation**: Pydantic model validation
- ✅ **SQL Injection Prevention**: SQLAlchemy ORM
- ✅ **XSS Protection**: Content Security Policy
- ✅ **CSRF Protection**: SameSite cookies
- ✅ **Rate Limiting**: Request throttling
- ✅ **HTTPS Enforcement**: TLS encryption
- ✅ **Secret Management**: Environment variables
- ✅ **Dependency Scanning**: Security audits

### 🔒 Data Protection

```python
# Sensitive data handling
class User(Base):
    password_hash = Column(String, nullable=False)  # Never store plain passwords
    email = Column(String, unique=True, nullable=False)  # PII protection
    
    def verify_password(self, password: str) -> bool:
        return pwd_context.verify(password, self.password_hash)
```

## 📚 Additional Resources

### 🔗 External Documentation

- **FastAPI**: https://fastapi.tiangolo.com/
- **SQLAlchemy**: https://docs.sqlalchemy.org/
- **Celery**: https://docs.celeryproject.org/
- **PostgreSQL**: https://www.postgresql.org/docs/
- **Redis**: https://redis.io/docs/
- **Docker**: https://docs.docker.com/

### 🤖 AI Service Documentation

- **Google Gemini**: https://ai.google.dev/docs
- **FLUX AI**: https://docs.flux.ai/
- **Stable Audio**: https://platform.stability.ai/docs
- **LTX Video**: https://docs.ltx.video/

### ☁️ AWS Services

- **S3**: https://docs.aws.amazon.com/s3/
- **RDS**: https://docs.aws.amazon.com/rds/
- **ElastiCache**: https://docs.aws.amazon.com/elasticache/

## 🆘 Troubleshooting

### 🔧 Common Issues

**🗄️ Database Connection Issues**
```bash
# Check PostgreSQL connection
psql -h localhost -U vidflux_user -d vidflux_local

# Verify environment variables
echo $DATABASE_URL
```

**🔄 Redis Connection Issues**
```bash
# Test Redis connection
redis-cli ping

# Check Redis memory usage
redis-cli info memory
```

**🤖 AI Service Integration Issues**
```bash
# Verify API keys
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models
```

**🐳 Docker Issues**
```bash
# Check container logs
docker-compose logs app

# Restart services
docker-compose restart

# Clean rebuild
docker-compose down -v
docker-compose up --build
```

## 📞 Support

### 🆘 Getting Help

- **📧 Email**: <EMAIL>
- **📖 Documentation**: Check the comprehensive API docs
- **🐛 Issues**: GitHub Issues for bug reports
- **💬 Discussions**: GitHub Discussions for questions

### 📊 Reporting Issues

When reporting issues, please include:

1. **📝 Description**: Clear problem description
2. **🔄 Steps to Reproduce**: Exact steps to trigger the issue
3. **🌍 Environment**: OS, Python version, dependencies
4. **📋 Logs**: Relevant error logs and stack traces
5. **📊 Expected vs Actual**: What should happen vs what happens

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

<div align="center">

**🎬 VidFlux - Transforming Ideas into Visual Stories**

Made with ❤️ by the VidFlux Team

[![GitHub](https://img.shields.io/badge/GitHub-VidFlux-181717?style=flat&logo=github)](https://github.com/vidflux)
[![Documentation](https://img.shields.io/badge/Docs-Latest-blue?style=flat&logo=gitbook)](http://localhost:8000/docs)
[![API Status](https://img.shields.io/badge/API-Online-success?style=flat&logo=fastapi)](http://localhost:8000/)

</div>
