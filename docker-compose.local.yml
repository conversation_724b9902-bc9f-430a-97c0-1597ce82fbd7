services:
  redis:
    image: redis:7-alpine
    container_name: vidflux_redis
    ports:
      - "6379:6379"
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  api:
    build: .
    image: vidflux-backend:latest
    container_name: vidflux_api
    command: uvicorn main:app --host 0.0.0.0 --port 8000
    env_file:
      - .env
    ports:
      - "8000:8000"
    depends_on:
      - redis
    restart: always

  worker:
    build: .
    image: vidflux-backend:latest
    container_name: vidflux_worker
    command: celery -A src.worker.celery_app worker --loglevel=info
    env_file:
      - .env
    depends_on:
      - redis
    restart: always

volumes:
  redis_data:
