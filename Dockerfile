# Production Dockerfile for VidFlux Backend (FastAPI + Celery)
FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set workdir
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install pipx for isolated installs (optional, for poetry)
RUN pip install --upgrade pip

# Copy dependency files
COPY pyproject.toml  ./

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

RUN if [ -f pyproject.toml ]; then \
      uv pip install --system --no-cache-dir .; \
    elif [ -f requirements.txt ]; then \
      uv pip install --system --no-cache-dir -r requirements.txt; \
    fi

# Copy app code
COPY ./src ./src
COPY ./main.py ./
COPY ./init_db.py ./
COPY ./init.sql ./

# Expose port for FastAPI
EXPOSE 8000

# Default command (can be overridden for Celery)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

# To run Celery worker, override CMD:
# docker run ... celery -A src.worker.celery_app worker --loglevel=info 