"""
Scene processor for enhanced professional advertisement script format
Parses the professional script format and extracts scenes with proper structure
"""

# Global imports
import re
from loguru import logger
from typing import List, Dict, Any, Optional


class SceneProcessor:
    """Processes enhanced professional advertisement scripts to extract scenes"""

    def __init__(self):
        """Initialize the scene processor"""
        self.scene_pattern = re.compile(
            r"Scene\s+(\d+):\s*([^\n]+)\s*\n"
            r"Location Group:\s*([^\n]+)\s*\n"
            r"Visual:\s*([^\n]+(?:\n(?!Narration:)[^\n]+)*)\s*\n"
            r'Narration:\s*\(\(([^)]+)\)\)\s*"([^"]+)"\s*\n'
            r"SFX / Music:\s*([^\n]+)\s*\n"
            r"Transition:\s*([^\n]+)\s*\n"
            r"Duration:\s*([^\n]+)",
            re.MULTILINE | re.DOTALL,
        )

        self.character_profile_pattern = re.compile(
            r"\*\*CHARACTER PROFILE \(MAINTAIN CONSISTENCY\)\*\*\s*\n"
            r"Main Character:\s*([^\n]+(?:\n(?!Supporting Characters:)[^\n]+)*)",
            re.MULTILINE | re.DOTALL,
        )

        self.title_pattern = re.compile(r"Title:\s*([^\n]+)")
        self.overview_pattern = re.compile(r"Overview:\s*([^\n]+(?:\n[^\n]+)*)")

    def process_script_content(self, script_content: str) -> List[Dict[str, Any]]:
        """
        Process enhanced professional advertisement script content and extract scenes

        Args:
            script_content: The full script content in professional advertisement format

        Returns:
            List of scene dictionaries with proper structure
        """
        logger.info("Processing enhanced professional advertisement script content")

        # Safety check for empty or invalid content
        if not script_content or len(script_content.strip()) < 50:
            error_msg = f"Script content is too short or empty ({len(script_content) if script_content else 0} chars). Cannot extract scenes from invalid content."
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            # Extract character profile
            character_profile = self._extract_character_profile(script_content)

            # Extract title and overview
            title = self._extract_title(script_content)
            overview = self._extract_overview(script_content)

            # Extract scenes
            scenes = self._extract_scenes(script_content)

            # Enhance scenes with character profile and metadata
            enhanced_scenes = []
            logger.info(f"Enhancing {len(scenes)} scenes with metadata")

            for i, scene in enumerate(scenes):
                # Preserve original scene number if available, otherwise use sequential
                original_scene_number = scene.get("scene_number", i + 1)

                enhanced_scene = {
                    "scene_number": original_scene_number,  # Keep original scene number
                    "title": scene.get("title", f"Scene {original_scene_number}"),
                    "description": scene.get("visual_description", ""),
                    "visual_description": scene.get("visual_description", ""),
                    "narration": scene.get("narration", ""),
                    "duration": scene.get("duration", "5s"),
                    "location_group": scene.get("location_group", 1),
                    "status": "active",
                    "generation_status": "completed",
                    "character_info": character_profile,
                    "metadata": {
                        "sfx_music": scene.get("sfx_music", ""),
                        "transition": scene.get("transition", ""),
                        "script_title": title,
                        "script_overview": overview,
                        "scene_type": "professional_advertisement",
                    },
                }
                enhanced_scenes.append(enhanced_scene)
                logger.debug(f"Enhanced scene {original_scene_number}: {enhanced_scene['title']}")

            logger.success(
                f"Successfully processed {len(enhanced_scenes)} scenes from professional advertisement script"
            )
            return enhanced_scenes

        except ValueError:
            # Re-raise ValueError (our custom errors)
            raise
        except Exception as e:
            logger.error(f"Failed to process script content: {e}")
            # For unexpected errors, raise a ValueError with context
            error_msg = f"Script processing failed due to unexpected error: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)



    def _extract_character_profile(self, script_content: str) -> Dict[str, Any]:
        """Extract character profile from script content"""
        try:
            match = self.character_profile_pattern.search(script_content)
            if match:
                character_text = match.group(1).strip()
                return {
                    "main_character": character_text,
                    "description": character_text,
                    "type": "professional_advertisement",
                }
        except Exception as e:
            logger.warning(f"Failed to extract character profile: {e}")

        return {
            "main_character": "Professional advertisement character",
            "description": "Professional advertisement character",
            "type": "professional_advertisement",
        }

    def _extract_title(self, script_content: str) -> str:
        """Extract title from script content"""
        try:
            match = self.title_pattern.search(script_content)
            if match:
                return match.group(1).strip()
        except Exception as e:
            logger.warning(f"Failed to extract title: {e}")

        return "Professional Advertisement"

    def _extract_overview(self, script_content: str) -> str:
        """Extract overview from script content"""
        try:
            match = self.overview_pattern.search(script_content)
            if match:
                return match.group(1).strip()
        except Exception as e:
            logger.warning(f"Failed to extract overview: {e}")

        return "Professional advertisement script"

    def _extract_scenes(self, script_content: str) -> List[Dict[str, Any]]:
        """Extract scenes from script content using regex pattern"""
        scenes = []

        try:
            # DEBUG: Log script content preview for debugging
            logger.info(f"Script content preview (first 500 chars): {script_content[:500]}...")
            logger.info(f"Script content length: {len(script_content)} characters")

            matches = list(self.scene_pattern.finditer(script_content))
            logger.info(f"Regex pattern found {len(matches)} matches")

            for match in matches:
                scene_data = {
                    "scene_number": int(match.group(1)),
                    "title": match.group(2).strip(),
                    "location_group": self._parse_location_group(match.group(3)),
                    "visual_description": match.group(4).strip(),
                    "narration_type": match.group(5).strip(),
                    "narration": match.group(6).strip(),
                    "sfx_music": match.group(7).strip(),
                    "transition": match.group(8).strip(),
                    "duration": match.group(9).strip(),
                }
                scenes.append(scene_data)
                logger.debug(f"Extracted scene {scene_data['scene_number']}: {scene_data['title']}")

            # Sort by scene number
            scenes.sort(key=lambda x: x["scene_number"])
            logger.info(f"Regex extraction completed with {len(scenes)} scenes")

            # If no scenes found with regex, try basic extraction
            if not scenes:
                logger.warning("No scenes found with regex pattern, trying basic extraction")
                scenes = self._basic_scene_extraction(script_content)

                # If basic extraction also fails, raise error instead of creating defaults
                if not scenes:
                    error_msg = f"Failed to extract any scenes from script content. Both regex and basic extraction failed. Script may be malformed or not follow expected format."
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                logger.info("Regex extraction successful, skipping basic extraction")

        except ValueError:
            # Re-raise ValueError (our custom errors)
            raise
        except Exception as e:
            logger.error(f"Failed to extract scenes with regex: {e}")
            # Fallback to basic extraction
            scenes = self._basic_scene_extraction(script_content)

            # If basic extraction also fails, raise error
            if not scenes:
                error_msg = f"Failed to extract any scenes from script content. Error: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        return scenes

    def _parse_location_group(self, location_text: str) -> int:
        """Parse location group from text"""
        try:
            # Extract number from "Location 1/2/3/4"
            match = re.search(r"Location\s+(\d+)", location_text)
            if match:
                return int(match.group(1))
        except Exception as e:
            logger.warning(f"Failed to parse location group: {e}")

        return 1

    def _basic_scene_extraction(self, script_content: str) -> List[Dict[str, Any]]:
        """Basic scene extraction as fallback - should only be called when regex fails"""
        scenes = []

        try:
            logger.warning("🚨 BASIC SCENE EXTRACTION CALLED - This should only happen when regex fails!")
            logger.info("Attempting basic scene extraction as fallback")
            logger.info(f"Basic extraction input length: {len(script_content)} characters")

            # Try simpler patterns that don't overlap with the main regex
            scene_patterns = [
                r"SCENE\s+(\d+):",  # SCENE 1: (uppercase)
                r"(\d+)\.\s*Scene",  # 1. Scene
                r"(\d+)\)",  # 1)
                r"Scene\s+(\d+)",  # Scene 1 (without colon)
            ]

            scene_sections = []
            used_pattern = None
            for pattern in scene_patterns:
                sections = re.split(pattern, script_content)
                if len(sections) > 2:  # Need at least 3 sections to have 1 scene
                    logger.info(f"Basic extraction found scenes using pattern: {pattern}")
                    scene_sections = sections
                    used_pattern = pattern
                    break

            if not scene_sections:
                logger.warning("No scene patterns found in script content")
                # Don't create default scenes - return empty list to indicate failure
                logger.error("Basic extraction failed: No recognizable scene patterns found in script content")
                return []

            # Process found scene sections
            # When using re.split with capturing groups, the result alternates between
            # split content and captured groups. We need to process them in pairs.
            scene_number = 1
            for i in range(1, len(scene_sections), 2):  # Skip first empty section, process every other
                if i + 1 >= len(scene_sections):
                    break

                captured_number = scene_sections[i]  # The captured scene number
                section_content = scene_sections[i + 1]  # The actual scene content

                if not section_content.strip():
                    continue

                # Extract basic scene info
                lines = section_content.strip().split("\n")
                title = lines[0].strip() if lines else f"Scene {scene_number}"

                # Use the captured number if it's a valid integer
                try:
                    scene_num = int(captured_number)
                except (ValueError, TypeError):
                    scene_num = scene_number

                # Find visual description and narration
                visual_desc = ""
                narration = ""
                location_group = 1

                for line in lines:
                    line_clean = line.strip()
                    if line_clean.startswith("Visual:"):
                        visual_desc = line_clean.replace("Visual:", "").strip()
                    elif line_clean.startswith("Narration:"):
                        narration = line_clean.replace("Narration:", "").strip()
                        # Extract narration from quotes if present
                        quote_match = re.search(r'"([^"]+)"', narration)
                        if quote_match:
                            narration = quote_match.group(1)
                    elif line_clean.startswith("Location Group:"):
                        loc_match = re.search(r'Location\s+(\d+)', line_clean)
                        if loc_match:
                            location_group = int(loc_match.group(1))

                # If no specific content found, use the section content
                if not visual_desc and not narration:
                    section_text = ' '.join(lines[:3])  # Use first few lines
                    visual_desc = section_text[:200] if section_text else "Professional advertisement setting"
                    narration = section_text[:100] if section_text else "Professional advertisement narration"

                scene_data = {
                    "scene_number": scene_num,
                    "title": title[:50],  # Limit title length
                    "location_group": location_group,
                    "visual_description": visual_desc or "Professional advertisement setting",
                    "narration_type": "Voice-over, Professional",
                    "narration": narration or "Professional advertisement narration",
                    "sfx_music": "Professional background music",
                    "transition": "Smooth cut",
                    "duration": "5s",
                }
                scenes.append(scene_data)
                scene_number += 1

            logger.info(f"Basic extraction found {len(scenes)} scenes")

        except Exception as e:
            logger.error(f"Failed to perform basic scene extraction: {e}")
            # Don't create default scenes - return empty list to indicate failure
            scenes = []

        return scenes




# Create global instance
scene_processor = SceneProcessor()
