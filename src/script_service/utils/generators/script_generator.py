"""
Script generation utilities using Gemini AI
"""

# Global imports
from loguru import logger
from typing import Optional

# Local imports
from src.shared.config.settings import settings
from src.shared.services.llm_service import llm_service


class ScriptGenerator:
    """Script generation using unified LLM service"""

    def __init__(self):
        self.llm_service = llm_service

    def generate_script_sync(
        self,
        text_input: str,
        video_style: str,
        duration: str,
        brand_name: Optional[str] = None,
        product_name: Optional[str] = None,
        brand_description: Optional[str] = None,
    ) -> str:
        try:
            prompt = self._build_script_prompt(
                text_input, video_style, duration, brand_name, product_name, brand_description
            )
            result = self.llm_service.generate_content(prompt)
            if result["success"]:
                script_content = result["content"]
                logger.info(
                    f"Generated script of length {len(script_content)} characters using {result['provider']}"
                )
                return script_content
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                raise Exception(f"LLM service failed: {result.get('error')}")
        except Exception as e:
            logger.error(f"Script generation failed: {e}")
            raise

    def refine_script_sync(self, current_script: str, feedback: str) -> str:
        try:
            prompt = f"""
You are an expert script writer. Please refine the following script based on the user feedback provided.

CURRENT SCRIPT:
{current_script}

USER FEEDBACK:
{feedback}

Return only the refined script."""
            result = self.llm_service.generate_content(prompt)
            if result["success"]:
                refined_script = result["content"]
                logger.info(
                    f"Refined script of length {len(refined_script)} characters using {result['provider']}"
                )
                return refined_script
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                raise Exception(f"LLM service failed: {result.get('error')}")
        except Exception as e:
            logger.error(f"Script refinement failed: {e}")
            raise

    def _build_script_prompt(
        self,
        text_input: str,
        video_style: str,
        duration: str,
        brand_name: Optional[str] = None,
        product_name: Optional[str] = None,
        brand_description: Optional[str] = None,
    ) -> str:
        brand_context = ""
        if brand_name or product_name or brand_description:
            brand_context = f"""
BRAND CONTEXT:
- Brand Name: {brand_name or 'Not specified'}
- Product Name: {product_name or 'Not specified'}
- Brand Description: {brand_description or 'Not specified'}
"""
        # Calculate scene count
        duration_map = {
            "15 seconds": 15,
            "30 seconds": 30,
            "1 minute": 60,
            "2 minutes": 120,
            "3 minutes": 180,
        }
        total_seconds = duration_map.get(duration, 30)
        scene_count = total_seconds // 5
        return f"""
You are an expert video script writer. Create a compelling script for a {duration} video in {video_style} style.

VIDEO CONCEPT:
{text_input}

{brand_context}

REQUIREMENTS:
- Duration: {duration}
- Style: {video_style}
- Include engaging hook in the first 3 seconds
- Clear narrative structure with beginning, middle, and end
- Include scene descriptions and visual cues
- Natural, conversational tone
- Strong call-to-action if appropriate
- **You must output exactly {scene_count} scenes, each 5 seconds long, for a total of {duration}.**

CRITICAL: You MUST use this EXACT format for each scene:

Scene X: [Compelling scene title]
Location Group: [Location description]
Visual: [Detailed visual description for AI image generation]
Narration: ((narrator)) "Spoken dialogue or narration"
SFX / Music: [Sound effects and music description]
Transition: [Transition to next scene]
Duration: 5 seconds

EXAMPLE FORMAT:
Scene 1: Professional Introduction
Location Group: Modern Office
Visual: A confident professional in business attire standing in a sleek modern office with large windows, holding a premium leather work bag
Narration: ((professional voice)) "Meet the modern professional who demands excellence"
SFX / Music: Upbeat corporate music, subtle office ambiance
Transition: Smooth fade to product close-up
Duration: 5 seconds

Please provide exactly {scene_count} scenes using this EXACT format. Each scene must be exactly 5 seconds.
"""


# Global instance
script_generator = ScriptGenerator()
