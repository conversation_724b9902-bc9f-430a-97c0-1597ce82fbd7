# Script and Scene Generation Generator

## Overview

The Script and Scene Generation Generator is a unified service that combines script generation and scene processing using an enhanced professional advertisement framework. This service uses Gemini AI to create high-quality, production-ready advertisement scripts with character consistency and professional standards.

## Features

### Enhanced Professional Advertisement Framework
- **Professional Ad Creator Persona**: Uses 15+ years of experience in creating high-converting video ads
- **Character Consistency**: Maintains the same characters throughout all scenes with detailed descriptions
- **Hook Framework**: Captures attention within the first 3 seconds using various hook types
- **Location Grouping**: Organizes scenes by location for professional continuity
- **Brand Integration**: Natural product placement and brand authenticity

### Script Generation
- **Initial Script Creation**: Generate professional advertisement scripts from user input
- **Script Refinement**: Improve existing scripts based on user feedback
- **Brand Analysis**: Analyze brand images for visual characteristics
- **Optimal Scene Configuration**: Automatically determine scene count based on duration

### Scene Processing
- **Scene Extraction**: Parse professional advertisement scripts into individual scenes
- **Scene Regeneration**: Improve individual scenes based on feedback
- **Character Profile Management**: Maintain character consistency across scenes
- **Metadata Management**: Track scene metadata including SFX, transitions, and character info

## API Endpoints

### Script Generation
- `POST /generation/script` - Create a new script with professional advertisement generation
- `GET /generation/script/{script_id}` - Get a specific script with its scenes

### Scene Management
- `GET /generation/scenes` - List all scenes for a specific script
- `PUT /generation/scene/{scene_id}` - Update a scene manually
- `POST /generation/scene/{scene_id}/regenerate` - Regenerate a scene based on feedback
- `DELETE /generation/scene/{scene_id}` - Delete a specific scene

### Task Management
- `GET /generation/task/{task_id}` - Get the status of a specific task
- `GET /generation/tasks` - List all tasks for the current user

## Technical Architecture

### Core Components

1. **ScriptGenerator** (`services/script_generator.py`)
   - Enhanced professional advertisement script generation
   - Brand image analysis using Gemini Vision
   - Script refinement with feedback
   - Character consistency management

2. **SceneProcessor** (`utils/generators/scene_processor.py`)
   - Parse professional advertisement script format
   - Extract scenes with proper structure
   - Character profile extraction
   - Metadata management

3. **SceneService** (`services/scene_service.py`)
   - Scene processing and database management
   - Scene regeneration with enhanced framework
   - Integration with script generator

4. **Generation API** (`api/routes/generation.py`)
   - Unified API endpoints for script and scene generation
   - Task management and status tracking
   - Authentication and authorization

### Database Models

- **Script**: Main script entity with professional advertisement metadata
- **Scene**: Individual scenes with character info and metadata
- **TextAsset**: Content storage for scripts and scenes
- **TaskQueue**: Background task tracking

### Celery Tasks

- **generate_script_task**: Enhanced script generation with professional framework
- **regenerate_script_task**: Script refinement with feedback
- **regenerate_scene_task**: Scene regeneration with enhanced framework

## Usage Examples

### Create a Professional Advertisement Script

```bash
curl -X POST "http://localhost:8000/generation/script" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "text_input": "Create a professional advertisement for our new coffee product",
    "video_style": "Professional",
    "duration": "30 seconds",
    "brand_name": "Starbucks",
    "product_name": "Premium Coffee Blend",
    "brand_description": "Natural, authentic integration"
  }'
```

### Regenerate a Scene

```bash
curl -X POST "http://localhost:8000/generation/scene/{scene_id}/regenerate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "feedback": "Make the scene more dynamic and engaging"
  }'
```

## Professional Advertisement Framework

### Key Features

1. **Hook & Engagement (First 3 seconds)**
   - Product-centric, question, pain-point, or testimonial approaches
   - Visual hooks with product close-ups and hands-on demos
   - Strategic sound effects and compelling voice-over

2. **Story Arc & Messaging**
   - Problem→Solution or Before→After narrative structure
   - Relatable character development
   - Pain point framing and benefit focus
   - Social proof integration

3. **Visual Continuity & Character Consistency**
   - Same characters throughout all scenes
   - Detailed character descriptions (age, gender, ethnicity, clothing)
   - Identical outfit and hair color across scenes
   - Coherent environment and visual flow

4. **Location-Based Scene Grouping**
   - Distinct locations with identical backgrounds within groups
   - Smooth transitions between different locations
   - Professional flow with narrative purpose

5. **Technical Excellence**
   - Professional camera work specifications
   - Lighting and audio design details
   - Strategic transitions and pacing

### Scene Duration Requirements

- **STRICT MAXIMUM**: 5 seconds per scene (NO EXCEPTIONS)
- **Total Runtime**: Exact scene count × 5 seconds = target duration
- **Pacing**: ~150 words per minute for voice-over

## Configuration

The service uses the following configuration:

- **Gemini API**: For script generation and brand image analysis
- **Database**: PostgreSQL with async SQLAlchemy
- **Task Queue**: Celery for background processing
- **Logging**: Loguru with rotation and compression

## Error Handling

- **Fallback Mechanisms**: Multiple levels of fallback for scene extraction
- **Error Recovery**: Graceful handling of API failures
- **Logging**: Comprehensive logging for debugging and monitoring
- **Status Tracking**: Real-time task status updates

## Performance Considerations

- **Async Processing**: Non-blocking database operations
- **Background Tasks**: Celery for long-running operations
- **Caching**: Efficient scene and script content caching
- **Resource Management**: Proper cleanup of temporary resources
