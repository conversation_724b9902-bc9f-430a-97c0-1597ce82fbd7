"""
Pydantic models for script service request/response serialization
"""

# Global imports
from uuid import UUID
from enum import Enum
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """Task status enumeration"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class GenerationStatus(str, Enum):
    """Generation status enumeration"""

    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


# Base schemas
class ScriptBase(BaseModel):
    """Base script schema"""

    title: str = Field(..., min_length=1, max_length=255)
    video_style: str = Field(..., min_length=1, max_length=100)
    duration: str = Field(..., min_length=1, max_length=50)
    aspect_ratio: str = Field(default="16:9", max_length=20)
    narration_type: str = Field(default="voice-based", max_length=50)
    brand_name: Optional[str] = Field(None, max_length=255)
    product_name: Optional[str] = Field(None, max_length=255)
    brand_description: Optional[str] = None


class ScriptResponse(ScriptBase):
    """Script response schema"""

    id: UUID
    org_id: str
    user_id: UUID
    project_id: Optional[UUID] = None
    text_asset_id: Optional[str] = None
    content: Optional[str] = None  # Will be fetched from text_asset if available
    status: str
    generation_status: str
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = {}

    class Config:
        from_attributes = True


class SceneBase(BaseModel):
    """Base scene schema"""

    scene_number: int = Field(..., ge=1)
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    visual_description: str = Field(..., min_length=1)
    narration: str = Field(..., min_length=1)
    duration: str = Field(default="5s", max_length=50)
    location_group: Optional[int] = Field(None, ge=0)


class SceneCreate(SceneBase):
    """Scene creation schema"""

    script_id: UUID


class SceneResponse(SceneBase):
    """Scene response schema"""

    id: UUID
    script_id: UUID
    text_asset_id: Optional[str] = None
    status: str
    generation_status: str
    error_message: Optional[str] = None
    character_info: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime
    regenerated_at: Optional[datetime] = None
    metadata: Dict[str, Any] = {}

    class Config:
        from_attributes = True


class ScriptWithScenes(ScriptResponse):
    """Script with scenes schema"""

    scenes: List[SceneResponse] = []


class APIErrorResponse(BaseModel):
    """API error response schema"""

    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ScriptListResponse(BaseModel):
    """Paginated script list response"""

    items: List[ScriptResponse]
    total: int
    page: int
    size: int
    pages: int


class FormattedSceneResponse(BaseModel):
    """Formatted scene response for the specific format requested"""

    formatted_text: str = Field(..., description="Formatted scene text in the requested format")


class FormattedScenesResponse(BaseModel):
    """Response containing formatted scenes text"""

    scenes_text: str = Field(..., description="Complete formatted scenes text")
