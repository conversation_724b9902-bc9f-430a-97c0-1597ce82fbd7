"""
Celery tasks for enhanced script and scene generation
Handles background processing for the unified generation service
"""

# Global imports
import asyncio
from loguru import logger

# Local imports
from src.worker.celery_app import celery_app
from src.script_service.services.scene_service import regenerate_scene_content


@celery_app.task(name="script_service.regenerate_scene")
def regenerate_scene_task(scene_id: str, feedback: str):
    """
    Celery task for scene regeneration using enhanced professional advertisement framework

    Args:
        scene_id: The scene ID to regenerate
        feedback: User feedback for regeneration

    Returns:
        Updated scene data
    """
    logger.info(f"Starting enhanced scene regeneration task for scene {scene_id}")

    try:
        # Run the async function in a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Call the async scene regeneration function
        result = loop.run_until_complete(regenerate_scene_content(scene_id, feedback))

        logger.success(f"Enhanced scene regeneration completed successfully for scene {scene_id}")
        return result

    except Exception as e:
        logger.error(f"Enhanced scene regeneration failed for scene {scene_id}: {e}")
        raise
    finally:
        loop.close()
