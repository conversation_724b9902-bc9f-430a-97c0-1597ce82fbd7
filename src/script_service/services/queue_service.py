"""
Queue service utilities
Handles task queue operations and monitoring
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func, delete
from typing import List, Dict, Any, Optional
from uuid import UUID
from loguru import logger
from datetime import datetime, timedelta

from src.shared.config.database import db_manager
from src.shared.models.database_models import TaskQueue


class QueueService:
    """Queue service for task management"""

    @staticmethod
    async def get_user_tasks(
        org_id: str, user_id: str, status: Optional[str] = None, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get tasks for a specific user"""

        async for session in db_manager.get_session():
            try:
                query = select(TaskQueue).where(
                    TaskQueue.org_id == org_id, TaskQueue.user_id == UUID(user_id)
                )

                if status:
                    query = query.where(TaskQueue.status == status)

                query = query.order_by(TaskQueue.created_at.desc()).limit(limit)

                result = await session.execute(query)
                tasks = result.scalars().all()

                task_list = []
                for task in tasks:
                    task_dict = {
                        "id": str(task.id),
                        "task_id": task.task_id,
                        "task_type": task.task_type,
                        "status": task.status,
                        "progress": task.progress,
                        "result": task.result,
                        "error_message": task.error_message,
                        "created_at": task.created_at.isoformat(),
                        "started_at": task.started_at.isoformat() if task.started_at else None,
                        "completed_at": (
                            task.completed_at.isoformat() if task.completed_at else None
                        ),
                    }
                    task_list.append(task_dict)

                return task_list

            except Exception as e:
                logger.error(f"Failed to get user tasks: {e}")
                return []
            finally:
                break

    @staticmethod
    async def get_task_by_id(task_id: str, org_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific task by ID with ownership check"""

        async for session in db_manager.get_session():
            try:
                stmt = select(TaskQueue).where(
                    TaskQueue.task_id == task_id,
                    TaskQueue.org_id == org_id,
                    TaskQueue.user_id == UUID(user_id),
                )

                result = await session.execute(stmt)
                task = result.scalar_one_or_none()

                if not task:
                    return None

                return {
                    "id": str(task.id),
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "status": task.status,
                    "progress": task.progress,
                    "result": task.result,
                    "error_message": task.error_message,
                    "related_script_id": (
                        str(task.related_script_id) if task.related_script_id else None
                    ),
                    "related_scene_id": (
                        str(task.related_scene_id) if task.related_scene_id else None
                    ),
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                }

            except Exception as e:
                logger.error(f"Failed to get task by ID: {e}")
                return None
            finally:
                break

    @staticmethod
    async def get_queue_stats() -> Dict[str, Any]:
        """Get overall queue statistics"""

        async for session in db_manager.get_session():
            try:
                # Get task counts by status
                status_stmt = select(
                    TaskQueue.status, func.count(TaskQueue.id).label("count")
                ).group_by(TaskQueue.status)

                status_result = await session.execute(status_stmt)
                status_counts = {row.status: row.count for row in status_result}

                # Get task counts by type
                type_stmt = select(
                    TaskQueue.task_type, func.count(TaskQueue.id).label("count")
                ).group_by(TaskQueue.task_type)

                type_result = await session.execute(type_stmt)
                type_counts = {row.task_type: row.count for row in type_result}

                # Get pending tasks count
                pending_stmt = select(func.count(TaskQueue.id)).where(TaskQueue.status == "pending")
                pending_result = await session.execute(pending_stmt)
                pending_count = pending_result.scalar()

                # Get processing tasks count
                processing_stmt = select(func.count(TaskQueue.id)).where(
                    TaskQueue.status == "processing"
                )
                processing_result = await session.execute(processing_stmt)
                processing_count = processing_result.scalar()

                return {
                    "status_counts": status_counts,
                    "type_counts": type_counts,
                    "pending_tasks": pending_count,
                    "processing_tasks": processing_count,
                    "total_tasks": sum(status_counts.values()),
                }

            except Exception as e:
                logger.error(f"Failed to get queue stats: {e}")
                return {}
            finally:
                break

    @staticmethod
    async def cleanup_old_tasks(days_old: int = 30) -> int:
        """Clean up old completed/failed tasks"""

        async for session in db_manager.get_session():
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days_old)

                stmt = select(TaskQueue.id).where(
                    and_(
                        TaskQueue.status.in_(["completed", "failed"]),
                        TaskQueue.created_at < cutoff_date,
                    )
                )

                result = await session.execute(stmt)
                old_task_ids = [row.id for row in result]

                if old_task_ids:
                    delete_stmt = delete(TaskQueue).where(TaskQueue.id.in_(old_task_ids))
                    await session.execute(delete_stmt)
                    await session.commit()

                    logger.info(f"Cleaned up {len(old_task_ids)} old tasks")
                    return len(old_task_ids)

                return 0

            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to cleanup old tasks: {e}")
                return 0
            finally:
                break


# Global instance
queue_service = QueueService()
