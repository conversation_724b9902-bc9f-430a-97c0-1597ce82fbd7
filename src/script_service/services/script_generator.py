"""
Enhanced Script generation module using Gemini API with Professional Advertisement Framework
Handles initial script generation and refinement based on user feedback
Now includes professional ad maker persona, character consistency, and hook framework
"""

# Global imports
import sys
import base64
from pathlib import Path
from loguru import logger
from typing import Optional, Dict, Any

# Local imports
from src.shared.config.settings import settings
from src.shared.services.llm_service import llm_service


class ScriptGenerator:
    """Handles professional video script generation with advertisement expertise and character consistency"""

    def __init__(self, log_file: Optional[str] = None):
        """
        Initialize the script generator with Gemini API and configure logging

        Args:
            log_file: Optional path to log file. If None, uses default location.
        """
        self._setup_logging(log_file)

        try:
            logger.info("Initializing Enhanced ScriptGenerator with professional ad framework")

            # Check if LLM service is available
            if not llm_service.is_available():
                raise ValueError("No LLM providers available")

            logger.debug("Using unified LLM service")
            self.llm_service = llm_service
            self.current_script = ""
            self.brand_info = {}  # Store brand information

            available_providers = llm_service.get_available_providers()
            logger.success(
                f"Enhanced ScriptGenerator initialized successfully with providers: {available_providers}"
            )

        except Exception as e:
            logger.critical(f"Failed to initialize ScriptGenerator: {e}")
            raise

    def _setup_logging(self, log_file: Optional[str] = None):
        """Configure Loguru logging with appropriate handlers"""
        # Remove default handler to avoid duplicate logs
        logger.remove()

        # Add console handler with colors
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
            colorize=True,
        )

        # Add file handler with rotation
        log_path = log_file or "logs/script_generator.log"
        logger.add(
            log_path,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True,
        )

    def set_brand_info(
        self,
        brand_name: str = None,
        product_name: str = None,
        brand_image_path: str = None,
        brand_description: str = None,
    ):
        """
        Set brand information for script generation

        Args:
            brand_name: Name of the brand (e.g., "Coca-Cola")
            product_name: Specific product name (e.g., "Coke Zero")
            brand_image_path: Path to brand/product image
            brand_description: Additional brand context or guidelines
        """
        self.brand_info = {
            "brand_name": brand_name,
            "product_name": product_name,
            "brand_image_path": brand_image_path,
            "brand_description": brand_description,
        }

        if brand_name:
            logger.info(f"Brand information set: {brand_name} - {product_name}")
        else:
            logger.info("No brand information set - generating generic script")

    def _analyze_brand_image(self, image_path: str) -> str:
        """
        Use Gemini to analyze the brand image and extract visual characteristics

        Args:
            image_path: Path to the brand image

        Returns:
            Description of the brand image
        """
        try:
            logger.debug(f"Analyzing brand image: {image_path}")

            # Import PIL for proper image handling
            from PIL import Image

            # Load image using PIL instead of raw bytes
            image = Image.open(image_path)

            # Use Gemini vision capabilities
            vision_prompt = """
            Analyze this product/brand image and provide:
            1. Physical description (shape, colors, text, logos)
            2. Key visual elements that must be preserved
            3. Typical usage contexts
            4. Brand identity elements
            
            Be specific and concise. Focus on elements that would be important for product placement in various scenes.
            """

            # Pass PIL Image object instead of raw bytes
            result = self.llm_service.generate_content([vision_prompt, image])

            if result["success"]:
                return result["content"]
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                return "Brand product image provided"

        except Exception as e:
            logger.error(f"Error analyzing brand image: {e}")
            return "Brand product image provided"

    def _get_optimal_scene_count(self, duration: str) -> Dict[str, Any]:
        """
        Determine optimal scene count and location grouping based on STRICT 5-second scene limit

        Returns:
            Dictionary with scene count, location groups, and pacing guidelines for professional continuity
        """
        duration_map = {
            "15 seconds": {
                "scenes": 3,
                "max_elements": 2,
                "pacing": "fast",
                "locations": 2,  # 2 locations: 2 scenes + 1 scene
                "scenes_per_location": [2, 1],
            },
            "30 seconds": {
                "scenes": 6,
                "max_elements": 3,
                "pacing": "medium",
                "locations": 3,  # 3 locations: 2 scenes each
                "scenes_per_location": [2, 2, 2],
            },
            "1 minute": {
                "scenes": 12,
                "max_elements": 4,
                "pacing": "medium",
                "locations": 4,  # 4 locations: 3 scenes each
                "scenes_per_location": [3, 3, 3, 3],
            },
            "2 minutes": {
                "scenes": 24,
                "max_elements": 5,
                "pacing": "slow",
                "locations": 6,  # 6 locations: 4 scenes each
                "scenes_per_location": [4, 4, 4, 4, 4, 4],
            },
            "3 minutes": {
                "scenes": 36,
                "max_elements": 6,
                "pacing": "slow",
                "locations": 9,  # 9 locations: 4 scenes each
                "scenes_per_location": [4, 4, 4, 4, 4, 4, 4, 4, 4],
            },
        }

        return duration_map.get(
            duration,
            {
                "scenes": 12,
                "max_elements": 4,
                "pacing": "medium",
                "locations": 4,
                "scenes_per_location": [3, 3, 3, 3],
            },
        )

    @logger.catch
    def generate_initial_script(
        self,
        text_input: str,
        video_style: str,
        duration: str,
        brand_name: str = None,
        product_name: str = None,
        brand_image_path: str = None,
        brand_description: str = None,
    ) -> str:
        """
        Generate initial professional advertisement script with character consistency and hook framework

        Args:
            text_input: User's video idea or concept
            video_style: Style of video (Educational, Entertainment, etc.)
            duration: Target video duration
            brand_name: Optional brand name for integration
            product_name: Optional specific product name
            brand_image_path: Optional path to brand/product image
            brand_description: Optional brand guidelines or context

        Returns:
            Generated script in text format with professional ad elements and character consistency
        """
        logger.info("Starting professional advertisement script generation")
        logger.debug(f"Input parameters - Style: {video_style}, Duration: {duration}")

        # Set brand info if provided
        if brand_name or product_name:
            self.set_brand_info(brand_name, product_name, brand_image_path, brand_description)

        # Analyze brand image if provided
        brand_visual_description = ""
        if brand_image_path and Path(brand_image_path).exists():
            brand_visual_description = self._analyze_brand_image(brand_image_path)

        # Log input text length for debugging
        logger.debug(f"Input text length: {len(text_input)} characters")

        try:
            prompt = self._build_professional_generation_prompt(
                text_input, video_style, duration, brand_visual_description
            )
            logger.debug(f"Generated prompt length: {len(prompt)} characters")

            logger.info("Sending professional ad request to LLM service")
            result = self.llm_service.generate_content(prompt)

            if not result["success"]:
                logger.warning(f"LLM service failed: {result.get('error')}")
                return "Error: LLM service failed"

            if not result["content"]:
                logger.warning("Received empty response from AI service")
                return "Error: Empty response from API"

            self.current_script = result["content"]
            logger.success(
                f"Professional script generated successfully ({len(result['content'])} characters) using {result['provider']}"
            )
            logger.debug(f"Script preview: {result['content'][:200]}...")

            return result["content"]

        except Exception as e:
            logger.error(f"Error generating script: {str(e)}")
            logger.exception("Full traceback:")
            return f"Error generating script: {str(e)}"

    def _build_professional_generation_prompt(
        self, text_input: str, video_style: str, duration: str, brand_visual_description: str = ""
    ) -> str:
        """Build the professional advertisement generation prompt with consistency and hook framework"""
        logger.debug("Building professional advertisement generation prompt")

        # Get optimal scene configuration
        scene_config = self._get_optimal_scene_count(duration)

        # Base brand context
        brand_context = ""
        if self.brand_info.get("brand_name"):
            brand_context = f"""
        BRAND INTEGRATION REQUIREMENTS
        ──────────────────────────────
        • Brand: {self.brand_info.get('brand_name')}
        • Product: {self.brand_info.get('product_name', 'Brand product')}
        • Visual Description: {brand_visual_description}
        • Brand Guidelines: {self.brand_info.get('brand_description', 'Natural, authentic integration')}
        
        Integration Instructions:
        - Naturally integrate the {self.brand_info.get('product_name', 'product')} into 2-4 key scenes
        - Ensure product placement feels organic and enhances the story
        - Product should be visible but not forced or overly prominent
        - Maintain brand authenticity while serving the narrative
        - Not every scene needs the product - focus on meaningful moments
        - For each scene with the product, specify exact placement and context
        """

        prompt = f"""
        You are a PROFESSIONAL ADVERTISEMENT CREATOR with 15+ years of experience creating high-converting video ads.
        Your expertise includes viral marketing, consumer psychology, and cinematography.
        
        🚨 CRITICAL REQUIREMENT: Each scene must be EXACTLY 5 SECONDS OR LESS. NO EXCEPTIONS. 🚨
        
        Create a professional, production-ready advertisement script that maximizes engagement and conversion.

        CAMPAIGN INPUTS
        ──────────────
        • Core Message/Product: {text_input}  
        • Video Style: {video_style}
        • Target Duration: {duration}
        • Required Scene Count: {scene_config['scenes']} scenes (EXACT - not maximum)
        • Max Elements per Scene: {scene_config['max_elements']} 
        • Pacing Style: {scene_config['pacing']}
        • MATH CHECK: {scene_config['scenes']} scenes × 5 seconds = {duration}
        
        {brand_context}

        PROFESSIONAL ADVERTISEMENT FRAMEWORK
        ───────────────────────────────────

        **HOOK & ENGAGEMENT (First 3 seconds are CRITICAL)**
        - Hook Type: Use product-centric, question, pain-point, or testimonial approach
        - Visual Hook: Product close-ups, hands-on demo, or lifestyle scene
        - Emotional Tone: Create curiosity, excitement, urgency, or humor
        - Audio Hook: Strategic sound effects, compelling voice-over, or dramatic silence
        - Capture attention within first 0-3 seconds MAX

        **STORY ARC & MESSAGING**
        - Structure: Problem→Solution or Before→After narrative
        - Character Development: Create relatable personas that audience connects with
        - Pain Point Framing: Address specific customer problems early
        - Benefit Focus: Emphasize outcomes, not just features
        - Social Proof: Include testimonials, statistics, or user results when relevant

        **VISUAL CONTINUITY & CHARACTER CONSISTENCY**
        - CRITICAL: Use SAME CHARACTERS throughout all scenes
        - Detailed Character Descriptions: Specify age, gender, ethnicity, clothing, hair, distinctive features
        - Character Continuity: Each scene MUST reference the same character details
        - Visual Flow: Ensure scenes connect logically and visually
        - Coherent Environment: Maintain consistent locations/settings where appropriate

        **LOCATION-BASED SCENE GROUPING (PROFESSIONAL CONTINUITY)**
        - Total Locations: {scene_config['locations']} distinct locations
        - Scenes per Location: {scene_config['scenes_per_location']}
        - CRITICAL: Use IDENTICAL background/location descriptions for scenes within same location group
        - Location Transitions: Create smooth transitions between different locations
        - Visual Continuity: Maintain same lighting, setting, and environment within location groups
        - Professional Flow: Each location serves a narrative purpose

        **PACING & STRUCTURE**
        - Scene Duration: STRICT MAXIMUM 5 seconds per scene (NO EXCEPTIONS)
        - Total Runtime: {duration} = {scene_config['scenes']} scenes × 5 seconds each
        - Transition Flow: Use cuts, dissolves, or match-cuts strategically
        - Building Tension: Create anticipation leading to resolution
        - Climactic Peak: Position strongest moment in final 1/3
        - Call-to-Action: Clear, compelling CTA in last 5-10 seconds

        **TECHNICAL EXCELLENCE**
        - Camera Work: Specify shot types (close-up, mid-shot, wide)
        - Movement: Include camera movements (static, pan, dolly, handheld)
        - Lighting: Professional lighting descriptions (natural, studio, dramatic)
        - Audio Design: Background music style, sound effects, voice-over tone

        DELIVERABLE FORMAT (MAINTAIN EXACT STRUCTURE)
        ────────────────────────────────────────────
        Title: [Compelling, memorable title ≤ 8 words]  
        Overview: [2-3 lines summarizing story arc and value proposition]  
        
        Core Brand Notes:  
        Tone & Personality: [Professional tone keywords]  
        Mandatory Tag-line / CTA: "[Compelling call-to-action]"  
        Colour / Mood Keywords: [Professional palette references] 
        Do-Not-Show / Restricted: [Any content restrictions]  

        **CHARACTER PROFILE (MAINTAIN CONSISTENCY)**
        Main Character: [DETAILED description including:]
            • Exact age (e.g., "32-year-old")
            • Gender and specific ethnicity
            • EXACT hair color, style, length (e.g., "shoulder-length auburn brown hair, slightly wavy")
            • Eye color and facial features
            • EXACT clothing description (e.g., "navy blue blazer over white button-down shirt, dark jeans")
            • Specific accessories (watch, jewelry, glasses, etc.)
            • Build, height, posture, distinctive features
        Supporting Characters: [If any - equally detailed descriptions with exact clothing]
        Character Arc: [How character transforms/benefits from product]

        Scenes: {scene_config['scenes']} scenes (EXACT COUNT to match {duration})
        Location Groups: {scene_config['locations']} locations with {scene_config['scenes_per_location']} scenes each

        **LOCATION GROUPING INSTRUCTIONS:**
        - Location 1: Scenes 1-{scene_config['scenes_per_location'][0]} (IDENTICAL background/setting)
        - Location 2: Scenes {scene_config['scenes_per_location'][0]+1}-{sum(scene_config['scenes_per_location'][:2])} (IDENTICAL background/setting)
        {"- Location 3: Scenes " + str(sum(scene_config['scenes_per_location'][:2])+1) + "-" + str(sum(scene_config['scenes_per_location'][:3])) + " (IDENTICAL background/setting)" if len(scene_config['scenes_per_location']) >= 3 else ""}
        {"- Location 4: Scenes " + str(sum(scene_config['scenes_per_location'][:3])+1) + "-" + str(sum(scene_config['scenes_per_location'][:4])) + " (IDENTICAL background/setting)" if len(scene_config['scenes_per_location']) >= 4 else ""}

        For EACH scene supply (EXACT FORMAT):
        ──────────────────────────────────────
        Scene X: [Compelling scene slug ≤ 8 words]  
        Location Group: [Location 1/2/3/4 - specify which group this scene belongs to]
        Visual: [Detailed AI-prompt-ready description including:]
            • LOCATION CONSISTENCY: If same location group, use IDENTICAL background/setting description
            • CHARACTER CONSISTENCY: Reference exact character details from profile - EXACT same clothing, hair color, and appearance
            • CRITICAL: Character must wear IDENTICAL outfit across all scenes (same colors, same style, same accessories)
            • CRITICAL: Character must have IDENTICAL hair color and style across all scenes
            • Specific location and cultural context (CONSISTENT within location group)
            • Detailed wardrobe, props, and environmental elements (CONSISTENT when in same location)
            • Professional camera work (framing, movement, lens choice)
            • Lighting setup and mood (CONSISTENT within location group)
            {"• BRAND PLACEMENT: Specify exact position and context of " + self.brand_info.get('product_name', 'product') + " if present" if self.brand_info.get('brand_name') else ""}
        Narration: ((Voice-over, {video_style})) "[Exact compelling copy in quotes]"
        SFX / Music: [Professional audio design - music style, sound effects]  
        Transition: [Professional transition guidance - smooth within location, dynamic between locations]  
        Duration: 5s (MAXIMUM - can be shorter, NEVER longer)

        PROFESSIONAL VALIDATION CHECKLIST
        ─────────────────────────────────
        ✓ Hook captures attention in first 3 seconds
        ✓ Same character(s) appear consistently across all scenes
        ✓ CRITICAL: Character wears IDENTICAL clothing across all scenes (same outfit, same colors)
        ✓ CRITICAL: Character has IDENTICAL hair color and style across all scenes
        ✓ Clear problem→solution or before→after narrative
        ✓ Maximum {scene_config['max_elements']} key elements per scene
        ✓ Logical scene flow and visual continuity
        ✓ CRITICAL: Location consistency within groups ({scene_config['locations']} locations total)
        ✓ CRITICAL: Identical backgrounds for scenes within same location group
        ✓ Smooth transitions between different locations
        ✓ Compelling CTA in final scenes
        ✓ CRITICAL: Each scene is EXACTLY 5 seconds or less (NEVER longer)
        ✓ Total runtime: {scene_config['scenes']} scenes × 5s = {duration}
        ✓ Voice-over pacing fits ~150 words per minute
        {"✓ Brand integration feels natural and authentic" if self.brand_info.get('brand_name') else ""}
        ✓ Each scene advances the story or emotional journey
        ✓ Professional production quality throughout

        CREATE A HIGH-CONVERTING ADVERTISEMENT THAT PROFESSIONALS WOULD BE PROUD TO PRODUCE.
        """

        logger.debug("Professional generation prompt built successfully")
        return prompt

    @logger.catch
    def refine_script(self, current_script: str, feedback: str) -> str:
        """
        Refine existing script based on user feedback while maintaining professional standards and character consistency

        Args:
            current_script: The current version of the script
            feedback: User's feedback or requested changes

        Returns:
            Refined script maintaining professional advertisement standards
        """
        logger.info("Starting professional script refinement")
        logger.debug(f"Current script length: {len(current_script)} characters")
        logger.debug(f"Feedback: {feedback}")

        try:
            prompt = self._build_professional_refinement_prompt(current_script, feedback)
            logger.debug(f"Refinement prompt length: {len(prompt)} characters")

            logger.info("Sending professional refinement request to LLM service")
            result = self.llm_service.generate_content(prompt)

            if not result["success"]:
                logger.warning(f"LLM service failed during refinement: {result.get('error')}")
                return "Error: LLM service failed"

            if not result["content"]:
                logger.warning("Received empty response from AI service during refinement")
                return "Error: Empty response from API"

            # Compare old and new script lengths
            old_length = len(self.current_script)
            new_length = len(result["content"])

            self.current_script = result["content"]

            logger.success(f"Professional script refined successfully using {result['provider']}")
            logger.info(f"Script length changed from {old_length} to {new_length} characters")
            logger.debug(f"Refined script preview: {result['content'][:200]}...")

            return result["content"]

        except Exception as e:
            logger.error(f"Error refining script: {str(e)}")
            logger.exception("Full traceback:")
            return f"Error refining script: {str(e)}"

    def _build_professional_refinement_prompt(self, current_script: str, feedback: str) -> str:
        """Build the prompt for professional script refinement"""
        logger.debug("Building professional refinement prompt")

        brand_reminder = ""
        if self.brand_info.get("brand_name"):
            brand_reminder = f"""
        MAINTAIN BRAND INTEGRATION:
        - Brand: {self.brand_info.get('brand_name')}
        - Product: {self.brand_info.get('product_name')}
        - Ensure product placement remains natural and authentic
        """

        prompt = f"""
        You are a PROFESSIONAL ADVERTISEMENT CREATOR refining a high-quality video script.
        
        CURRENT SCRIPT:
        ═══════════════
        {current_script}
        
        CLIENT FEEDBACK:
        ═══════════════
        {feedback}
        
        {brand_reminder}
        
        REFINEMENT REQUIREMENTS:
        ═══════════════════════
        
        **MAINTAIN PROFESSIONAL STANDARDS:**
        - Keep the exact same format structure (Title, Overview, Core Brand Notes, Scenes)
        - Preserve character consistency across all scenes
        - Maintain professional advertisement quality
        - Ensure hook remains compelling in first 3 seconds
        - Keep clear problem→solution narrative flow
        
        **ADDRESS FEEDBACK WHILE PRESERVING:**
        - Character descriptions and continuity
        - Scene-based structure for image generation compatibility
        - Professional production quality
        - Optimal scene count and pacing
        - Brand integration authenticity (if applicable)
        
        **ENHANCEMENT FOCUS:**
        - Improve areas specifically mentioned in feedback
        - Maintain visual coherence between scenes
        - Ensure each scene advances the story
        - Keep compelling call-to-action
        - Preserve professional cinematography details
        
        Refine the script to address the client's concerns while maintaining all professional advertisement standards and structural requirements.
        """

        logger.debug("Professional refinement prompt built successfully")
        return prompt

    def get_brand_info(self) -> Dict[str, Any]:
        """Get current brand information"""
        return self.brand_info.copy()

    def get_current_script(self) -> str:
        """Get the current script"""
        logger.debug("Retrieving current script")
        if self.current_script:
            logger.debug(f"Current script length: {len(self.current_script)} characters")
        else:
            logger.warning("No current script available")
        return self.current_script

    def clear_script(self) -> None:
        """Clear the current script and brand info"""
        logger.info("Clearing current script and brand info")
        old_length = len(self.current_script) if self.current_script else 0
        self.current_script = ""
        self.brand_info = {}
        logger.debug(f"Cleared script of {old_length} characters")


# Create a global instance
script_generator = ScriptGenerator()
