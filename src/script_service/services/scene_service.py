"""
Scene service for enhanced professional advertisement scripts
Handles scene processing and regeneration using the new scene processor
"""

# Global imports
import asyncio
from uuid import UUID
from loguru import logger
from sqlalchemy import select
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

# Local imports
from src.shared.config.database import get_db
from src.shared.models.database_models import Scene, Script, TextAsset
from src.script_service.utils.generators.scene_processor import scene_processor


async def process_scenes_for_script(script_id: str, script_content: str) -> List[Dict[str, Any]]:
    """
    Process scenes from script content using enhanced professional advertisement framework

    Args:
        script_id: The script ID
        script_content: The script content to process

    Returns:
        List of processed scene dictionaries
    """
    logger.info(f"Processing scenes for script {script_id} using enhanced framework")

    try:
        # Use the enhanced scene processor
        scenes = scene_processor.process_script_content(script_content)

        # Delete existing scenes for this script
        await delete_existing_scenes(script_id)

        # Save new scenes to database
        saved_scenes = await save_scenes_to_database(script_id, scenes)

        logger.success(f"Successfully processed {len(saved_scenes)} scenes for script {script_id}")
        return saved_scenes

    except Exception as e:
        logger.error(f"Failed to process scenes for script {script_id}: {e}")
        raise


async def delete_existing_scenes(script_id: str):
    """Delete existing scenes for a script"""
    async for session in get_db():
        try:
            stmt = select(Scene).where(Scene.script_id == script_id)
            result = await session.execute(stmt)
            existing_scenes = result.scalars().all()

            for scene in existing_scenes:
                await session.delete(scene)

            await session.commit()
            logger.info(f"Deleted {len(existing_scenes)} existing scenes for script {script_id}")

        except Exception as e:
            logger.error(f"Failed to delete existing scenes: {e}")
            await session.rollback()
            raise


async def save_scenes_to_database(
    script_id: str, scenes_data: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Save scenes to database"""
    async for session in get_db():
        try:
            # Get script to get org_id
            script_stmt = select(Script).where(Script.id == script_id)
            script_result = await session.execute(script_stmt)
            script = script_result.scalar_one_or_none()

            if not script:
                raise ValueError("Script not found")

            saved_scenes = []
            for scene_data in scenes_data:
                # Create text asset for scene content
                scene_asset_id = f"scene-{scene_data.get('scene_number', 1)}-{script_id[:8]}"
                scene_text_asset = TextAsset(
                    org_id=script.org_id,
                    asset_id=scene_asset_id,
                    content=scene_data.get("description", ""),
                    type="scene",
                    metadata={
                        "script_id": script_id,
                        "scene_number": scene_data.get("scene_number", 1),
                        "generated_at": "2024-01-01T00:00:00Z",
                    },
                )
                session.add(scene_text_asset)
                await session.flush()

                # Create scene record
                scene = Scene(
                    script_id=script_id,
                    text_asset_id=scene_asset_id,
                    scene_number=scene_data.get("scene_number", 1),
                    title=scene_data.get("title", "")[:255] if scene_data.get("title") else None,
                    description=scene_data.get("description", ""),
                    visual_description=scene_data.get("visual_description", ""),
                    narration=scene_data.get("narration", ""),
                    duration=scene_data.get("duration", "5s"),
                    location_group=scene_data.get("location_group", 1),
                    status="active",
                    generation_status="completed",
                    character_info=scene_data.get("character_info", {}),
                    metadata=scene_data.get("metadata", {}),
                )
                session.add(scene)
                await session.flush()

                # Convert to dict for return
                scene_dict = {
                    "id": str(scene.id),
                    "script_id": str(scene.script_id),
                    "text_asset_id": scene.text_asset_id,
                    "scene_number": scene.scene_number,
                    "title": scene.title,
                    "description": scene.description,
                    "visual_description": scene.visual_description,
                    "narration": scene.narration,
                    "duration": scene.duration,
                    "location_group": scene.location_group,
                    "status": scene.status,
                    "generation_status": scene.generation_status,
                    "character_info": scene.character_info or {},
                    "metadata": scene.metadata or {},
                }
                saved_scenes.append(scene_dict)

            await session.commit()
            logger.info(f"Saved {len(saved_scenes)} scenes to database for script {script_id}")
            return saved_scenes

        except Exception as e:
            logger.error(f"Failed to save scenes to database: {e}")
            await session.rollback()
            raise


async def regenerate_scene_content(scene_id: str, feedback: str) -> Dict[str, Any]:
    """
    Regenerate a single scene based on feedback using enhanced framework

    Args:
        scene_id: The scene ID to regenerate
        feedback: User feedback for regeneration

    Returns:
        Updated scene data
    """
    logger.info(f"Regenerating scene {scene_id} with feedback")

    try:
        # Get current scene data
        async for session in get_db():
            stmt = select(Scene).where(Scene.id == scene_id)
            result = await session.execute(stmt)
            scene = result.scalar_one_or_none()

            if not scene:
                raise ValueError("Scene not found")

            # Get script content for context
            script_stmt = select(Script).where(Script.id == scene.script_id)
            script_result = await session.execute(script_stmt)
            script = script_result.scalar_one_or_none()

            if not script or not script.text_asset_id:
                raise ValueError("Script or text asset not found")

            text_asset_stmt = select(TextAsset).where(TextAsset.asset_id == script.text_asset_id)
            text_asset_result = await session.execute(text_asset_stmt)
            text_asset = text_asset_result.scalar_one_or_none()

            if not text_asset:
                raise ValueError("Text asset content not found")

            # Use enhanced script generator to refine the scene
            from src.script_service.services.script_generator import script_generator

            # Create a focused prompt for scene regeneration
            scene_context = f"""
            Current Scene:
            Title: {scene.title}
            Visual Description: {scene.visual_description}
            Narration: {scene.narration}
            Duration: {scene.duration}
            Location Group: {scene.location_group}
            
            User Feedback: {feedback}
            
            Please regenerate this scene with the feedback applied while maintaining professional advertisement standards.
            """

            # Use the script generator's refinement capability
            refined_content = script_generator.refine_script(text_asset.content, scene_context)

            # Process the refined content to extract the updated scene
            scenes = scene_processor.process_script_content(refined_content)

            # Find the corresponding scene (by scene number)
            updated_scene = None
            for s in scenes:
                if s.get("scene_number") == scene.scene_number:
                    updated_scene = s
                    break

            if not updated_scene:
                # If no matching scene found, use the first one
                updated_scene = (
                    scenes[0]
                    if scenes
                    else {
                        "title": scene.title,
                        "description": scene.description,
                        "visual_description": scene.visual_description,
                        "narration": scene.narration,
                        "duration": scene.duration,
                        "location_group": scene.location_group,
                    }
                )

            # Update the scene in database
            scene.title = updated_scene.get("title", scene.title)
            scene.description = updated_scene.get("description", scene.description)
            scene.visual_description = updated_scene.get(
                "visual_description", scene.visual_description
            )
            scene.narration = updated_scene.get("narration", scene.narration)
            scene.duration = updated_scene.get("duration", scene.duration)
            scene.location_group = updated_scene.get("location_group", scene.location_group)
            scene.character_info = updated_scene.get("character_info", scene.character_info or {})
            scene.metadata = updated_scene.get("metadata", scene.metadata or {})

            await session.commit()

            # Return updated scene data
            return {
                "id": str(scene.id),
                "script_id": str(scene.script_id),
                "text_asset_id": scene.text_asset_id,
                "scene_number": scene.scene_number,
                "title": scene.title,
                "description": scene.description,
                "visual_description": scene.visual_description,
                "narration": scene.narration,
                "duration": scene.duration,
                "location_group": scene.location_group,
                "status": scene.status,
                "generation_status": scene.generation_status,
                "character_info": scene.character_info or {},
                "metadata": scene.metadata or {},
            }

    except Exception as e:
        logger.error(f"Failed to regenerate scene {scene_id}: {e}")
        raise
