"""
Scene formatting service with multiple output formats
Provides flexible scene formatting with different templates and styles
"""

# Global imports
from enum import Enum
from typing import List, Dict, Any, Optional

# Local imports
from src.shared.models.database_models import Scene


class SceneFormatType(str, Enum):
    """Available scene format types"""

    SIMPLE = "simple"  # Just scene number and title
    DETAILED = "detailed"  # Full scene with all details
    COMPACT = "compact"  # Condensed format
    CUSTOM = "custom"  # User-defined format
    STORYBOARD = "storyboard"  # Visual storyboard format


class SceneFormatter:
    """Service for formatting scenes in different styles"""

    def __init__(self):
        self.default_templates = {
            SceneFormatType.SIMPLE: {
                "template": "Scene {number}: {description}",
                "separator": "\n",
            },
            SceneFormatType.DETAILED: {
                "template": "Scene {number}\n{description} Location Group: {location} SFX / Music: {sfx_music}",
                "separator": "\n\n",
            },
            SceneFormatType.COMPACT: {
                "template": "Scene {number} - {description} ({location}) - {sfx_music}",
                "separator": "\n",
            },
            SceneFormatType.STORYBOARD: {
                "template": "📽️ Scene {number}: {description}\n📍 Location: {location}\n🎵 Audio: {sfx_music}\n⏱️ Duration: {duration}",
                "separator": "\n\n",
            },
        }

    def format_scenes(
        self,
        scenes: List[Scene],
        format_type: SceneFormatType = SceneFormatType.DETAILED,
        custom_template: Optional[str] = None,
        include_metadata: bool = False,
    ) -> str:
        """
        Format scenes according to the specified format type

        Args:
            scenes: List of scene objects
            format_type: Type of formatting to apply
            custom_template: Custom template string (for CUSTOM format)
            include_metadata: Whether to include additional metadata

        Returns:
            Formatted scenes as string
        """
        if not scenes:
            return "No scenes found."

        if format_type == SceneFormatType.CUSTOM and not custom_template:
            raise ValueError("Custom template required for CUSTOM format type")

        template_info = self.default_templates.get(
            format_type, self.default_templates[SceneFormatType.DETAILED]
        )
        template = (
            custom_template if format_type == SceneFormatType.CUSTOM else template_info["template"]
        )
        separator = template_info["separator"]

        formatted_scenes = []
        for scene in scenes:
            formatted_scene = self._format_single_scene(scene, template, include_metadata)
            formatted_scenes.append(formatted_scene)

        return separator.join(formatted_scenes)

    def _format_single_scene(
        self, scene: Scene, template: str, include_metadata: bool = False
    ) -> str:
        """Format a single scene using the provided template"""

        # Extract scene data properly
        title = scene.title or scene.description or f"Scene {scene.scene_number}"
        description = scene.description or scene.visual_description or title

        # Get location group
        location = f"Location {scene.location_group}" if scene.location_group else "Location 1"

        # Get SFX/Music from extra_metadata
        sfx_music = ""
        if scene.extra_metadata and isinstance(scene.extra_metadata, dict):
            sfx_music = scene.extra_metadata.get("sfx_music", "")

        # If no sfx_music in extra_metadata, try to extract from narration or description
        if not sfx_music:
            # Look for sound-related keywords in description or narration
            sound_keywords = ["sound", "music", "audio", "sfx", "ambient", "background"]
            text_to_search = f"{scene.description or ''} {scene.visual_description or ''} {scene.narration or ''}".lower()

            if any(keyword in text_to_search for keyword in sound_keywords):
                # Extract a relevant sound description
                if "music" in text_to_search:
                    sfx_music = "Background music and ambient sounds."
                elif "sound" in text_to_search:
                    sfx_music = "Ambient sounds and effects."
                else:
                    sfx_music = "Background audio and effects."
            else:
                sfx_music = "Background music and ambient sounds."

        # Prepare template variables
        template_vars = {
            "number": scene.scene_number,
            "title": title,
            "description": description,
            "location": location,
            "sfx_music": sfx_music,
            "duration": scene.duration or "5s",
            "visual_description": scene.visual_description or "",
            "narration": scene.narration or "",
        }

        # Add metadata if requested
        if include_metadata:
            template_vars.update(
                {
                    "id": str(scene.id),
                    "status": scene.status,
                    "generation_status": scene.generation_status,
                    "created_at": scene.created_at.isoformat() if scene.created_at else "",
                    "updated_at": scene.updated_at.isoformat() if scene.updated_at else "",
                }
            )

        # Format using template
        try:
            return template.format(**template_vars)
        except KeyError as e:
            # Fallback if template has unknown variables
            return f"Scene {scene.scene_number}: {title}"

    def get_available_formats(self) -> Dict[str, str]:
        """Get list of available format types with descriptions"""
        return {
            SceneFormatType.SIMPLE: "Simple format with just scene number and title",
            SceneFormatType.DETAILED: "Detailed format with location and SFX/Music",
            SceneFormatType.COMPACT: "Compact format on single line",
            SceneFormatType.STORYBOARD: "Visual storyboard format with emojis",
            SceneFormatType.CUSTOM: "Custom format using user-defined template",
        }

    def get_template_variables(self) -> Dict[str, str]:
        """Get available template variables for custom formatting"""
        return {
            "{number}": "Scene number",
            "{title}": "Scene title",
            "{location}": "Location group",
            "{sfx_music}": "SFX and music description",
            "{duration}": "Scene duration",
            "{description}": "Scene description",
            "{visual_description}": "Visual description",
            "{narration}": "Narration text",
        }


# Global instance
scene_formatter = SceneFormatter()
