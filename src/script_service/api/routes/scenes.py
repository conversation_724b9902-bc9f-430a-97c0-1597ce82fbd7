"""
Scene API endpoints with authentication
Handles scene retrieval, regeneration, and management
"""

from loguru import logger

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from typing import List, Optional
from uuid import UUID, uuid4

from src.shared.config.database import get_database_session
from src.shared.models.database_models import Scene, TaskQueue, Script, TextAsset
from src.shared.core.dependencies import require_auth
from src.shared.schemas.script import (
    SceneResponse,
    SceneUpdate,
    SceneRegenerationRequest,
    TaskQueueResponse,
)
from src.script_service.tasks import regenerate_scene_task
from src.script_service.utils.generators.scene_processor import SceneProcessor

router = APIRouter(prefix="/scenes", tags=["Scene Management"])


@router.get("/", response_model=dict)
async def list_scenes(
    script_id: UUID = Query(..., description="Script ID to filter scenes"),
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, email = user_info
    try:
        # Verify script exists and belongs to user
        script_stmt = select(Script).where(
            Script.id == script_id, Script.org_id == org_id, Script.user_id == UUID(user_id)
        )
        script_result = await session.execute(script_stmt)
        script = script_result.scalar_one_or_none()
        if not script:
            raise HTTPException(status_code=404, detail="Script not found")

        # Fetch all scenes for this script from the database
        scenes_stmt = select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
        scenes_result = await session.execute(scenes_stmt)
        scenes = scenes_result.scalars().all()
        if not scenes:
            return {"scene_ids": [], "scenes": []}

        scene_ids = [str(scene.id) for scene in scenes]
        scene_dicts = [
            {
                "id": str(scene.id),
                "scene_number": scene.scene_number,
                "title": scene.title,
                "description": scene.description,
                "visual": scene.visual_description,
                "narration": scene.narration,
                "duration": scene.duration,
                "location_group": scene.location_group,
                "status": scene.status,
                "generation_status": scene.generation_status,
                "full_content": scene.description,  # or another field if you want
            }
            for scene in scenes
        ]
        return {"scene_ids": scene_ids, "scenes": scene_dicts}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list scenes: {str(e)}")


# @router.get("/{scene_id}", response_model=SceneResponse)
# async def get_scene(
#     scene_id: UUID,
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session)
# ):
#     """
#     Get a specific scene by ID
#     """
#     org_id, user_id, email = user_info
#
#     try:
#         # Get scene with ownership check through script
#         stmt = (
#             select(Scene)
#             .join(Script, Scene.script_id == Script.id)
#             .where(
#                 Scene.id == scene_id,
#                 Script.org_id == org_id,
#                 Script.user_id == UUID(user_id)
#             )
#         )
#
#         result = await session.execute(stmt)
#         scene = result.scalar_one_or_none()
#
#         if not scene:
#             raise HTTPException(status_code=404, detail="Scene not found")
#
#         return SceneResponse(
#             id=scene.id,
#             script_id=scene.script_id,
#             text_asset_id=scene.text_asset_id,
#             scene_number=scene.scene_number,
#             title=scene.title,
#             description=scene.description,
#             visual_description=scene.visual_description,
#             narration=scene.narration,
#             duration=scene.duration,
#             location_group=scene.location_group,
#             status=scene.status,
#             generation_status=scene.generation_status,
#             error_message=scene.error_message,
#             character_info=scene.character_info or {},
#             created_at=scene.created_at,
#             updated_at=scene.updated_at,
#             regenerated_at=scene.regenerated_at,
#             metadata=dict(scene.metadata) if isinstance(scene.metadata, dict) else {}
#
#         )
#
#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to get scene: {str(e)}")


@router.put("/{scene_id}", response_model=SceneResponse)
async def update_scene(
    scene_id: UUID,
    scene_update: SceneUpdate,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Update a scene manually (direct edit)
    """
    org_id, user_id, email = user_info

    try:
        # Get existing scene with ownership check
        stmt = (
            select(Scene)
            .join(Script, Scene.script_id == Script.id)
            .where(Scene.id == scene_id, Script.org_id == org_id, Script.user_id == UUID(user_id))
        )

        result = await session.execute(stmt)
        scene = result.scalar_one_or_none()

        if not scene:
            raise HTTPException(status_code=404, detail="Scene not found")

        # Update fields that are provided
        update_data = scene_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(scene, field):
                setattr(scene, field, value)

        await session.commit()
        await session.refresh(scene)

        return SceneResponse(
            id=scene.id,
            script_id=scene.script_id,
            text_asset_id=scene.text_asset_id,
            scene_number=scene.scene_number,
            title=scene.title,
            description=scene.description,
            visual_description=scene.visual_description,
            narration=scene.narration,
            duration=scene.duration,
            location_group=scene.location_group,
            status=scene.status,
            generation_status=scene.generation_status,
            error_message=scene.error_message,
            character_info=scene.character_info or {},
            created_at=scene.created_at,
            updated_at=scene.updated_at,
            regenerated_at=scene.regenerated_at,
            metadata=dict(scene.metadata) if isinstance(scene.metadata, dict) else {},
        )

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update scene: {str(e)}")


@router.post("/{scene_id}/regenerate", response_model=TaskQueueResponse, status_code=202)
async def regenerate_scene(
    scene_id: UUID,
    regeneration_request: SceneRegenerationRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Regenerate a scene based on feedback

    Returns task information for tracking progress
    """
    org_id, user_id, email = user_info

    try:
        # Verify scene exists and belongs to user through script
        stmt = (
            select(Scene)
            .join(Script, Scene.script_id == Script.id)
            .where(Scene.id == scene_id, Script.org_id == org_id, Script.user_id == UUID(user_id))
        )

        result = await session.execute(stmt)
        scene = result.scalar_one_or_none()

        if not scene:
            raise HTTPException(status_code=404, detail="Scene not found")

        # Create task queue entry
        task_id = str(uuid4())
        task_queue = TaskQueue(
            task_id=task_id,
            task_type="scene_regeneration",
            org_id=org_id,
            user_id=UUID(user_id),
            related_scene_id=scene.id,
            input_data={"feedback": regeneration_request.feedback},
            queue_name="scene_processing",
        )

        session.add(task_queue)
        await session.commit()

        # Start background task
        logger.info(f"Starting scene regeneration task: {task_id} for scene: {scene_id}")
        regenerate_scene_task.apply_async(
            args=[str(scene_id), regeneration_request.feedback], task_id=task_id
        )

        return TaskQueueResponse(
            id=task_queue.id,
            task_id=task_id,
            task_type="scene_regeneration",
            org_id=org_id,
            user_id=UUID(user_id),
            status="pending",
            progress=0,
            created_at=task_queue.created_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to regenerate scene {scene_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to regenerate scene: {str(e)}")


@router.delete("/{scene_id}", status_code=204)
async def delete_scene(
    scene_id: UUID,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Delete a specific scene
    """
    org_id, user_id, email = user_info

    try:
        # Verify scene exists and belongs to user through script
        stmt = (
            select(Scene)
            .join(Script, Scene.script_id == Script.id)
            .where(Scene.id == scene_id, Script.org_id == org_id, Script.user_id == UUID(user_id))
        )

        result = await session.execute(stmt)
        scene = result.scalar_one_or_none()

        if not scene:
            raise HTTPException(status_code=404, detail="Scene not found")

        # Delete scene
        await session.delete(scene)
        await session.commit()

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete scene: {str(e)}")
