"""
Script API endpoints with authentication
Handles script creation, retrieval, and regeneration
"""

# Global imports
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends, HTTPException, Query

# Local imports
from src.shared.core.dependencies import require_auth
from src.shared.config.database import get_database_session
from src.shared.models.database_models import Script, TextAsset
from src.script_service.services.script_service import generate_script_task
from src.shared.schemas.script import (
    ScriptCreateResponse,
    ScriptResponse,
    ScriptWithScenes,
    ScriptCreate,
    ScriptBase,
    SceneResponse,
)

router = APIRouter(prefix="/scripts", tags=["Script Management"])


@router.post("/", response_model=ScriptCreateResponse, status_code=202)
async def create_script(
    script_data: ScriptCreate,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, email = user_info
    try:
        script = Script(
            org_id=org_id,
            user_id=UUID(user_id),
            project_id=script_data.project_id,
            title=script_data.title,
            video_style=script_data.video_style,
            duration=script_data.duration,
            aspect_ratio=script_data.aspect_ratio,
            narration_type=script_data.narration_type,
            brand_name=script_data.brand_name,
            product_name=script_data.product_name,
            brand_description=script_data.brand_description,
            status="pending",
            generation_status="queued",
        )
        session.add(script)
        await session.flush()
        task_id = str(uuid4())
        from src.shared.models.database_models import TaskQueue

        task_queue = TaskQueue(
            task_id=task_id,
            task_type="script_generation",
            org_id=org_id,
            user_id=UUID(user_id),
            related_script_id=script.id,
            input_data={
                "text_input": script_data.text_input,
                "video_style": script_data.video_style,
                "duration": script_data.duration,
                "brand_name": script_data.brand_name,
                "product_name": script_data.product_name,
                "brand_description": script_data.brand_description,
            },
            queue_name="script_generation",
        )
        session.add(task_queue)
        await session.commit()

        # Start the Celery task with the task_id
        task = generate_script_task.apply_async(
            args=[str(script.id), task_queue.input_data], task_id=task_id
        )

        return ScriptCreateResponse(
            script_id=script.id,
            task_id=task_id,
            task_type="script_generation",
            org_id=org_id,
            user_id=UUID(user_id),
            status="pending",
            progress=0,
            created_at=task_queue.created_at,
        )
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create script: {str(e)}")


@router.get("/{script_id}", response_model=ScriptWithScenes)
async def get_script(
    script_id: UUID,
    user_info: tuple = Depends(require_auth),
    include_scenes: bool = Query(default=True, description="Include scenes in response"),
    session: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, email = user_info
    try:
        stmt = select(Script).where(
            Script.id == script_id, Script.org_id == org_id, Script.user_id == UUID(user_id)
        )
        result = await session.execute(stmt)
        script = result.scalar_one_or_none()
        if not script:
            raise HTTPException(status_code=404, detail="Script not found")
        content = None
        if script.text_asset_id:
            text_asset_stmt = select(TextAsset).where(TextAsset.asset_id == script.text_asset_id)
            text_asset_result = await session.execute(text_asset_stmt)
            text_asset = text_asset_result.scalar_one_or_none()
            if text_asset:
                content = text_asset.content
        script_response = ScriptResponse(
            id=script.id,
            org_id=script.org_id,
            user_id=script.user_id,
            project_id=script.project_id,
            text_asset_id=script.text_asset_id,
            title=script.title,
            video_style=script.video_style,
            duration=script.duration,
            aspect_ratio=script.aspect_ratio,
            narration_type=script.narration_type,
            brand_name=script.brand_name,
            product_name=script.product_name,
            brand_description=script.brand_description,
            content=content,
            status=script.status,
            generation_status=script.generation_status,
            error_message=script.error_message,
            created_at=script.created_at,
            updated_at=script.updated_at,
            completed_at=script.completed_at,
            metadata=dict(script.metadata) if isinstance(script.metadata, dict) else {},
        )
        scenes = []
        if include_scenes:
            from src.shared.models.database_models import Scene

            scenes_stmt = (
                select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
            )
            scenes_result = await session.execute(scenes_stmt)
            script_scenes = scenes_result.scalars().all()
            for scene in script_scenes:
                from src.script_service.schemas.script_schemas import SceneResponse

                scene_response = SceneResponse(
                    id=scene.id,
                    script_id=scene.script_id,
                    text_asset_id=scene.text_asset_id,
                    scene_number=scene.scene_number,
                    title=scene.title,
                    description=scene.description,
                    visual_description=scene.visual_description,
                    narration=scene.narration,
                    duration=scene.duration,
                    location_group=scene.location_group,
                    status=scene.status,
                    generation_status=scene.generation_status,
                    error_message=scene.error_message,
                    character_info=scene.character_info or {},
                    created_at=scene.created_at,
                    updated_at=scene.updated_at,
                    regenerated_at=scene.regenerated_at,
                    metadata=dict(scene.metadata) if isinstance(scene.metadata, dict) else {},
                )
                scenes.append(scene_response)
        return ScriptWithScenes(**script_response.dict(), scenes=scenes)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get script: {str(e)}")
