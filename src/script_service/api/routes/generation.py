"""
Unified Script and Scene Generation Generator API endpoints
Handles script creation, scene generation, and regeneration with professional advertisement framework
"""

# Global imports
from uuid import UUID, uuid4
from typing import List, Optional
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends, HTTPException, Query

# Local imports
from src.shared.core.dependencies import require_auth
from src.script_service.tasks import regenerate_scene_task
from src.shared.config.database import get_database_session
from src.shared.models.database_models import Script, TaskQueue, TextAsset, Scene
from src.script_service.utils.generators.scene_processor import SceneProcessor
from src.script_service.services.script_service import generate_script_task
from src.shared.schemas.script import (
    ScriptCreateResponse,
    ScriptResponse,
    ScriptWithScenes,
    ScriptCreate,
    ScriptBase,
    SceneResponse,
    SceneUpdate,
    SceneRegenerationRequest,
    TaskQueueResponse,
)

router = APIRouter(prefix="/generation", tags=["Script and Scene Generation Generator"])

# ============================================================================
# SCRIPT GENERATION ENDPOINTS
# ============================================================================


@router.post("/script", response_model=ScriptCreateResponse, status_code=202)
async def create_script(
    script_data: ScriptCreate,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Create a new script with professional advertisement generation
    """
    org_id, user_id, email = user_info
    try:
        script = Script(
            org_id=org_id,
            user_id=UUID(user_id),
            project_id=script_data.project_id,
            title=script_data.title,
            video_style=script_data.video_style,
            duration=script_data.duration,
            aspect_ratio=script_data.aspect_ratio,
            narration_type=script_data.narration_type,
            brand_name=script_data.brand_name,
            product_name=script_data.product_name,
            brand_description=script_data.brand_description,
            status="pending",
            generation_status="queued",
        )
        session.add(script)
        await session.flush()
        task_id = str(uuid4())
        task_queue = TaskQueue(
            task_id=task_id,
            task_type="script_generation",
            org_id=org_id,
            user_id=UUID(user_id),
            related_script_id=script.id,
            input_data={
                "text_input": script_data.text_input,
                "video_style": script_data.video_style,
                "duration": script_data.duration,
                "brand_name": script_data.brand_name,
                "product_name": script_data.product_name,
                "brand_description": script_data.brand_description,
            },
            queue_name="script_generation",
        )
        session.add(task_queue)
        await session.commit()

        # Start the Celery task with the task_id
        task = generate_script_task.apply_async(
            args=[str(script.id), task_queue.input_data], task_id=task_id
        )

        return ScriptCreateResponse(
            script_id=script.id,
            task_id=task_id,
            task_type="script_generation",
            org_id=org_id,
            user_id=UUID(user_id),
            status="pending",
            progress=0,
            created_at=task_queue.created_at,
        )
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create script: {str(e)}")


@router.get("/script/{script_id}", response_model=ScriptWithScenes)
async def get_script(
    script_id: UUID,
    user_info: tuple = Depends(require_auth),
    include_scenes: bool = Query(default=True, description="Include scenes in response"),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Get a specific script with its scenes
    """
    org_id, user_id, email = user_info
    try:
        stmt = select(Script).where(
            Script.id == script_id, Script.org_id == org_id, Script.user_id == UUID(user_id)
        )
        result = await session.execute(stmt)
        script = result.scalar_one_or_none()
        if not script:
            raise HTTPException(status_code=404, detail="Script not found")
        content = None
        if script.text_asset_id:
            text_asset_stmt = select(TextAsset).where(TextAsset.asset_id == script.text_asset_id)
            text_asset_result = await session.execute(text_asset_stmt)
            text_asset = text_asset_result.scalar_one_or_none()
            if text_asset:
                content = text_asset.content
        script_response = ScriptResponse(
            id=script.id,
            org_id=script.org_id,
            user_id=script.user_id,
            project_id=script.project_id,
            text_asset_id=script.text_asset_id,
            title=script.title,
            video_style=script.video_style,
            duration=script.duration,
            aspect_ratio=script.aspect_ratio,
            narration_type=script.narration_type,
            brand_name=script.brand_name,
            product_name=script.product_name,
            brand_description=script.brand_description,
            content=content,
            status=script.status,
            generation_status=script.generation_status,
            error_message=script.error_message,
            created_at=script.created_at,
            updated_at=script.updated_at,
            completed_at=script.completed_at,
            metadata=dict(script.metadata) if isinstance(script.metadata, dict) else {},
        )
        scenes = []
        if include_scenes:
            scenes_stmt = (
                select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
            )
            scenes_result = await session.execute(scenes_stmt)
            script_scenes = scenes_result.scalars().all()
            for scene in script_scenes:
                scene_response = SceneResponse(
                    id=scene.id,
                    script_id=scene.script_id,
                    text_asset_id=scene.text_asset_id,
                    scene_number=scene.scene_number,
                    title=scene.title,
                    description=scene.description,
                    visual_description=scene.visual_description,
                    narration=scene.narration,
                    duration=scene.duration,
                    location_group=scene.location_group,
                    status=scene.status,
                    generation_status=scene.generation_status,
                    error_message=scene.error_message,
                    character_info=scene.character_info or {},
                    created_at=scene.created_at,
                    updated_at=scene.updated_at,
                    regenerated_at=scene.regenerated_at,
                    metadata=dict(scene.metadata) if isinstance(scene.metadata, dict) else {},
                )
                scenes.append(scene_response)
        return ScriptWithScenes(**script_response.dict(), scenes=scenes)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get script: {str(e)}")


# ============================================================================
# SCENE GENERATION ENDPOINTS
# ============================================================================


@router.get("/scenes", response_model=dict)
async def list_scenes(
    script_id: UUID = Query(..., description="Script ID to filter scenes"),
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    List all scenes for a specific script
    """
    org_id, user_id, email = user_info
    try:
        # Verify script exists and belongs to user
        script_stmt = select(Script).where(
            Script.id == script_id, Script.org_id == org_id, Script.user_id == UUID(user_id)
        )
        script_result = await session.execute(script_stmt)
        script = script_result.scalar_one_or_none()
        if not script:
            raise HTTPException(status_code=404, detail="Script not found")

        # Fetch all scenes for this script from the database
        scenes_stmt = select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
        scenes_result = await session.execute(scenes_stmt)
        scenes = scenes_result.scalars().all()
        if not scenes:
            return {"scene_ids": [], "scenes": []}

        scene_ids = [str(scene.id) for scene in scenes]
        scene_dicts = [
            {
                "id": str(scene.id),
                "scene_number": scene.scene_number,
                "title": scene.title,
                "description": scene.description,
                "visual": scene.visual_description,
                "narration": scene.narration,
                "duration": scene.duration,
                "location_group": scene.location_group,
                "status": scene.status,
                "generation_status": scene.generation_status,
                "full_content": scene.description,  # or another field if you want
            }
            for scene in scenes
        ]
        return {"scene_ids": scene_ids, "scenes": scene_dicts}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list scenes: {str(e)}")


@router.put("/scene/{scene_id}", response_model=SceneResponse)
async def update_scene(
    scene_id: UUID,
    scene_update: SceneUpdate,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Update a scene manually (direct edit)
    """
    org_id, user_id, email = user_info

    try:
        # Get existing scene with ownership check
        stmt = (
            select(Scene)
            .join(Script, Scene.script_id == Script.id)
            .where(Scene.id == scene_id, Script.org_id == org_id, Script.user_id == UUID(user_id))
        )

        result = await session.execute(stmt)
        scene = result.scalar_one_or_none()

        if not scene:
            raise HTTPException(status_code=404, detail="Scene not found")

        # Update fields that are provided
        update_data = scene_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(scene, field):
                setattr(scene, field, value)

        await session.commit()
        await session.refresh(scene)

        return SceneResponse(
            id=scene.id,
            script_id=scene.script_id,
            text_asset_id=scene.text_asset_id,
            scene_number=scene.scene_number,
            title=scene.title,
            description=scene.description,
            visual_description=scene.visual_description,
            narration=scene.narration,
            duration=scene.duration,
            location_group=scene.location_group,
            status=scene.status,
            generation_status=scene.generation_status,
            error_message=scene.error_message,
            character_info=scene.character_info or {},
            created_at=scene.created_at,
            updated_at=scene.updated_at,
            regenerated_at=scene.regenerated_at,
            metadata=dict(scene.metadata) if isinstance(scene.metadata, dict) else {},
        )

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update scene: {str(e)}")


# Scene regeneration endpoint is a duplicate endpoint in scenes.py


@router.post("/scene/{scene_id}/regenerate", response_model=TaskQueueResponse, status_code=202)
async def regenerate_scene(
    scene_id: UUID,
    regeneration_request: SceneRegenerationRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Regenerate a scene based on feedback

    Returns task information for tracking progress
    """
    org_id, user_id, email = user_info

    try:
        # Verify scene exists and belongs to user through script
        stmt = (
            select(Scene)
            .join(Script, Scene.script_id == Script.id)
            .where(Scene.id == scene_id, Script.org_id == org_id, Script.user_id == UUID(user_id))
        )

        result = await session.execute(stmt)
        scene = result.scalar_one_or_none()

        if not scene:
            raise HTTPException(status_code=404, detail="Scene not found")

        # Create task queue entry
        task_id = str(uuid4())
        task_queue = TaskQueue(
            task_id=task_id,
            task_type="scene_regeneration",
            org_id=org_id,
            user_id=UUID(user_id),
            related_scene_id=scene.id,
            input_data={"feedback": regeneration_request.feedback},
            queue_name="scene_processing",
        )

        session.add(task_queue)
        await session.commit()

        # Start background task
        regenerate_scene_task.apply_async(
            args=[str(scene_id), regeneration_request.feedback], task_id=task_id
        )

        return TaskQueueResponse(
            task_id=task_id,
            task_type="scene_regeneration",
            org_id=org_id,
            user_id=UUID(user_id),
            status="pending",
            progress=0,
            created_at=task_queue.created_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to regenerate scene: {str(e)}")


# ============================================================================
# async def delete_scene(
#     scene_id: UUID,
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session)
# ):
#     """
#     Delete a specific scene
#     """
#     org_id, user_id, email = user_info

#     try:
#         # Get scene with ownership check
#         stmt = (
#             select(Scene)
#             .join(Script, Scene.script_id == Script.id)
#             .where(
#                 Scene.id == scene_id,
#                 Script.org_id == org_id,
#                 Script.user_id == UUID(user_id)
#             )
#         )

#         result = await session.execute(stmt)
#         scene = result.scalar_one_or_none()

#         if not scene:
#             raise HTTPException(status_code=404, detail="Scene not found")

#         await session.delete(scene)
#         await session.commit()

#     except HTTPException:
#         raise
#     except Exception as e:
#         await session.rollback()
#         raise HTTPException(status_code=500, detail=f"Failed to delete scene: {str(e)}")

# ============================================================================
# TASK MANAGEMENT ENDPOINTS
# ============================================================================

# @router.get("/task/{task_id}", response_model=TaskQueueResponse)
# async def get_task_status(
#     task_id: str,
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session)
# ):
#     """
#     Get the status of a specific task
#     """
#     org_id, user_id, email = user_info

#     try:
#         stmt = select(TaskQueue).where(
#             TaskQueue.task_id == task_id,
#             TaskQueue.org_id == org_id,
#             TaskQueue.user_id == UUID(user_id)
#         )

#         result = await session.execute(stmt)
#         task = result.scalar_one_or_none()

#         if not task:
#             raise HTTPException(status_code=404, detail="Task not found")

#         return TaskQueueResponse(
#             task_id=task.task_id,
#             task_type=task.task_type,
#             org_id=task.org_id,
#             user_id=task.user_id,
#             status=task.status,
#             progress=task.progress,
#             result=task.result,
#             error_message=task.error_message,
#             created_at=task.created_at,
#             started_at=task.started_at,
#             completed_at=task.completed_at
#         )

#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to get task status: {str(e)}")

# @router.get("/tasks", response_model=List[TaskQueueResponse])
# async def list_tasks(
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session),
#     task_type: Optional[str] = Query(None, description="Filter by task type"),
#     status: Optional[str] = Query(None, description="Filter by status"),
#     limit: int = Query(50, ge=1, le=100, description="Number of tasks to return"),
#     offset: int = Query(0, ge=0, description="Number of tasks to skip")
# ):
#     """
#     List all tasks for the current user with optional filtering
#     """
#     org_id, user_id, email = user_info

#     try:
#         # Build query with filters
#         stmt = select(TaskQueue).where(
#             TaskQueue.org_id == org_id,
#             TaskQueue.user_id == UUID(user_id)
#         )

#         if task_type:
#             stmt = stmt.where(TaskQueue.task_type == task_type)

#         if status:
#             stmt = stmt.where(TaskQueue.status == status)

#         # Order by creation date (newest first) and apply pagination
#         stmt = stmt.order_by(TaskQueue.created_at.desc()).offset(offset).limit(limit)

#         result = await session.execute(stmt)
#         tasks = result.scalars().all()

#         return [
#             TaskQueueResponse(
#                 task_id=task.task_id,
#                 task_type=task.task_type,
#                 org_id=task.org_id,
#                 user_id=task.user_id,
#                 status=task.status,
#                 progress=task.progress,
#                 result=task.result,
#                 error_message=task.error_message,
#                 created_at=task.created_at,
#                 started_at=task.started_at,
#                 completed_at=task.completed_at
#             )
#             for task in tasks
#         ]

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to list tasks: {str(e)}")
