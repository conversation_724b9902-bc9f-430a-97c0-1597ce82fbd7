"""
Shared script and scene Pydantic schemas for VidFlux
"""

# Global imports
from uuid import UUID
from enum import Enum
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ScriptBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    video_style: str = Field(..., min_length=1, max_length=100)
    duration: str = Field(..., min_length=1, max_length=50)
    aspect_ratio: str = Field(default="16:9", max_length=20)
    narration_type: str = Field(default="voice-based", max_length=50)
    brand_name: Optional[str] = Field(None, max_length=255)
    product_name: Optional[str] = Field(None, max_length=255)
    brand_description: Optional[str] = None


class ScriptResponse(ScriptBase):
    id: UUID
    org_id: str
    user_id: UUID
    project_id: Optional[UUID] = None
    text_asset_id: Optional[str] = None
    content: Optional[str] = None
    status: str
    generation_status: str
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = {}

    class Config:
        from_attributes = True


class SceneBase(BaseModel):
    scene_number: int = Field(..., ge=1)
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    visual_description: str = Field(..., min_length=1)
    narration: str = Field(..., min_length=1)
    duration: str = Field(default="5s", max_length=50)
    location_group: Optional[int] = Field(None, ge=0)


class SceneResponse(SceneBase):
    id: UUID
    script_id: UUID
    text_asset_id: Optional[str] = None
    status: str
    generation_status: str
    error_message: Optional[str] = None
    character_info: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime
    regenerated_at: Optional[datetime] = None
    metadata: Dict[str, Any] = {}

    class Config:
        from_attributes = True


class ScriptWithScenes(ScriptResponse):
    scenes: List[SceneResponse] = []


class ScriptListResponse(BaseModel):
    items: List[ScriptResponse]
    total: int
    page: int
    size: int
    pages: int


class ScriptCreate(ScriptBase):
    """Script creation schema"""

    text_input: str = Field(..., min_length=1, description="Video idea or concept")
    project_id: Optional[UUID] = Field(None, description="Optional project association")

    @validator("duration")
    def validate_duration(cls, v):
        valid_durations = ["15 seconds", "30 seconds", "1 minute", "2 minutes", "3 minutes"]
        if v not in valid_durations:
            raise ValueError(f'Duration must be one of: {", ".join(valid_durations)}')
        return v


class ScriptCreateResponse(BaseModel):
    script_id: UUID
    task_id: str
    task_type: str
    org_id: str
    user_id: UUID
    status: TaskStatus
    progress: int = Field(..., ge=0, le=100)
    created_at: datetime

    class Config:
        from_attributes = True


class FormattedSceneResponse(BaseModel):
    formatted_text: str = Field(..., description="Formatted scene text in the requested format")


class SceneUpdate(BaseModel):
    """Scene update schema"""

    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    visual_description: Optional[str] = Field(None, min_length=1)
    narration: Optional[str] = Field(None, min_length=1)
    duration: Optional[str] = Field(None, max_length=50)


class SceneRegenerationRequest(BaseModel):
    """Scene regeneration request schema"""

    feedback: str = Field(..., min_length=1, description="Feedback for scene improvement")


class TaskQueueResponse(BaseModel):
    id: UUID
    task_id: str
    task_type: str
    org_id: str
    user_id: UUID
    status: TaskStatus
    progress: int = Field(..., ge=0, le=100)
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PaginationParams(BaseModel):
    page: int = Field(1, ge=1)
    size: int = Field(10, ge=1, le=100)
