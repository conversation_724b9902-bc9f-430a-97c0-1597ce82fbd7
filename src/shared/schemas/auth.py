# Global imports
from typing import Optional
from datetime import datetime
from pydantic import BaseModel


class UserResponse(BaseModel):
    org_id: str
    user_id: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime

    class Config:
        from_attributes = True
