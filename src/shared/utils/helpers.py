"""
Utility helper functions
"""

# Global imports
import re
import uuid
import secrets
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any


def generate_short_uuid() -> str:
    """Generate a short UUID for IDs"""
    return uuid.uuid4().hex[:12]


def generate_asset_id(asset_type: str) -> str:
    """Generate an asset ID with type prefix"""
    return f"{asset_type}-{generate_short_uuid()}"


def generate_org_id() -> str:
    """Generate an organization ID"""
    return f"org-{uuid.uuid4().hex[:8]}"


def generate_secure_token(length: int = 32) -> str:
    """Generate a secure random token"""
    return secrets.token_urlsafe(length)


def validate_email(email: str) -> bool:
    """Validate email format"""
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None


def get_email_domain(email: str) -> str:
    """Extract domain from email address"""
    return email.split("@")[-1].lower()


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace unsafe characters
    filename = re.sub(r"[^\w\s.-]", "", filename)
    filename = re.sub(r"[-\s]+", "-", filename)
    return filename.strip("-")


def parse_duration_to_seconds(duration: str) -> int:
    """Parse duration string to seconds"""
    duration = duration.lower().strip()

    if "second" in duration:
        return int(re.findall(r"\d+", duration)[0])
    elif "minute" in duration:
        minutes = int(re.findall(r"\d+", duration)[0])
        return minutes * 60
    elif "hour" in duration:
        hours = int(re.findall(r"\d+", duration)[0])
        return hours * 3600
    else:
        # Default to seconds if no unit specified
        numbers = re.findall(r"\d+", duration)
        return int(numbers[0]) if numbers else 30


def format_duration_from_seconds(seconds: int) -> str:
    """Format seconds to duration string"""
    if seconds < 60:
        return f"{seconds} seconds"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes} minute{'s' if minutes != 1 else ''}"
    else:
        hours = seconds // 3600
        return f"{hours} hour{'s' if hours != 1 else ''}"


def utc_now() -> datetime:
    """Get current UTC datetime"""
    return datetime.now(timezone.utc)


def paginate_query_params(page: int = 1, size: int = 20) -> Dict[str, int]:
    """Calculate pagination parameters"""
    offset = (page - 1) * size
    return {"offset": offset, "limit": size}


def calculate_pagination_info(total: int, page: int, size: int) -> Dict[str, Any]:
    """Calculate pagination information"""
    pages = (total + size - 1) // size
    has_next = page < pages
    has_prev = page > 1

    return {
        "total": total,
        "page": page,
        "size": size,
        "pages": pages,
        "has_next": has_next,
        "has_prev": has_prev,
    }


def extract_text_preview(text: str, max_length: int = 150) -> str:
    """Extract preview text with ellipsis if too long"""
    if len(text) <= max_length:
        return text

    # Try to break at word boundary
    preview = text[:max_length]
    last_space = preview.rfind(" ")

    if last_space > max_length * 0.8:  # If we can break reasonably close to limit
        preview = preview[:last_space]

    return preview + "..."


def merge_metadata(existing: Optional[Dict], new_data: Dict) -> Dict:
    """Merge metadata dictionaries"""
    if not existing:
        return new_data.copy()

    merged = existing.copy()
    merged.update(new_data)
    return merged


def clean_html_tags(text: str) -> str:
    """Remove HTML tags from text"""
    return re.sub(r"<[^>]+>", "", text)


def normalize_text(text: str) -> str:
    """Normalize text by removing extra whitespace and cleaning"""
    # Remove extra whitespace
    text = " ".join(text.split())

    # Remove HTML tags
    text = clean_html_tags(text)

    return text.strip()


def is_valid_uuid(uuid_string: str) -> bool:
    """Check if string is a valid UUID"""
    try:
        uuid.UUID(uuid_string)
        return True
    except ValueError:
        return False


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """Mask sensitive data showing only first few characters"""
    if len(data) <= visible_chars:
        return mask_char * len(data)

    return data[:visible_chars] + mask_char * (len(data) - visible_chars)
