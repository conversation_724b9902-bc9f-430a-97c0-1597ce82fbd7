"""
Utility functions for cleaning and formatting AI-generated prompts for frontend display
"""

# Global imports
import re
from typing import Optional


def clean_prompt_for_frontend(prompt: Optional[str]) -> Optional[str]:
    """
    Clean and format AI-generated prompts for better frontend display.

    Args:
        prompt: The raw AI-generated prompt

    Returns:
        Cleaned prompt suitable for frontend display, or None if input is None/empty
    """
    if not prompt or not prompt.strip():
        return None

    # Remove common AI disclaimers and meta-instructions
    disclaimers_to_remove = [
        r"This is a description for a photographer or videographer.*?(?=\n\n|\Z)",
        r"I can't create images.*?However,.*?(?=`)",  # Modified to preserve content after "However"
        r"I am a text-based AI.*?(?=\n\n|\Z)",
        r"To achieve this shot, they would need:.*?(?=\n\n|\Z)",
        r"Here's a breakdown of the steps involved.*?(?=\n\n|\Z)",
        r"This is a complex task requiring.*?(?=\n\n|\Z)",
        r"In short, this is a complex task.*?(?=\Z)",
        r"This response provides the technical steps.*?(?=\Z)",
        r"Remember to experiment with different AI art generators.*?(?=\Z)",
        r"However, I can provide you with a detailed text prompt.*?(?=`)",  # Modified
        r"\*\*Technical Notes:\*\*.*?(?=\Z)",
        r"## Shot Description:.*?\n\n",
        r"Okay, here's a shot description.*?\n\n",
        r"This detailed description ensures.*?(?=\Z)",  # Remove concluding meta-text
    ]

    cleaned_prompt = prompt
    for pattern in disclaimers_to_remove:
        cleaned_prompt = re.sub(pattern, "", cleaned_prompt, flags=re.DOTALL | re.IGNORECASE)

    # Extract Midjourney-style prompts and clean them
    midjourney_match = re.search(r"`/imagine\s+(.*?)`", cleaned_prompt, flags=re.DOTALL)
    if midjourney_match:
        midjourney_content = midjourney_match.group(1).strip()
        # Remove technical parameters like --ar 16:9
        midjourney_content = re.sub(r"\s+--\w+\s+[\w:]+", "", midjourney_content)
        cleaned_prompt = midjourney_content

    # Remove bullet point lists for equipment/technical requirements
    equipment_patterns = [
        r"\* \*\*.*?\*\*:.*?(?=\n\* |\n\n|\Z)",
        r"• .*?(?=\n• |\n\n|\Z)",
        r"\d+\. \*\*.*?\*\*.*?(?=\n\d+\.|\n\n|\Z)",
    ]

    for pattern in equipment_patterns:
        cleaned_prompt = re.sub(pattern, "", cleaned_prompt, flags=re.DOTALL)

    # Try to extract useful content from structured descriptions FIRST
    if "**" in cleaned_prompt and "Audi Q8 e-tron" in cleaned_prompt:
        # Look for the most descriptive sections
        structured_patterns = [
            r"\*\*Lighting:\*\*\s*(.*?)(?=\n\*\*|\n\n|\Z)",
            r"\*\*Vehicle:\*\*\s*(.*?)(?=\n\*\*|\n\n|\Z)",
            r"\*\*Background:\*\*\s*(.*?)(?=\n\*\*|\n\n|\Z)",
            r"\*\*Character:\*\*\s*(.*?)(?=\n\*\*|\n\n|\Z)",
            r"\*\*Framing:\*\*\s*(.*?)(?=\n\*\*|\n\n|\Z)",
        ]

        extracted_parts = []
        for pattern in structured_patterns:
            match = re.search(pattern, cleaned_prompt, flags=re.DOTALL | re.IGNORECASE)
            if match:
                content = match.group(1).strip()
                if len(content) > 20:  # Only include substantial content
                    extracted_parts.append(content)

        if extracted_parts:
            # Combine the best parts into a coherent description
            combined = " ".join(extracted_parts)
            if len(combined) > 100:
                cleaned_prompt = combined

    # Only AFTER trying structured extraction, remove the structured headers
    if "**" in cleaned_prompt:
        header_removal_patterns = [
            r"\*\*Shot Type:\*\*.*?\n",
            r"\*\*Framing:\*\*.*?\n",
            r"\*\*Camera Movement:\*\*.*?\n",
            r"\*\*Sound:\*\*.*?\n",
            r"\*\*Lighting:\*\*.*?\n",
            r"\*\*Vehicle:\*\*.*?\n",
            r"\*\*Character:\*\*.*?\n",
            r"\*\*Background:\*\*.*?\n",
            r"\*\*Shot:\*\*.*?\n",
            r"\*\*Overall Mood:\*\*.*?\n",
            r"\*\*Composition:\*\*.*?\n",
        ]

        for pattern in header_removal_patterns:
            cleaned_prompt = re.sub(pattern, "", cleaned_prompt, flags=re.DOTALL | re.IGNORECASE)

    # Extract the best descriptive parts - look for vivid scene descriptions
    scene_description_patterns = [
        r"(The sun.*?(?=\n\n|\Z))",  # Scenes starting with "The sun"
        r"(The image should show.*?(?=\n\n|\Z))",  # Direct image descriptions
        r"(A close-up shot of.*?Audi.*?(?=\n\n|\Z))",  # Close-up descriptions
        r"(.*?Audi Q8 e-tron.*?shadows.*?(?=\n\n|\Z))",  # Rich descriptions with lighting
        r"(.*?warm.*?light.*?Audi.*?(?=\n\n|\Z))",  # Warm lighting descriptions
        r"(.*?Audi Q8 e-tron.*?accelerat.*?(?=\n\n|\Z))",  # Acceleration descriptions
        r"(.*?Audi Q8 e-tron.*?traffic.*?(?=\n\n|\Z))",  # Traffic scene descriptions
        r"(.*?touchscreen.*?display.*?(?=\n\n|\Z))",  # Touchscreen descriptions
        r"(.*?navigation.*?Copenhagen.*?(?=\n\n|\Z))",  # Navigation descriptions
    ]

    best_description = None
    for pattern in scene_description_patterns:
        match = re.search(pattern, cleaned_prompt, flags=re.DOTALL | re.IGNORECASE)
        if match:
            candidate = match.group(1).strip()
            # Prefer longer, more descriptive matches that mention the car
            if not best_description or (
                len(candidate) > len(best_description)
                and ("audi" in candidate.lower() or "car" in candidate.lower())
            ):
                best_description = candidate

    # If no good scene description found, try to extract any Audi-related content
    if not best_description:
        audi_patterns = [
            r"(.*?Audi.*?(?=\n|\Z))",  # Any line with Audi
            r"(.*?touchscreen.*?(?=\n|\Z))",  # Touchscreen content
            r"(.*?display.*?(?=\n|\Z))",  # Display content
        ]

        for pattern in audi_patterns:
            matches = re.findall(pattern, cleaned_prompt, flags=re.DOTALL | re.IGNORECASE)
            if matches:
                # Take the longest match
                best_match = max(matches, key=len)
                if len(best_match.strip()) > 30:
                    best_description = best_match.strip()
                    break

    # If we found a good description, use it
    if best_description:
        cleaned_prompt = best_description

    # Clean up formatting
    cleaned_prompt = re.sub(r"\n+", " ", cleaned_prompt)  # Replace newlines with spaces
    cleaned_prompt = re.sub(
        r"\s+", " ", cleaned_prompt
    )  # Replace multiple spaces with single space
    cleaned_prompt = re.sub(r"\*\*([^*]+)\*\*", r"\1", cleaned_prompt)  # Remove markdown bold
    cleaned_prompt = re.sub(r"#{1,6}\s*", "", cleaned_prompt)  # Remove markdown headers
    cleaned_prompt = cleaned_prompt.strip()

    # If the result contains unhelpful text, return a generic message
    # Note: Removed length check to allow short user-provided prompts
    unhelpful_indicators = [
        "would need to use",
        "you would need",
        "this is a description for",
        "i can't create",
        "text-based ai",
        "suitable for input into",
    ]

    # Only check for unhelpful content, not length
    if any(indicator in cleaned_prompt.lower() for indicator in unhelpful_indicators):
        return "A cinematic scene featuring the Audi Q8 e-tron with professional lighting and composition."

    # Only truncate if extremely long (keep most content intact)
    if len(cleaned_prompt) > 800:
        # Find a good break point near the end (sentence or clause)
        truncate_point = 750
        last_period = cleaned_prompt.rfind(".", 600, truncate_point)
        last_comma = cleaned_prompt.rfind(",", 600, truncate_point)

        if last_period > 600:
            cleaned_prompt = cleaned_prompt[: last_period + 1]
        elif last_comma > 600:
            cleaned_prompt = cleaned_prompt[: last_comma + 1]
        else:
            # Fallback to hard truncation only if no good break point
            cleaned_prompt = cleaned_prompt[:750] + "..."

    return cleaned_prompt


def format_prompt_for_display(prompt: Optional[str]) -> str:
    """
    Format prompt for frontend display with fallback.

    Args:
        prompt: The raw AI-generated prompt

    Returns:
        Formatted prompt string (never None)
    """
    cleaned = clean_prompt_for_frontend(prompt)
    return cleaned or "Professional image composition featuring the Audi Q8 e-tron."


def format_video_prompt_for_display(prompt: Optional[str]) -> str:
    """
    Format video prompt for frontend display with specific video-related cleaning.

    Args:
        prompt: The raw AI-generated video prompt

    Returns:
        Formatted video prompt string (never None)
    """
    if not prompt or not prompt.strip():
        return "Professional cinematic video featuring the Audi Q8 e-tron."

    cleaned_prompt = prompt.strip()

    # Remove video-specific technical instructions
    video_disclaimers = [
        r"This is optimized for.*?(?=\n\n|\Z)",
        r"Camera smoothly.*?(?=\n\n|\.)",
        r"Slow, steady.*?(?=\n\n|\.)",
        r"Ensure accurate.*?(?=\n\n|\.)",
        r"maintaining focus.*?(?=\n\n|\.)",
        r"Professional camera.*?(?=\n\n|\.)",
        r"cinematic depth of field.*?(?=\n\n|\.)",
    ]

    for pattern in video_disclaimers:
        cleaned_prompt = re.sub(pattern, "", cleaned_prompt, flags=re.DOTALL | re.IGNORECASE)

    # Extract the main scene description (before camera instructions)
    # Split by common separators and take the most descriptive parts
    sentences = re.split(r"[.!]\s+", cleaned_prompt)

    # Filter for the most descriptive sentences
    descriptive_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()

        # Skip technical camera instructions
        skip_patterns = [
            r"camera\s+(smoothly|slowly|steadily)",
            r"zoom\s+(out|in)",
            r"maintain\s+focus",
            r"ensure\s+accurate",
            r"cinematic\s+depth",
            r"slow,?\s+steady",
        ]

        if any(re.search(pattern, sentence, re.IGNORECASE) for pattern in skip_patterns):
            continue

        # Keep sentences with good content
        if len(sentence) > 20 and (
            "audi" in sentence.lower()
            or "character" in sentence.lower()
            or "lighting" in sentence.lower()
            or "background" in sentence.lower()
            or "scene" in sentence.lower()
        ):
            descriptive_sentences.append(sentence)

    # Reconstruct from the best sentences
    if descriptive_sentences:
        cleaned_prompt = ". ".join(descriptive_sentences[:3])  # Take first 3 good sentences
        if not cleaned_prompt.endswith("."):
            cleaned_prompt += "."

    # Clean up formatting
    cleaned_prompt = re.sub(r"\n+", " ", cleaned_prompt)  # Replace newlines with spaces
    cleaned_prompt = re.sub(
        r"\s+", " ", cleaned_prompt
    )  # Replace multiple spaces with single space
    cleaned_prompt = cleaned_prompt.strip()

    # Ensure reasonable length
    if len(cleaned_prompt) > 350:
        # Find a good break point
        truncate_point = 320
        last_period = cleaned_prompt.rfind(".", 250, truncate_point)

        if last_period > 250:
            cleaned_prompt = cleaned_prompt[: last_period + 1]
        else:
            cleaned_prompt = cleaned_prompt[:320] + "..."

    # Fallback if too short or unhelpful
    if len(cleaned_prompt) < 30:
        return "Professional cinematic video featuring the Audi Q8 e-tron with dynamic lighting and composition."

    return cleaned_prompt


def format_bgsound_prompt_for_display(prompt: Optional[str]) -> Optional[str]:
    """
    Format background sound prompts for frontend display.
    Background sound prompts are typically very short (1-4 words) so minimal cleaning needed.

    Args:
        prompt: The raw background sound prompt

    Returns:
        Cleaned prompt suitable for frontend display, or None if input is None/empty
    """
    if not prompt or not prompt.strip():
        return None

    # Clean basic formatting
    cleaned_prompt = prompt.strip().lower()

    # Remove quotes and extra formatting
    cleaned_prompt = cleaned_prompt.strip("\"'`")

    # Remove common prefixes that might be added by AI
    prefixes_to_remove = ["the sound of ", "sound of ", "ambient ", "background ", "audio of "]

    for prefix in prefixes_to_remove:
        if cleaned_prompt.startswith(prefix):
            cleaned_prompt = cleaned_prompt[len(prefix) :]

    # Ensure it's not too long (background sounds should be short)
    words = cleaned_prompt.split()
    if len(words) > 6:  # Allow up to 6 words for display
        cleaned_prompt = " ".join(words[:6])

    # Capitalize first letter for display
    if cleaned_prompt:
        cleaned_prompt = cleaned_prompt[0].upper() + cleaned_prompt[1:]

    # Fallback if empty
    if not cleaned_prompt or len(cleaned_prompt) < 2:
        return "Ambient sound"

    return cleaned_prompt


def format_audio_prompt_for_display(prompt: Optional[str]) -> str:
    """
    Format audio prompts for frontend display with fallback.
    Audio prompts are typically short descriptions for background sounds.

    Args:
        prompt: The raw AI-generated audio prompt

    Returns:
        Formatted audio prompt string (never None)
    """
    if not prompt or not prompt.strip():
        return "Ambient background sound"

    cleaned_prompt = prompt.strip()

    # Remove common audio-specific prefixes
    audio_prefixes = [
        "the sound of ",
        "sound of ",
        "ambient ",
        "background ",
        "audio of ",
        "sounds of ",
    ]

    cleaned_lower = cleaned_prompt.lower()
    for prefix in audio_prefixes:
        if cleaned_lower.startswith(prefix):
            cleaned_prompt = cleaned_prompt[len(prefix) :]
            break

    # Clean up formatting
    cleaned_prompt = re.sub(r"\n+", " ", cleaned_prompt)  # Replace newlines with spaces
    cleaned_prompt = re.sub(
        r"\s+", " ", cleaned_prompt
    )  # Replace multiple spaces with single space
    cleaned_prompt = re.sub(r"\*\*([^*]+)\*\*", r"\1", cleaned_prompt)  # Remove markdown bold
    cleaned_prompt = cleaned_prompt.strip()

    # Ensure reasonable length for audio prompts
    words = cleaned_prompt.split()
    if len(words) > 8:  # Keep audio prompts concise
        cleaned_prompt = " ".join(words[:8])

    # Capitalize first letter
    if cleaned_prompt:
        cleaned_prompt = cleaned_prompt[0].upper() + cleaned_prompt[1:]

    # Fallback for empty or very short prompts
    if not cleaned_prompt or len(cleaned_prompt) < 3:
        return "Ambient background sound"

    return cleaned_prompt
