# Global imports
import os
import boto3
import logging
from typing import Optional
from fastapi import UploadFile
from datetime import timedelta
from botocore.exceptions import ClientError


class S3Client:
    def __init__(self, aws_region: str):
        self.s3_client = boto3.client("s3", region_name=aws_region)
        self.region = aws_region
        self.logger = logging.getLogger(__name__)

    def upload_file(
        self,
        bucket: str,
        key: str,
        file: Optional[UploadFile] = None,
        file_path: Optional[str] = None,
    ) -> str:
        import time

        if not file and not file_path:
            self.logger.error("No file or file_path provided for upload.")
            raise ValueError("Input is neither local file nor UploadFile")
        # Read file bytes
        if file:
            file.file.seek(0)
            file_content = file.file.read()
            content_type = file.content_type or "application/octet-stream"
            file_size = len(file_content)
            self.logger.info(
                f"Preparing to upload UploadFile to S3: bucket={bucket}, key={key}, size={file_size} bytes"
            )
        else:
            if not os.path.exists(file_path):
                self.logger.error(f"File to upload does not exist: {file_path}")
                raise FileNotFoundError(f"File to upload does not exist: {file_path}")
            file_size = os.path.getsize(file_path)
            self.logger.info(
                f"Preparing to upload file to S3: bucket={bucket}, key={key}, path={file_path}, size={file_size} bytes"
            )
            with open(file_path, "rb") as f:
                file_content = f.read()
            content_type = "application/octet-stream"  # or guess from mimetypes
        # Check for empty file
        if file_size == 0:
            self.logger.error(
                f"Refusing to upload empty file to S3: bucket={bucket}, key={key}, path={file_path if file_path else 'UploadFile'}"
            )
            raise ValueError("Refusing to upload empty file to S3.")
        put_args = {
            "Bucket": bucket,
            "Key": key,
            "Body": file_content,
            "ContentType": content_type,
            "ServerSideEncryption": "AES256",
            "ContentDisposition": "attachment",
        }
        upload_success = False
        last_exception = None
        for attempt in range(2):
            try:
                self.logger.info(
                    f"Attempt {attempt+1}: Uploading to S3: bucket={bucket}, key={key}, size={file_size} bytes"
                )
                self.s3_client.put_object(**put_args)
                self.logger.info(
                    f"Successfully uploaded file to S3: bucket={bucket}, key={key}, size={file_size} bytes"
                )
                upload_success = True
                break
            except Exception as e:
                self.logger.error(
                    f"Attempt {attempt+1} failed: Could not upload file to S3: bucket={bucket}, key={key}, error={str(e)}",
                    exc_info=True,
                )
                last_exception = e
                if attempt == 0:
                    self.logger.info("Retrying S3 upload after 2 seconds...")
                    time.sleep(2)
        if not upload_success:
            raise RuntimeError(
                f"Could not upload file to S3 after 2 attempts: {str(last_exception)}"
            )
        return f"https://{bucket}.s3.{self.region}.amazonaws.com/{key}"

    def download_file(self, bucket: str, key: str, local_path: Optional[str] = None) -> str:
        if local_path is None:
            local_path = f"/tmp/{uuid.uuid4().hex}"
        try:
            with open(local_path, "wb") as f:
                self.s3_client.download_fileobj(bucket, key, f)
        except ClientError as e:
            if os.path.exists(local_path):
                os.remove(local_path)
            raise RuntimeError(f"Error downloading file: {str(e)}")
        return local_path

    def get_presigned_url(self, bucket: str, key: str, ttl: timedelta) -> str:
        try:
            url = self.s3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params={"Bucket": bucket, "Key": key},
                ExpiresIn=int(ttl.total_seconds()),
            )
            print("bucket", bucket)
            print("key", key)
        except ClientError as e:
            raise RuntimeError(f"Could not generate presigned URL: {str(e)}")
        return url

    def delete_file(self, bucket: str, key: str) -> bool:
        """
        Delete a file from S3
        Returns True if successful, False if file doesn't exist, raises exception for other errors
        """
        try:
            self.s3_client.delete_object(Bucket=bucket, Key=key)
            self.logger.info(f"Successfully deleted file from S3: bucket={bucket}, key={key}")
            return True
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "NoSuchKey":
                self.logger.warning(f"File not found in S3: bucket={bucket}, key={key}")
                return False
            else:
                self.logger.error(
                    f"Error deleting file from S3: bucket={bucket}, key={key}, error={str(e)}"
                )
                raise RuntimeError(f"Could not delete file from S3: {str(e)}")

    def delete_files_by_prefix(self, bucket: str, prefix: str) -> int:
        """
        Delete all files in S3 with the given prefix
        Returns the number of files deleted
        """
        try:
            # List objects with the prefix
            paginator = self.s3_client.get_paginator("list_objects_v2")
            pages = paginator.paginate(Bucket=bucket, Prefix=prefix)

            deleted_count = 0
            for page in pages:
                if "Contents" in page:
                    objects_to_delete = [{"Key": obj["Key"]} for obj in page["Contents"]]
                    if objects_to_delete:
                        response = self.s3_client.delete_objects(
                            Bucket=bucket, Delete={"Objects": objects_to_delete}
                        )
                        deleted_count += len(objects_to_delete)
                        self.logger.info(
                            f"Deleted {len(objects_to_delete)} files with prefix {prefix}"
                        )

            return deleted_count
        except ClientError as e:
            self.logger.error(
                f"Error deleting files with prefix from S3: bucket={bucket}, prefix={prefix}, error={str(e)}"
            )
            raise RuntimeError(f"Could not delete files with prefix from S3: {str(e)}")
