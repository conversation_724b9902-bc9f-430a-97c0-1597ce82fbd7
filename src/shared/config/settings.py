"""
Application settings using Pydantic Settings v2
Centralized configuration management with environment variable support
"""

# Global imports
import os
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with validation and environment variable support"""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # Application settings
    app_name: str = Field(default="VidFlux Backend", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    api_v1_str: str = Field(default="/api/v1", description="API v1 prefix")
    secret_key: str = Field(..., description="Secret key for JWT and encryption")

    # Database settings
    database_url: str = Field(..., description="Async database URL")
    database_url_sync: str = Field(..., description="Sync database URL for migrations")

    # Database pool settings
    db_pool_size: int = Field(default=10, description="Database connection pool size")
    db_max_overflow: int = Field(default=20, description="Database connection pool max overflow")
    db_pool_timeout: int = Field(default=30, description="Database connection pool timeout")
    db_pool_recycle: int = Field(default=1800, description="Database connection pool recycle time")

    # Redis settings
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis URL")

    # Celery settings
    celery_broker_url: str = Field(
        default="redis://localhost:6379/0", description="Celery broker URL"
    )
    celery_result_backend: str = Field(
        default="redis://localhost:6379/0", description="Celery result backend URL"
    )
    celery_worker_concurrency: int = Field(default=4, description="Celery worker concurrency")

    # Image Generation settings
    max_concurrent_images: int = Field(
        default=3, description="Maximum number of images to generate concurrently"
    )
    image_generation_timeout: int = Field(
        default=300, description="Timeout for individual image generation in seconds"
    )
    batch_generation_timeout: int = Field(
        default=1800, description="Timeout for batch image generation in seconds"
    )
    enable_batch_processing: bool = Field(
        default=True, description="Enable batch processing for image generation"
    )

    # LLM Provider Configuration
    primary_llm_provider: str = Field(
        default="claude",
        description="Primary LLM provider (claude or gemini)"
    )
    fallback_llm_provider: str = Field(
        default="gemini",
        description="Fallback LLM provider (claude or gemini)"
    )

    @validator('primary_llm_provider', 'fallback_llm_provider')
    def validate_llm_providers(cls, v):
        """Validate LLM provider names"""
        valid_providers = ['claude', 'gemini']
        if v not in valid_providers:
            raise ValueError(f"LLM provider must be one of {valid_providers}, got: {v}")
        return v

    # Gemini API settings
    gemini_api_key: str = Field(..., description="Gemini API key")
    gemini_model_name: str = Field(default="gemini-2.0-flash-exp", description="Gemini model name")

    # Anthropic API settings
    anthropic_api_key: Optional[str] = Field(None, description="Anthropic API key")
    anthropic_model_name: str = Field(
        default="claude-3-haiku-20240307", description="Anthropic model name"
    )

    # FAL AI settings
    fal_key: Optional[str] = Field(None, description="FAL AI API key")

    # ElevenLabs API settings
    elevenlabs_api_key: Optional[str] = Field(None, description="ElevenLabs API key")

    # JWT settings
    jwt_secret_key: str = Field(..., description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(
        default=1440, description="JWT access token expiry in minutes"
    )  # 24 hours

    # Google OAuth settings
    google_client_id: Optional[str] = Field(None, description="Google OAuth client ID")
    allowed_email_domains: List[str] = Field(
        default=[], description="Allowed email domains for registration"
    )

    # Email settings
    email_provider: str = Field(
        default="resend", description="Email provider to use: 'resend' or 'ses'"
    )
    aws_region: str = Field(default="us-east-2", description="AWS region for SES")
    aws_access_key_id: Optional[str] = Field(None, description="AWS Access Key ID for SES")
    aws_secret_access_key: Optional[str] = Field(None, description="AWS Secret Access Key for SES")
    resend_api_key: Optional[str] = Field(None, description="Resend API key for email sending")
    from_email: str = Field(
        default="<EMAIL>", description="From email address for OTP emails"
    )
    from_name: str = Field(default="Supermaya", description="From name for OTP emails")

    # CORS settings
    backend_cors_origins: List[str] = Field(
        default=["http://localhost", "http://localhost:3000", "http://localhost:8080"],
        description="CORS origins",
    )

    # Rate limiting
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")

    @validator("backend_cors_origins", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @validator("allowed_email_domains", pre=True)
    def assemble_email_domains(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @property
    def database_config(self) -> dict:
        """Get database configuration for SQLAlchemy"""
        return {
            "pool_size": self.db_pool_size,
            "max_overflow": self.db_max_overflow,
            "pool_timeout": self.db_pool_timeout,
            "pool_recycle": self.db_pool_recycle,
            "pool_pre_ping": True,
            "echo": self.debug,
        }

    @property
    def celery_config(self) -> dict:
        """Get Celery configuration"""
        return {
            "broker_url": self.celery_broker_url,
            "result_backend": self.celery_result_backend,
            "task_serializer": "json",
            "accept_content": ["json"],
            "result_serializer": "json",
            "timezone": "UTC",
            "enable_utc": True,
            "worker_concurrency": self.celery_worker_concurrency,
            "task_routes": {
                "script_service.generate_script": {"queue": "script_generation"},
                "script_service.regenerate_script": {"queue": "script_generation"},
                "scene_service.regenerate_scene": {"queue": "scene_processing"},
            },
            "task_time_limit": 600,  # 10 minutes
            "task_soft_time_limit": 540,  # 9 minutes
            "worker_send_task_events": True,
            "task_send_sent_event": True,
            "worker_enable_remote_control": True,
            "worker_disable_rate_limits": False,
            "worker_pool_restarts": True,
            "worker_cancel_long_running_tasks_on_connection_loss": True,
            "worker_prefetch_multiplier": 1,
            "worker_max_tasks_per_child": 1000,
            "task_remote_tracebacks": True,
            "worker_enable_remote_control": True,
            "worker_direct": True,
        }


# Global settings instance
settings = Settings()
