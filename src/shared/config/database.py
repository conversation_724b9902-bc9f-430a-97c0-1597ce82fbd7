"""
Database configuration and session management
Async database setup with connection pooling and session factory
"""

# Global imports
import logging
from typing import AsyncGenerator
from sqlalchemy.orm import declarative_base
from sqlalchemy.pool import AsyncAdaptedQueuePool
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

# Local imports
from .settings import settings

logger = logging.getLogger(__name__)

# SQLAlchemy base class
Base = declarative_base()


class DatabaseManager:
    """Manages database connections and sessions"""

    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialized = False

    def initialize(self):
        """Initialize database engine and session factory"""
        if self._initialized:
            return

        logger.info(f"DATABASE_URL in use: {settings.database_url}")
        logger.info("Initializing database connection...")

        # Create async engine with connection pooling
        self.engine = create_async_engine(
            settings.database_url,
            poolclass=AsyncAdaptedQueuePool,
            **settings.database_config,
        )

        # Create session factory
        self.session_factory = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False,
        )

        self._initialized = True
        logger.info("Database connection initialized successfully")

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with proper cleanup"""
        if not self._initialized:
            self.initialize()

        async with self.session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"Database session error: {e}")
                raise
            finally:
                await session.close()

    async def close(self):
        """Close database connections"""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


# Dependency for FastAPI
async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Database session dependency for FastAPI"""
    async for session in db_manager.get_session():
        yield session


# FastAPI dependency for DB session
from sqlalchemy.orm import Session
from typing import Generator


def get_db() -> Generator[Session, None, None]:
    """
    Dependency that provides a SQLAlchemy session and ensures it is closed after use.
    """
    from .settings import settings
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker

    # Use the async database URL for async code
    DATABASE_URL = settings.database_url
    engine = create_engine(DATABASE_URL, pool_pre_ping=True)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_sync_db() -> Generator[Session, None, None]:
    """
    Dependency that provides a sync SQLAlchemy session for background tasks.
    """
    from .settings import settings
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker

    # Use the sync database URL for Celery and sync code
    DATABASE_URL = settings.database_url_sync
    engine = create_engine(
        DATABASE_URL, pool_pre_ping=True, poolclass=None  # Use default pool for sync
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
