"""
AWS SSM Parameter Store integration for environment variable management
Fetches configuration values from AWS SSM based on environment-specific parameter names
"""

# Global imports
import os
import boto3
from loguru import logger
from typing import Dict, Optional, Any, List
from botocore.exceptions import ClientError, NoCredentialsError

# Define the specific SSM parameter keys that the app should read
# Format: {environment}_{PARAMETER_NAME}
SSM_PARAMETER_KEYS = [
    "SECRET_KEY",
    "JWT_SECRET_KEY",
    "DATABASE_URL",
    "DATABASE_URL_SYNC",
    "GEMINI_API_KEY",
    "GEMINI_MODEL_NAME",
    "GOOGLE_CLIENT_ID",
    "REDIS_URL",
    "CELERY_BROKER_URL",
    "CELERY_RESULT_BACKEND",
    "DEBUG",
    "LOG_LEVEL",
    "BACKEND_CORS_ORIGINS",
    "RATE_LIMIT_REQUESTS",
    "RATE_LIMIT_WINDOW",
    "DB_POOL_SIZE",
    "DB_MAX_OVERFLOW",
    "DB_POOL_TIMEOUT",
    "DB_POOL_RECYCLE",
    "CELERY_WORKER_CONCURRENCY",
    "JWT_ALGORITHM",
    "JWT_ACCESS_TOKEN_EXPIRE_MINUTES",
]


class SSMParameterStore:
    """AWS SSM Parameter Store client for fetching environment variables"""

    def __init__(self, region_name: Optional[str] = None):
        self.region_name = region_name or os.getenv("AWS_DEFAULT_REGION", "us-east-2")
        self.ssm_client = None
        self._initialize_client()

    def _initialize_client(self):
        try:
            self.ssm_client = boto3.client("ssm", region_name=self.region_name)
            logger.debug(f"SSM client initialized for region: {self.region_name}")
        except NoCredentialsError:
            logger.warning("AWS credentials not found. SSM integration disabled.")
            self.ssm_client = None
        except Exception as e:
            logger.error(f"Failed to initialize SSM client: {e}")
            self.ssm_client = None

    def get_parameter(self, parameter_name: str, decrypt: bool = True) -> Optional[str]:
        if not self.ssm_client:
            logger.warning("SSM client not available, cannot fetch parameter")
            return None
        try:
            response = self.ssm_client.get_parameter(Name=parameter_name, WithDecryption=decrypt)
            value = response["Parameter"]["Value"]
            logger.debug(f"Successfully fetched SSM parameter: {parameter_name}")
            return value
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "ParameterNotFound":
                logger.debug(f"SSM parameter not found: {parameter_name}")
            else:
                logger.error(f"Error fetching SSM parameter {parameter_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching SSM parameter {parameter_name}: {e}")
            return None

    def get_environment_parameters(self, environment: str) -> Dict[str, str]:
        if not self.ssm_client:
            logger.warning("SSM client not available, cannot fetch parameters")
            return {}
        parameters = {}
        for param_key in SSM_PARAMETER_KEYS:
            ssm_param_name = f"{environment}_{param_key}"
            value = self.get_parameter(ssm_param_name)
            if value is not None:
                parameters[param_key] = value
                logger.debug(f"Fetched SSM parameter: {ssm_param_name} -> {param_key}")
        logger.info(f"Fetched {len(parameters)} parameters for environment: {environment}")
        return parameters

    def is_available(self) -> bool:
        if not self.ssm_client:
            return False
        try:
            self.ssm_client.describe_parameters(MaxResults=1)
            return True
        except Exception:
            return False


class EnvironmentVariableManager:
    """Manages environment variables from SSM and .env files"""

    def __init__(self, environment: str, region_name: Optional[str] = None):
        self.environment = environment
        self.ssm_store = SSMParameterStore(region_name)
        self._cached_parameters: Optional[Dict[str, str]] = None

    def get_variable(self, key: str, default: Optional[str] = None) -> Optional[str]:
        # 1. SSM parameter (highest priority)
        if self.ssm_store.is_available():
            if self._cached_parameters is None:
                self._cached_parameters = self.ssm_store.get_environment_parameters(
                    self.environment
                )
            ssm_value = self._cached_parameters.get(key)
            if ssm_value is not None:
                logger.debug(f"Using SSM parameter: {key}")
                return ssm_value
        # 2. Local environment variable (from .env or shell)
        local_value = os.getenv(key)
        if local_value is not None:
            logger.debug(f"Using local environment variable: {key}")
            return local_value
        # 3. Default value (lowest priority)
        if default is not None:
            logger.debug(f"Using default value for: {key}")
            return default
        return None

    def get_all_variables(self) -> Dict[str, str]:
        variables = {}
        # SSM parameters (highest priority)
        if self.ssm_store.is_available():
            ssm_params = self.ssm_store.get_environment_parameters(self.environment)
            variables.update(ssm_params)
        # .env and environment variables (lower priority)
        for key, value in os.environ.items():
            variables[key] = value
        return variables

    def set_environment_variables(self):
        # Only set SSM parameters as env vars if not already set (so .env can override)
        if not self.ssm_store.is_available():
            logger.debug("SSM not available, skipping environment variable setup")
            return
        ssm_params = self.ssm_store.get_environment_parameters(self.environment)
        for key, value in ssm_params.items():
            if key not in os.environ:
                os.environ[key] = value
                logger.debug(f"Set environment variable from SSM: {key}")
            else:
                logger.debug(f"Environment variable already set locally, skipping SSM value: {key}")


# Global instance for easy access
_ssm_manager: Optional[EnvironmentVariableManager] = None


def get_ssm_manager(
    environment: str, region_name: Optional[str] = None
) -> EnvironmentVariableManager:
    global _ssm_manager
    if _ssm_manager is None or _ssm_manager.environment != environment:
        _ssm_manager = EnvironmentVariableManager(environment, region_name)
    return _ssm_manager


def get_ssm_variable(
    key: str, environment: str, default: Optional[str] = None, region_name: Optional[str] = None
) -> Optional[str]:
    manager = get_ssm_manager(environment, region_name)
    return manager.get_variable(key, default)


def get_ssm_parameter_keys() -> List[str]:
    return SSM_PARAMETER_KEYS.copy()


def get_environment_ssm_parameter_names(environment: str) -> List[str]:
    return [f"{environment}_{key}" for key in SSM_PARAMETER_KEYS]
