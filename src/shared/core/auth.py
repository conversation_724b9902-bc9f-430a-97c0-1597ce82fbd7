"""
Authentication dependencies and helpers for FastAPI routes
"""

# Global imports
from sqlalchemy import select
from typing import Dict, <PERSON>, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Local imports
from src.shared.core.security import verify_token
from src.shared.models.database_models import User
from src.shared.config.database import get_database_session

security = HTTPBearer()


async def get_current_user_info(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: AsyncSession = Depends(get_database_session),
) -> Tuple[str, str, str]:  # Returns (org_id, user_id, email)
    """
    Get current user information from JWT token
    Returns tuple of (org_id, user_id, email)
    """
    token = credentials.credentials
    payload = verify_token(token)

    org_id = payload.get("org_id")
    user_id = payload.get("user_id")
    email = payload.get("sub")

    if not all([org_id, user_id, email]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token payload"
        )

    # Verify user exists and is active
    stmt = select(User).where(
        User.org_id == org_id,
        User.user_id == user_id,
        User.user_email == email,
        User.is_active == True,
    )
    result = await session.execute(stmt)
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found or inactive"
        )

    return org_id, user_id, email


async def get_current_user(
    user_info: Tuple[str, str, str] = Depends(get_current_user_info),
) -> Dict[str, str]:
    """
    Get current user as dictionary
    """
    org_id, user_id, email = user_info
    return {"org_id": org_id, "user_id": user_id, "email": email}


def require_auth(
    user_info: Tuple[str, str, str] = Depends(get_current_user_info),
) -> Tuple[str, str, str]:
    """
    Dependency that requires authentication
    """
    return user_info
