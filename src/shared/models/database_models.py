# Global imports
import uuid
from datetime import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB, ENUM
from sqlalchemy import (
    Column,
    String,
    Text,
    Integer,
    DateTime,
    ForeignKey,
    Boolean,
    TIMESTAMP,
    ForeignKeyConstraint,
)

# Local imports
from src.shared.config.database import Base

# ENUM definitions
asset_type_enum = ENUM("audio", "image", "text", "video", name="asset_type_enum")
audio_source_enum = ENUM(
    "background_music",
    "background_sound",
    "user_uploaded",
    "voiceover",
    "voiceover_combined",
    name="audio_source_enum",
)
text_type_enum = ENUM("caption", "prompt", "script", "scene", name="text_type_enum")
video_gen_method_enum = ENUM(
    "ttv", "rendered", "audio_mixed", "voiceover_mixed", name="video_gen_method_enum"
)
image_gen_status_enum = ENUM(
    "pending", "processing", "completed", "failed", name="image_gen_status_enum"
)


class Organization(Base):
    __tablename__ = "organizations"
    org_id = Column(String, primary_key=True)
    org_name = Column(String, nullable=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)


class User(Base):
    __tablename__ = "users"
    org_id = Column(
        String, ForeignKey("organizations.org_id", ondelete="CASCADE"), primary_key=True
    )
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_email = Column(String, unique=True, nullable=False)
    username = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    password_hash = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)


class PendingUser(Base):
    __tablename__ = "pending_users"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, nullable=False)
    password_hash = Column(String, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    org_name = Column(String, nullable=False)
    otp_code = Column(String, nullable=True)
    otp_expires_at = Column(TIMESTAMP, nullable=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)


class UserSignin(Base):
    __tablename__ = "user_signins"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    org_id = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)

    __table_args__ = (
        ForeignKeyConstraint(
            ["org_id", "user_id"], ["users.org_id", "users.user_id"], ondelete="CASCADE"
        ),
    )


class Campaign(Base):
    __tablename__ = "campaigns"
    org_id = Column(
        String, ForeignKey("organizations.org_id", ondelete="CASCADE"), primary_key=True
    )
    campaign_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)


class Project(Base):
    __tablename__ = "projects"
    org_id = Column(String, primary_key=True)
    campaign_id = Column(UUID(as_uuid=True), primary_key=True)
    project_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)

    __table_args__ = (
        ForeignKeyConstraint(
            ["org_id", "campaign_id"],
            ["campaigns.org_id", "campaigns.campaign_id"],
            ondelete="CASCADE",
        ),
    )


class ProjectAsset(Base):
    __tablename__ = "project_assets"
    project_id = Column(UUID(as_uuid=True), primary_key=True)
    asset_id = Column(String, primary_key=True)
    asset_type = Column(asset_type_enum, nullable=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)


class AudioAsset(Base):
    __tablename__ = "audio_assets"
    org_id = Column(String, ForeignKey("organizations.org_id", ondelete="CASCADE"), nullable=False)
    asset_id = Column(String, primary_key=True)
    s3_url = Column(String, nullable=False)
    local_path = Column(String, nullable=True)  # Local file path for background sounds
    source_type = Column(audio_source_enum, nullable=False)
    # The scene this audio asset is associated with (for background sounds)
    scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=True)
    # The script this audio asset is associated with (for background music)
    script_id = Column(UUID(as_uuid=True), ForeignKey("scripts.id"), nullable=True)
    # Generation metadata
    generation_metadata = Column(JSONB, nullable=True)
    generation_status = Column(String, default="pending")
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)


class ImageAsset(Base):
    __tablename__ = "image_assets"
    org_id = Column(String, ForeignKey("organizations.org_id", ondelete="CASCADE"), nullable=False)
    asset_id = Column(String, primary_key=True)
    s3_url = Column(String, nullable=False)
    # The scene this image asset is associated with (if any)
    scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=True)
    # The script this image asset is associated with (if any)
    script_id = Column(UUID(as_uuid=True), ForeignKey("scripts.id"), nullable=True)
    # The generation that created this image (if any)
    generation_id = Column(String, nullable=True)
    # Whether this image is selected for the scene
    is_selected = Column(Boolean, default=False)
    status = Column(String, default="active")
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)
    local_path = Column(String, nullable=True)  # New column for local file path


class TextAsset(Base):
    __tablename__ = "text_assets"
    org_id = Column(String, ForeignKey("organizations.org_id", ondelete="CASCADE"), nullable=False)
    asset_id = Column(String, primary_key=True)
    content = Column(Text, nullable=True)
    s3_url = Column(String, nullable=True)
    type = Column(text_type_enum, nullable=False)
    extra_metadata = Column(JSONB, nullable=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)


class VideoAsset(Base):
    __tablename__ = "video_assets"
    org_id = Column(String, ForeignKey("organizations.org_id", ondelete="CASCADE"), nullable=False)
    asset_id = Column(String, primary_key=True)
    s3_url = Column(String, nullable=False)
    generation_method = Column(video_gen_method_enum, nullable=False)
    # The scene this video asset is associated with
    scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=True)
    # The script this video asset is associated with (for stitched videos)
    script_id = Column(UUID(as_uuid=True), ForeignKey("scripts.id"), nullable=True)
    local_path = Column(String, nullable=True)  # New column for local file path
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(TIMESTAMP, nullable=True)


class Script(Base):
    __tablename__ = "scripts"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    org_id = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    project_id = Column(UUID(as_uuid=True), nullable=True)
    text_asset_id = Column(String, ForeignKey("text_assets.asset_id"), nullable=True)
    title = Column(String, nullable=False)
    video_style = Column(String, nullable=False)
    duration = Column(String, nullable=False)
    aspect_ratio = Column(String, nullable=False, default="16:9")
    narration_type = Column(String, nullable=False, default="voice-based")
    brand_name = Column(String, nullable=True)
    product_name = Column(String, nullable=True)
    brand_description = Column(Text, nullable=True)
    background_music_prompt = Column(Text, nullable=True)
    status = Column(String, default="pending")
    generation_status = Column(String, default="queued")
    error_message = Column(Text, nullable=True)
    extra_metadata = Column(JSONB, nullable=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(TIMESTAMP, nullable=True)

    __table_args__ = (
        ForeignKeyConstraint(["org_id", "user_id"], ["users.org_id", "users.user_id"]),
    )


class Scene(Base):
    __tablename__ = "scenes"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    script_id = Column(
        UUID(as_uuid=True), ForeignKey("scripts.id", ondelete="CASCADE"), nullable=False
    )
    text_asset_id = Column(String, ForeignKey("text_assets.asset_id"), nullable=True)
    scene_number = Column(Integer, nullable=False)
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    visual_description = Column(Text, nullable=False)
    narration = Column(Text, nullable=False)
    duration = Column(String, default="5s")
    location_group = Column(Integer, nullable=True)
    status = Column(String, default="active")
    generation_status = Column(String, default="completed")
    error_message = Column(Text, nullable=True)
    character_info = Column(JSONB, nullable=True)
    extra_metadata = Column(JSONB, nullable=True)
    image_prompt = Column(Text, nullable=True)  # Store the AI-generated prompt for image generation
    video_prompt = Column(Text, nullable=True)  # Store the AI-generated prompt for video generation
    bgsound_prompt = Column(
        Text, nullable=True
    )  # Store the AI-generated prompt for background sound generation
    audio_generation_status = Column(
        String, default="pending"
    )  # Audio generation status (like video_generation_status)
    voiceover_prompt = Column(Text, nullable=True)  # Store custom voiceover prompt for scene
    voiceover_status = Column(
        String, default="pending"
    )  # Voiceover generation status (pending, generating, completed, failed)
    voiceover_generated_at = Column(TIMESTAMP, nullable=True)  # When voiceover was last generated
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    regenerated_at = Column(TIMESTAMP, nullable=True)


class TaskQueue(Base):
    __tablename__ = "task_queue"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(String, unique=True, nullable=False)
    task_type = Column(String, nullable=False)
    org_id = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    related_script_id = Column(UUID(as_uuid=True), ForeignKey("scripts.id"), nullable=True)
    related_scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=True)
    input_data = Column(JSONB, nullable=False)
    status = Column(String, default="pending")
    progress = Column(Integer, default=0)
    result = Column(JSONB, nullable=True)
    error_message = Column(Text, nullable=True)
    queue_name = Column(String, nullable=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    started_at = Column(TIMESTAMP, nullable=True)
    completed_at = Column(TIMESTAMP, nullable=True)

    __table_args__ = (
        ForeignKeyConstraint(["org_id", "user_id"], ["users.org_id", "users.user_id"]),
    )


class ImageGeneration(Base):
    __tablename__ = "image_generations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    org_id = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=False)
    script_id = Column(UUID(as_uuid=True), ForeignKey("scripts.id"), nullable=False)
    aspect_ratio = Column(String, default="16:9")
    include_brand = Column(Boolean, default=True)
    status = Column(image_gen_status_enum, default="pending")
    progress = Column(Integer, default=0)
    is_regeneration = Column(Boolean, default=False)
    result = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)

    # Relationships
    scene = relationship("Scene", back_populates="image_generations")
    script = relationship("Script", back_populates="image_generations")


# Add back_populates to Scene and Script if not present
try:
    Scene.image_generations = relationship("ImageGeneration", back_populates="scene")
except Exception:
    pass
try:
    Script.image_generations = relationship("ImageGeneration", back_populates="script")
except Exception:
    pass


class VideoGeneration(Base):
    __tablename__ = "video_generations"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    org_id = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=False)
    script_id = Column(UUID(as_uuid=True), ForeignKey("scripts.id"), nullable=False)
    aspect_ratio = Column(String, default="16:9")
    duration = Column(String, default="5")
    status = Column(String, default="pending")  # pending, processing, completed, failed
    progress = Column(Integer, default=0)
    video_url = Column(String, nullable=True)
    local_path = Column(String, nullable=True)
    error_message = Column(Text, nullable=True)
    generation_metadata = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)

    # Relationships
    scene = relationship("Scene", back_populates="video_generations")
    script = relationship("Script", back_populates="video_generations")


# Add back_populates to Scene and Script for video generations
try:
    Scene.video_generations = relationship("VideoGeneration", back_populates="scene")
except Exception:
    pass
try:
    Script.video_generations = relationship("VideoGeneration", back_populates="script")
except Exception:
    pass


class RefreshToken(Base):
    __tablename__ = "refresh_tokens"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    org_id = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    token = Column(String, nullable=False, unique=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = Column(TIMESTAMP, nullable=False)
    revoked = Column(Boolean, default=False)
    last_used_at = Column(TIMESTAMP, nullable=True)

    __table_args__ = (
        ForeignKeyConstraint(
            ["org_id", "user_id"], ["users.org_id", "users.user_id"], ondelete="CASCADE"
        ),
    )
