"""
LLM Service - Unified service for Large Language Model providers
Supports multiple providers with fallback mechanism.
"""

import google.generativeai as genai
import anthropic
from typing import Optional, Dict, Any, List
from loguru import logger
from src.shared.config.settings import settings


class LLMService:
    """
    Unified LLM service that supports multiple providers with fallback mechanism.
    Primary: <PERSON> (Anthropic)
    Fallback: Gemini (Google)
    """

    def __init__(self):
        self.claude_client = None
        self.gemini_model = None
        self.current_gemini_model = None  # Track which Gemini model is actually being used

        # Configure primary and fallback providers from environment
        self.primary_provider = settings.primary_llm_provider
        self.fallback_provider = settings.fallback_llm_provider

        logger.info(f"🔧 LLM Service configured: Primary={self.primary_provider}, Fallback={self.fallback_provider}")
        self.setup_providers()

    def setup_providers(self):
        """Initialize LLM providers with fallback logic"""
        # Setup Claude (Primary)
        if settings.anthropic_api_key:
            try:
                self.claude_client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
                # Test Claude connection with a minimal request
                test_response = self.claude_client.messages.create(
                    model=settings.anthropic_model_name,
                    max_tokens=10,
                    messages=[{"role": "user", "content": "Hi"}],
                )
                logger.info("✅ <PERSON> LLM initialized successfully")
            except Exception as e:
                logger.warning(f"⚠️ Claude setup failed: {e}")
                self.claude_client = None
        else:
            logger.warning("⚠️ Claude API key not found")

        # Setup Gemini (Fallback)
        if settings.gemini_api_key:
            try:
                genai.configure(api_key=settings.gemini_api_key)

                # Try configured model first, then fallback models
                model_fallbacks = [
                    "gemini-2.0-flash-exp",
                    "gemini-2.0-flash",
                    "gemini-1.5-flash",
                    "gemini-1.5-pro",
                    "gemini-pro",
                ]

                # Remove configured model from fallbacks to avoid duplication
                if settings.gemini_model_name in model_fallbacks:
                    model_fallbacks.remove(settings.gemini_model_name)

                # Try configured model first
                try:
                    self.gemini_model = genai.GenerativeModel(settings.gemini_model_name)
                    # Test the model
                    test_response = self.gemini_model.generate_content("Hi")
                    logger.info(
                        f"✅ Gemini LLM initialized with configured model: {settings.gemini_model_name}"
                    )
                    self.current_gemini_model = settings.gemini_model_name
                except Exception as model_error:
                    logger.warning(
                        f"⚠️ Configured Gemini model {settings.gemini_model_name} failed: {model_error}"
                    )
                    logger.info("🔄 Trying fallback Gemini models...")

                    # Try fallback models
                    for model_name in model_fallbacks:
                        try:
                            self.gemini_model = genai.GenerativeModel(model_name)
                            # Test the model
                            test_response = self.gemini_model.generate_content("Hi")
                            logger.info(
                                f"✅ Gemini LLM initialized with fallback model: {model_name}"
                            )
                            self.current_gemini_model = model_name
                            break
                        except Exception as fallback_error:
                            logger.warning(
                                f"⚠️ Gemini fallback model {model_name} failed: {fallback_error}"
                            )
                            continue
                    else:
                        logger.warning("⚠️ All Gemini models failed")
                        self.gemini_model = None
                        self.current_gemini_model = None
            except Exception as e:
                logger.warning(f"⚠️ Gemini setup failed: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ Gemini API key not found")

    def is_available(self) -> bool:
        """Check if any provider is available"""
        return self.claude_client is not None or self.gemini_model is not None

    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        providers = []
        if self.claude_client:
            providers.append("claude")
        if self.gemini_model:
            providers.append("gemini")
        return providers

    def generate_content(self, prompt, **kwargs) -> Dict[str, Any]:
        """
        Generate content using configurable primary provider with fallback
        Supports both text and vision (multimodal) requests

        Args:
            prompt: The text prompt or list [text_prompt, image] for vision
            **kwargs: Additional parameters (model-specific)

        Returns:
            Dict with 'success', 'content', 'provider', and optional 'error'
        """
        # Check if this is a vision request (prompt is a list with image)
        is_vision_request = isinstance(prompt, list) and len(prompt) == 2

        # Try primary provider first
        primary_result = self._try_provider(self.primary_provider, prompt, is_vision_request, **kwargs)
        if primary_result["success"]:
            return primary_result

        # Fallback to secondary provider
        logger.warning(f"Primary provider {self.primary_provider} failed, trying fallback {self.fallback_provider}")
        fallback_result = self._try_provider(self.fallback_provider, prompt, is_vision_request, **kwargs)
        if fallback_result["success"]:
            return fallback_result

        # Both providers failed
        return {
            "success": False,
            "content": None,
            "provider": None,
            "error": f"Both {self.primary_provider} and {self.fallback_provider} providers failed"
        }

    def _try_provider(self, provider: str, prompt, is_vision_request: bool, **kwargs) -> Dict[str, Any]:
        """Try a specific provider"""
        if provider == "claude" and self.claude_client and not is_vision_request:
            return self._try_claude(prompt, **kwargs)
        elif provider == "gemini" and self.gemini_model:
            return self._try_gemini(prompt, is_vision_request, **kwargs)
        else:
            return {
                "success": False,
                "content": None,
                "provider": provider,
                "error": f"Provider {provider} not available or not suitable for this request"
            }

    def _try_claude(self, prompt, **kwargs) -> Dict[str, Any]:
        """Try Claude provider"""
        try:
            # Extract Claude-specific parameters
            max_tokens = kwargs.get("max_tokens", 4000)
            model = kwargs.get("claude_model", settings.anthropic_model_name)
            temperature = kwargs.get("temperature", 0.7)

            # For text-only requests
            text_prompt = prompt if isinstance(prompt, str) else prompt[0]

            response = self.claude_client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": text_prompt}],
            )

            return {
                "success": True,
                "content": response.content[0].text,
                "provider": "claude",
                "model": model,
            }
        except Exception as e:
            logger.warning(f"⚠️ Claude failed: {e}")
            return {
                "success": False,
                "content": None,
                "provider": "claude",
                "error": str(e)
            }

    def _try_gemini(self, prompt, is_vision_request: bool, **kwargs) -> Dict[str, Any]:
        """Try Gemini provider"""
        try:
            # Extract Gemini-specific parameters
            generation_config = {}
            if "temperature" in kwargs:
                generation_config["temperature"] = kwargs["temperature"]
            if "max_output_tokens" in kwargs:
                generation_config["max_output_tokens"] = kwargs["max_output_tokens"]
            elif "max_tokens" in kwargs:
                generation_config["max_output_tokens"] = kwargs["max_tokens"]

            response = self.gemini_model.generate_content(
                prompt, generation_config=generation_config if generation_config else None
            )

            return {
                "success": True,
                "content": response.text,
                "provider": "gemini",
                "model": self.current_gemini_model or settings.gemini_model_name,
            }
        except Exception as e:
            logger.warning(f"⚠️ Gemini failed: {e}")
            return {
                "success": False,
                "content": None,
                "provider": "gemini",
                "error": str(e)
            }

    def generate_vision_content(
        self,
        prompt: str,
        image_data: bytes,
        mime_type: str = "image/jpeg",
        provider: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate content with vision capabilities

        Args:
            prompt: The input prompt
            image_data: Image data as bytes
            mime_type: MIME type of the image
            provider: Specific provider to use (optional)

        Returns:
            Dict with success, content, provider_used, and error (if any)
        """
        if not self.is_available():
            return {
                "success": False,
                "content": None,
                "provider_used": None,
                "error": "No LLM providers available",
            }

        # Determine provider order (prefer Claude for vision)
        if provider:
            providers_to_try = [provider]
        else:
            providers_to_try = [self.primary_provider, self.fallback_provider]

        last_error = None

        for provider_name in providers_to_try:
            try:
                if provider_name == "claude" and self.claude_client:
                    import base64

                    image_base64 = base64.b64encode(image_data).decode()

                    response = self.claude_client.messages.create(
                        model=settings.anthropic_model_name,
                        max_tokens=4000,
                        messages=[
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "image",
                                        "source": {
                                            "type": "base64",
                                            "media_type": mime_type,
                                            "data": image_base64,
                                        },
                                    },
                                    {"type": "text", "text": prompt},
                                ],
                            }
                        ],
                    )
                    content = response.content[0].text if response.content else ""
                    return {
                        "success": True,
                        "content": content,
                        "provider_used": "claude",
                        "error": None,
                    }

                elif provider_name == "gemini" and self.gemini_model:
                    # Gemini vision support
                    import PIL.Image
                    import io

                    image = PIL.Image.open(io.BytesIO(image_data))
                    response = self.gemini_model.generate_content([prompt, image])
                    return {
                        "success": True,
                        "content": response.text,
                        "provider_used": "gemini",
                        "error": None,
                    }

            except Exception as e:
                last_error = str(e)
                logger.warning(f"⚠️ {provider_name} vision failed: {e}")
                continue

        return {
            "success": False,
            "content": None,
            "provider_used": None,
            "error": f"All vision providers failed. Last error: {last_error}",
        }


# Global instance
llm_service = LLMService()
