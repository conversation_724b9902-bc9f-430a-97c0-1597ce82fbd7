# Global imports
import os
import uuid
import json
import tempfile
import numpy as np
from pathlib import Path
from moviepy.video import fx as vfx
import google.generativeai as genai
from PIL import Image, ImageDraw, ImageFont
from typing import Any, Dict, List, Optional, Tuple
from moviepy import CompositeVideoClip, ImageClip, VideoFileClip

# Local imports
# Use shared config for Gemini and S3
from src.shared.utils.s3_client import S3Client
from src.shared.config.settings import settings


class EnhancedTextOverlaySystem:
    """
    Enhanced Text Overlay System using Pillow for better text rendering
    FIXES: All animations working, font selection, easier UI, proper video loading
    Compatible with MoviePy 2.x API changes
    NOW WITH: User font selection + easier overlay creation UI + S3 integration
    """

    def __init__(self):
        # Initialize LLM service
        from src.shared.services.llm_service import llm_service

        self.llm_service = llm_service

        # Initialize S3 client
        self.s3_client = S3Client(aws_region="us-east-2")
        self.s3_bucket = "assets-vidflux"
        self.text_overlay_prefix = "Vidflux-Assets/text_overlay_assets/"
        self.stitched_videos_prefix = "Vidflux-Assets/stitched_videos/"

        # Setup paths - Use output/final_videos as primary source
        self.input_path = Path("output/final_videos")
        self.output_path = Path("output/text_videos")
        self.output_path.mkdir(parents=True, exist_ok=True)

        # Create temp directory for processing
        self.temp_dir = Path(tempfile.gettempdir()) / "enhanced_text_overlay"
        self.temp_dir.mkdir(exist_ok=True)

        # Load static font data from JSON
        static_dir = Path(__file__).parent.parent.parent / "static"
        font_json_path = static_dir / "text_overlay_fonts.json"
        with open(font_json_path, "r", encoding="utf-8") as f:
            self.font_data = json.load(f)

        # Video settings (16:9 ratio)
        self.video_ratio = (16, 9)
        self.bottom_margin_percent = 0.12  # 12% from bottom
        self.text_safe_area_percent = 0.8  # 80% of video width for text

        # Enhanced settings
        self.default_style = "modern"
        self.default_animation = "fade_in_out"
        self.max_overlays = 4

        # ENHANCED: Better font system with more fonts and user selection
        self.fonts = self._setup_universal_font_system()
        self.available_fonts = self._setup_enhanced_font_system()
        self.font_display_names = self._create_font_display_mapping()

        # Text styling
        self.default_font_size_percent = 0.045  # 4.5% of video height
        self.stroke_width_ratio = 0.15  # 15% of font size
        self.fade_duration = 0.5

        print(f"✓ FIXED Enhanced Text Overlay System initialized with S3 integration")
        print(f"✓ S3 Bucket: {self.s3_bucket}")
        print(f"✓ Text Overlay Prefix: {self.text_overlay_prefix}")
        print(f"✓ Available font categories: {list(self.available_fonts.keys())}")
        print(f"✓ Total fonts: {sum(len(fonts) for fonts in self.available_fonts.values())}")
        print(f"✓ Input: {self.input_path}")
        print(f"✓ Output: {self.output_path}")

    def _download_video_from_s3(self, script_id: str) -> str:
        """Download stitched video from S3 for the given script_id"""
        try:
            # First, try to find the video in the audio-stitching-assets directory
            audio_stitching_prefix = f"Vidflux-Assets/audio-stitching-assets/script_{script_id}/"

            print(f"Looking for video in S3: {self.s3_bucket}/{audio_stitching_prefix}")

            # List objects in the audio-stitching-assets prefix
            response = self.s3_client.s3_client.list_objects_v2(
                Bucket=self.s3_bucket, Prefix=audio_stitching_prefix
            )

            if "Contents" in response:
                # Get the most recent video file
                video_objects = [obj for obj in response["Contents"] if obj["Key"].endswith(".mp4")]
                if video_objects:
                    # Sort by last modified and get the most recent
                    video_objects.sort(key=lambda x: x["LastModified"], reverse=True)
                    video_key = video_objects[0]["Key"]

                    # Download to temp directory
                    temp_video_path = self.temp_dir / f"audio_stitched_video_{script_id}.mp4"

                    print(f"Downloading video from S3: {self.s3_bucket}/{video_key}")
                    downloaded_path = self.s3_client.download_file(
                        bucket=self.s3_bucket, key=video_key, local_path=str(temp_video_path)
                    )

                    if os.path.exists(downloaded_path):
                        print(f"✓ Successfully downloaded video: {downloaded_path}")
                        return downloaded_path
                    else:
                        raise FileNotFoundError(f"Downloaded file not found: {downloaded_path}")

            # Fallback: try the old stitched_videos path
            stitched_video_key = f"{self.stitched_videos_prefix}video_{script_id}.mp4"
            print(f"Trying fallback path: {self.s3_bucket}/{stitched_video_key}")

            temp_video_path = self.temp_dir / f"stitched_video_{script_id}.mp4"
            downloaded_path = self.s3_client.download_file(
                bucket=self.s3_bucket, key=stitched_video_key, local_path=str(temp_video_path)
            )

            if os.path.exists(downloaded_path):
                print(f"✓ Successfully downloaded video: {downloaded_path}")
                return downloaded_path
            else:
                raise FileNotFoundError(f"Downloaded file not found: {downloaded_path}")

        except Exception as e:
            print(f"✗ Error downloading video from S3: {e}")
            # Fallback to local file system
            local_paths = [
                f"output/final_videos/script_{script_id}_final_mixed_*.mp4",
                f"output/stitched_videos/video_{script_id}.mp4",
                f"output/final_videos/video_{script_id}.mp4",
                f"output/videos/video_{script_id}.mp4",
            ]

            for local_pattern in local_paths:
                import glob

                found_files = glob.glob(local_pattern)
                if found_files:
                    # Use the most recent file
                    most_recent = max(found_files, key=os.path.getctime)
                    print(f"✓ Using local video: {most_recent}")
                    return most_recent

            raise FileNotFoundError(f"No video found for script_id: {script_id}")

    def _upload_video_to_s3(
        self, local_video_path: str, script_id: str, style: str, animation: str
    ) -> str:
        """Upload processed video to S3"""
        try:
            # Generate unique filename
            unique_id = str(uuid.uuid4())[:8]
            s3_key = f"{self.text_overlay_prefix}text_overlay_{script_id}_{style}_{animation}_{unique_id}.mp4"

            print(f"Uploading video to S3: {self.s3_bucket}/{s3_key}")

            # Upload to S3
            s3_url = self.s3_client.upload_file(
                bucket=self.s3_bucket, key=s3_key, file_path=local_video_path
            )

            print(f"✓ Successfully uploaded video to S3: {s3_url}")
            return s3_url

        except Exception as e:
            print(f"✗ Error uploading video to S3: {e}")
            raise

    def process_video_with_enhanced_timeline_s3(
        self,
        script_id: str,
        script: str = "",
        style: str = "modern",
        animation: str = "fade_in_out",
        max_overlays: int = 4,
        font_name: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Process video with enhanced timeline and styling + S3 integration"""
        try:
            print(f"\n=== Processing Text Overlay with S3 for Script ID: {script_id} ===")

            # Download video from S3
            video_path = self._download_video_from_s3(script_id)

            if not os.path.exists(video_path):
                return {
                    "success": False,
                    "error": f"Video file not found: {video_path}",
                }

            print(f"Video downloaded: {video_path}")

            # Load video and get properties
            video_clip = VideoFileClip(video_path)
            video_duration = video_clip.duration
            video_size = video_clip.size

            print(f"Video: {video_duration:.1f}s, {video_size[0]}x{video_size[1]}")

            # Calculate text properties
            text_props = self._calculate_text_properties(video_size)

            # Generate enhanced text timeline
            text_timeline = self.generate_enhanced_text_timeline(
                script, video_duration, style, max_overlays
            )

            if not text_timeline:
                video_clip.close()
                return {"success": False, "error": "No text overlays generated"}

            print(f"Generated {len(text_timeline)} enhanced {style} overlays")

            # Create enhanced text clips with font selection
            text_clips = []
            for i, overlay in enumerate(text_timeline):
                print(f"Creating {style} overlay {i+1}: '{overlay['text']}'")

                # Create text image with style and optional font
                text_img = self._create_text_image_with_style_and_font(
                    text=overlay["text"],
                    properties=text_props,
                    style=style,
                    font_name=font_name,
                )

                # Convert to clip
                img_array = np.array(text_img)
                text_clip = ImageClip(img_array, duration=overlay["duration"], transparent=True)

                # Position
                video_width = text_props["video_width"]
                video_height = text_props["video_height"]
                bottom_margin = text_props["bottom_margin"]

                x_pos = (video_width - text_img.width) // 2
                y_pos = video_height - text_img.height - bottom_margin

                text_clip = text_clip.with_position((x_pos, y_pos))

                # Add timing and FIXED enhanced transitions
                timed_clip = text_clip.with_start(overlay["start_time"])
                final_clip = self._apply_enhanced_transitions_fixed(timed_clip, animation)

                text_clips.append(final_clip)
                print(f"✓ Enhanced {style} overlay {i+1} created successfully")

            if not text_clips:
                video_clip.close()
                return {"success": False, "error": "No text clips created successfully"}

            # Composite final video
            print(f"Compositing final video with {style} style...")
            final_video = CompositeVideoClip([video_clip] + text_clips)

            # Generate local output filename
            unique_id = str(uuid.uuid4())[:8]
            local_output_path = (
                self.output_path / f"text_overlay_{script_id}_{style}_{animation}_{unique_id}.mp4"
            )

            # Write final video locally first
            print(f"Writing to local: {local_output_path}")
            temp_audio_file = self.temp_dir / f"temp_audio_{unique_id}.m4a"

            final_video.write_videofile(
                str(local_output_path),
                codec="libx264",
                audio_codec="aac",
                temp_audiofile=str(temp_audio_file),
                remove_temp=True,
                preset="medium",
                ffmpeg_params=["-crf", "23", "-pix_fmt", "yuv420p"],
                logger="bar",
                audio_fps=44100,
            )

            # Upload to S3
            s3_url = self._upload_video_to_s3(str(local_output_path), script_id, style, animation)

            # Cleanup
            final_video.close()
            video_clip.close()
            for clip in text_clips:
                clip.close()

            if temp_audio_file.exists():
                temp_audio_file.unlink()

            # Clean up local file after S3 upload
            if os.path.exists(local_output_path):
                os.remove(local_output_path)

            print(f"✓ Enhanced video processing completed: {s3_url}")

            return {
                "success": True,
                "s3_url": s3_url,
                "script_id": script_id,
                "overlays_count": len(text_clips),
                "style": style,
                "animation": animation,
                "font_used": font_name or f"Default {style}",
                "message": f"Successfully processed with {len(text_clips)} enhanced {style} overlays using {animation} animation",
            }

        except Exception as e:
            print(f"✗ Error processing enhanced video: {e}")
            return {"success": False, "error": str(e)}

    def _setup_universal_font_system(self) -> List[str]:
        """ENHANCED: Setup universal font system with MORE fonts"""
        fonts = []

        # Load font candidates from static data
        font_candidates = self.font_data["font_candidates"]

        # Test fonts in order
        for font_path in font_candidates:
            if os.path.exists(font_path):
                try:
                    ImageFont.truetype(font_path, 24)
                    fonts.append(font_path)
                    print(f"✓ Font working: {os.path.basename(font_path)}")
                except Exception:
                    continue

        # Add default font fallback
        fonts.append("default")
        print(f"✓ Total working fonts: {len(fonts)} (including default)")

        return fonts

    def _setup_enhanced_font_system(self) -> Dict[str, List[str]]:
        """ENHANCED: Font system with better categorization and more fonts"""
        # Load font categories from static data
        font_categories = self.font_data["font_categories"]

        available_fonts = {}
        for category, font_list in font_categories.items():
            available_fonts[category] = []
            for font_path in font_list:
                if os.path.exists(font_path):
                    try:
                        ImageFont.truetype(font_path, 24)
                        available_fonts[category].append(font_path)
                    except Exception:
                        continue

            # Add fallback to default fonts if no category fonts found
            if not available_fonts[category]:
                available_fonts[category] = self.fonts[:3] if len(self.fonts) > 3 else self.fonts

        return available_fonts

    def _create_font_display_mapping(self) -> Dict[str, List[str]]:
        """Create user-friendly font names for selection"""
        display_mapping = {}

        for category, font_paths in self.available_fonts.items():
            display_mapping[category] = []
            for font_path in font_paths:
                if font_path == "default":
                    display_name = "Default System Font"
                else:
                    font_name = os.path.basename(font_path).replace(".ttf", "").replace(".ttc", "")
                    # Clean up font names
                    font_name = font_name.replace("-", " ").replace("_", " ")
                    display_name = font_name

                display_mapping[category].append(display_name)

        return display_mapping

    def get_available_fonts_for_ui(self) -> Dict[str, List[str]]:
        """Get user-friendly font names for UI"""
        return self.font_display_names

    def _calculate_text_properties(self, video_size: Tuple[int, int]) -> Dict[str, Any]:
        """Calculate optimal text properties for video size"""
        width, height = video_size

        # Font size based on video height
        font_size = int(height * self.default_font_size_percent)
        font_size = max(24, min(font_size, 72))  # Clamp between 24-72px

        # Stroke width
        stroke_width = max(2, int(font_size * self.stroke_width_ratio))

        # Text area dimensions
        text_width = int(width * self.text_safe_area_percent)

        # FIXED: Increase bottom margin to prevent text cutoff
        bottom_margin = int(height * 0.15)  # Changed from 0.12 to 0.15 (15% instead of 12%)

        return {
            "font_size": font_size,
            "stroke_width": stroke_width,
            "text_width": text_width,
            "bottom_margin": bottom_margin,
            "video_width": width,
            "video_height": height,
        }

    def _get_font_by_name(
        self, font_name: str, font_size: int, style: str = "modern"
    ) -> ImageFont.ImageFont:
        """Get font by display name"""
        try:
            # Map display name back to file path
            style_fonts = self.available_fonts.get(
                style, self.available_fonts.get("modern", self.fonts)
            )
            style_display_names = self.font_display_names.get(style, [])

            if font_name in style_display_names:
                font_index = style_display_names.index(font_name)
                if font_index < len(style_fonts):
                    font_path = style_fonts[font_index]
                    if font_path == "default":
                        return ImageFont.load_default()
                    else:
                        return ImageFont.truetype(font_path, font_size)

            # Fallback
            return self._get_font_for_style(font_size, style)

        except Exception:
            return self._get_font_for_style(font_size, style)

    def _get_font_for_style(self, font_size: int, style: str = "modern") -> ImageFont.ImageFont:
        """Get font based on style category"""
        style_fonts = self.available_fonts.get(
            style, self.available_fonts.get("modern", self.fonts)
        )

        for font_path in style_fonts:
            try:
                if font_path == "default":
                    return ImageFont.load_default()
                else:
                    return ImageFont.truetype(font_path, font_size)
            except Exception:
                continue

        # Ultimate fallback
        return ImageFont.load_default()

    def _wrap_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> List[str]:
        """Wrap text to fit within max_width"""
        words = text.split()
        lines = []
        current_line = []

        for word in words:
            test_line = " ".join(current_line + [word])
            # Get text width using textbbox (PIL 8.0.0+)
            try:
                bbox = font.getbbox(test_line)
                text_width = bbox[2] - bbox[0]
            except AttributeError:
                # Fallback for older PIL versions
                text_width = font.getsize(test_line)[0]

            if text_width <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(" ".join(current_line))
                    current_line = [word]
                else:
                    # Word is too long, just add it
                    lines.append(word)

        if current_line:
            lines.append(" ".join(current_line))

        return lines

    def _create_text_image_with_style_and_font(
        self,
        text: str,
        properties: Dict[str, Any],
        style: str = "modern",
        font_name: Optional[str] = None,
    ) -> Image.Image:
        """Create text image with specific style and optional font selection"""
        font_size = properties["font_size"]
        stroke_width = properties["stroke_width"]
        text_width = properties["text_width"]

        # Get font (user selection or style default)
        if font_name:
            font = self._get_font_by_name(font_name, font_size, style)
        else:
            font = self._get_font_for_style(font_size, style)

        # Style-specific colors (ENHANCED)
        style_colors = {
            "modern": {"text": (255, 255, 255, 255), "stroke": (0, 0, 0, 180)},
            "cinematic": {"text": (245, 245, 245, 255), "stroke": (20, 20, 20, 220)},
            "elegant": {"text": (240, 240, 240, 255), "stroke": (40, 40, 40, 160)},
            "bold": {"text": (255, 255, 255, 255), "stroke": (0, 0, 0, 255)},
        }

        colors = style_colors.get(style, style_colors["modern"])

        # Wrap text to fit
        lines = self._wrap_text(text, font, text_width)

        # Calculate text dimensions
        line_heights = []
        max_line_width = 0

        for line in lines:
            try:
                bbox = font.getbbox(line)
                line_width = bbox[2] - bbox[0]
                line_height = bbox[3] - bbox[1]
            except AttributeError:
                line_width, line_height = font.getsize(line)

            line_heights.append(line_height)
            max_line_width = max(max_line_width, line_width)

        # Calculate total text block dimensions
        line_spacing = font_size // 4
        total_text_height = sum(line_heights) + (line_spacing * (len(lines) - 1))

        # Add extra padding to prevent cutoff (double bottom padding)
        padding = stroke_width * 2
        extra_bottom_padding = padding * 2
        img_width = max_line_width + (padding * 2)
        img_height = total_text_height + (padding + extra_bottom_padding)

        # Create transparent image
        img = Image.new("RGBA", (img_width, img_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Draw text (no stroke/outline)
        y_offset = padding
        for i, line in enumerate(lines):
            try:
                bbox = font.getbbox(line)
                line_width = bbox[2] - bbox[0]
            except AttributeError:
                line_width = font.getsize(line)[0]
            x_pos = (img_width - line_width) // 2
            draw.text((x_pos, y_offset), line, font=font, fill=colors["text"])
            y_offset += line_heights[i] + line_spacing

        return img

    def _apply_enhanced_transitions_fixed(
        self, text_clip: ImageClip, animation: str = "fade_in_out"
    ) -> ImageClip:
        """FIXED: Apply enhanced animation transitions that actually work"""
        try:
            if animation == "fade_in_out":
                return text_clip.with_effects(
                    [vfx.FadeIn(self.fade_duration), vfx.FadeOut(self.fade_duration)]
                )
            elif animation == "slide_in_from_side":
                # FIXED: Working slide animation with proper position handling
                def slide_pos(t):
                    duration = text_clip.duration
                    if t < self.fade_duration:
                        # Slide in from right
                        progress = t / self.fade_duration
                        start_x = 1920  # Start off-screen right
                        end_x = (
                            text_clip.pos[0]
                            if hasattr(text_clip, "pos")
                            and text_clip.pos
                            and text_clip.pos[0] != "center"
                            else 960
                        )
                        x = start_x + (end_x - start_x) * progress
                        y = (
                            text_clip.pos[1]
                            if hasattr(text_clip, "pos")
                            and text_clip.pos
                            and text_clip.pos[1] != "center"
                            else 540
                        )
                        return (int(x), int(y))
                    elif t > duration - self.fade_duration:
                        # Slide out to left
                        progress = (t - (duration - self.fade_duration)) / self.fade_duration
                        start_x = (
                            text_clip.pos[0]
                            if hasattr(text_clip, "pos")
                            and text_clip.pos
                            and text_clip.pos[0] != "center"
                            else 960
                        )
                        end_x = -200  # End off-screen left
                        x = start_x + (end_x - start_x) * progress
                        y = (
                            text_clip.pos[1]
                            if hasattr(text_clip, "pos")
                            and text_clip.pos
                            and text_clip.pos[1] != "center"
                            else 540
                        )
                        return (int(x), int(y))
                    else:
                        # Static position
                        return (
                            text_clip.pos
                            if hasattr(text_clip, "pos") and text_clip.pos
                            else ("center", "center")
                        )

                # Apply position function with fade
                return text_clip.with_position(slide_pos).with_effects(
                    [
                        vfx.FadeIn(self.fade_duration * 0.3),
                        vfx.FadeOut(self.fade_duration * 0.3),
                    ]
                )
            elif animation == "zoom_in_effect":
                # FIXED: Working zoom animation
                def zoom_size(t):
                    duration = text_clip.duration
                    if t < self.fade_duration:
                        # Zoom from 0.5x to 1.0x
                        progress = t / self.fade_duration
                        scale = 0.5 + (0.5 * progress)
                        return scale
                    elif t > duration - self.fade_duration:
                        # Zoom from 1.0x to 1.2x
                        progress = (t - (duration - self.fade_duration)) / self.fade_duration
                        scale = 1.0 + (0.2 * progress)
                        return scale
                    else:
                        return 1.0

                return text_clip.with_effects(
                    [
                        vfx.Resize(zoom_size),
                        vfx.FadeIn(self.fade_duration * 0.3),
                        vfx.FadeOut(self.fade_duration * 0.3),
                    ]
                )
            else:
                # Default fade
                return text_clip.with_effects(
                    [vfx.FadeIn(self.fade_duration), vfx.FadeOut(self.fade_duration)]
                )
        except Exception as e:
            print(f"⚠ Animation failed, using default fade: {e}")
            try:
                return text_clip.with_effects(
                    [vfx.FadeIn(self.fade_duration), vfx.FadeOut(self.fade_duration)]
                )
            except Exception:
                return text_clip

    def generate_enhanced_text_timeline(
        self,
        script: str,
        video_duration: float,
        style: str = "modern",
        max_overlays: int = 4,
    ) -> List[Dict[str, Any]]:
        """Generate enhanced timeline with style considerations"""
        try:
            # Calculate optimal number of text overlays
            min_overlay_duration = 3.0
            calculated_max = max(1, int(video_duration / (min_overlay_duration + 2.0)))
            actual_max = min(calculated_max, max_overlays)

            # Style-specific prompt adjustments
            style_prompts = {
                "modern": "Create modern, clean text overlays with contemporary language",
                "cinematic": "Create cinematic, dramatic text overlays with impactful phrases",
                "elegant": "Create elegant, sophisticated text overlays with refined language",
                "bold": "Create bold, attention-grabbing text overlays with powerful words",
            }

            style_instruction = style_prompts.get(style, style_prompts["modern"])

            prompt = f"""
            {style_instruction}.
            
            Create a timeline of {actual_max} text overlays for a {video_duration:.1f} second video.
            The video is generated from this script: "{script[:500]}"
            
            REQUIREMENTS:
            - Each text overlay should be 3-4 seconds long
            - Minimum 2 seconds gap between overlays
            - Text should match the {style} style
            - Maximum 4-6 words per overlay for {style} style
            - Start first overlay after 1-2 seconds
            - End last overlay before video ends
            
            For {style} style, use language that is:
            {self._get_style_language_guide(style)}
            
            Return ONLY valid JSON in this format:
            {{
                "overlays": [
                    {{
                        "text": "Amazing Discovery",
                        "start_time": 1.5,
                        "end_time": 4.5,
                        "style": "{style}"
                    }}
                ]
            }}
            """

            result = self.llm_service.generate_content(prompt)
            if result["success"]:
                response_text = result["content"].strip()
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                # Return a default overlay configuration
                return {
                    "overlays": [
                        {
                            "text": "Default Text",
                            "start_time": 0,
                            "end_time": 5,
                            "position": {"x": 50, "y": 50},
                            "style": {
                                "font_size": 48,
                                "font_color": "#FFFFFF",
                                "background_color": "#000000",
                                "font_family": "Arial",
                            },
                        }
                    ]
                }

            # Clean up response
            if "```json" in response_text:
                response_text = response_text.split("```json")[1].split("```")[0]
            elif "```" in response_text:
                response_text = response_text.split("```")[1]
                if response_text.startswith("json"):
                    response_text = response_text[4:]

            timeline_data = json.loads(response_text)
            return self._validate_and_fix_timeline(timeline_data, video_duration)

        except Exception as e:
            print(f"✗ Error generating enhanced timeline: {e}")
            return self._create_fallback_timeline(video_duration)

    def _get_style_language_guide(self, style: str) -> str:
        """Get language guidance for different styles"""
        guides = {
            "modern": "Clean, direct, and contemporary. Use active voice and current terminology.",
            "cinematic": "Dramatic, evocative, and powerful. Use emotional language and strong verbs.",
            "elegant": "Sophisticated, refined, and polished. Use eloquent and tasteful language.",
            "bold": "Strong, impactful, and attention-grabbing. Use powerful words and exclamations.",
        }
        return guides.get(style, guides["modern"])

    def _validate_and_fix_timeline(
        self, timeline_data: Dict, video_duration: float
    ) -> List[Dict[str, Any]]:
        """Validate and fix timeline data"""
        overlays = timeline_data.get("overlays", [])
        if not overlays:
            return self._create_fallback_timeline(video_duration)

        fixed_overlays = []

        for overlay in overlays:
            start_time = float(overlay.get("start_time", 0))
            end_time = float(overlay.get("end_time", start_time + 3))
            text = str(overlay.get("text", "")).strip()

            # Validate timing
            if start_time < 0:
                start_time = 0.5
            if end_time > video_duration - 0.5:
                end_time = video_duration - 0.5
            if end_time <= start_time:
                end_time = start_time + 3.0
            if end_time > video_duration - 0.5:
                continue  # Skip if doesn't fit

            # Validate text
            if not text or len(text) > 50:  # Skip very long text
                continue

            fixed_overlays.append(
                {
                    "text": text,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "style": overlay.get("style", "normal"),
                }
            )

        return fixed_overlays[:5]  # Maximum 5 overlays

    def _create_fallback_timeline(self, video_duration: float) -> List[Dict[str, Any]]:
        """Create a simple fallback timeline"""
        fallback_texts = [
            "Watch This!",
            "Amazing!",
            "Incredible!",
            "Don't Miss!",
            "Wow Factor!",
        ]

        overlays = []
        current_time = 1.0
        duration = 3.0
        gap = 3.0

        for i, text in enumerate(fallback_texts):
            end_time = current_time + duration
            if end_time > video_duration - 1.0:
                break

            overlays.append(
                {
                    "text": text,
                    "start_time": current_time,
                    "end_time": end_time,
                    "duration": duration,
                    "style": "emphasis",
                }
            )

            current_time = end_time + gap
            if current_time > video_duration - duration - 1.0:
                break

        return overlays

    def test_system(self) -> Dict[str, Any]:
        """Test the enhanced system functionality"""
        try:
            print("=== Testing FIXED Enhanced Text Overlay System ===")

            # Test font system
            font_tests = []
            for category, fonts in self.available_fonts.items():
                if fonts:
                    font_tests.append(f"✓ {category}: {len(fonts)} fonts")
                else:
                    font_tests.append(f"✗ {category}: No fonts available")

            # Test style system
            try:
                test_props = self._calculate_text_properties((1920, 1080))
                for style in ["modern", "cinematic", "elegant", "bold"]:
                    test_img = self._create_text_image_with_style_and_font(
                        "Test", test_props, style
                    )
                    if test_img:
                        font_tests.append(f"✓ {style} style working")
                style_test = "✓ All styles functional"
            except Exception as e:
                style_test = f"✗ Style system failed: {str(e)}"

            # Test font selection
            try:
                available_fonts_ui = self.get_available_fonts_for_ui()
                font_selection_test = f"✓ Font selection available: {sum(len(fonts) for fonts in available_fonts_ui.values())} total fonts"
            except Exception as e:
                font_selection_test = f"✗ Font selection failed: {str(e)}"

            # Check video directories
            available_videos = self.get_available_videos_for_overlay()

            # Test enhanced text generation
            try:
                enhanced_timeline = self.generate_enhanced_text_timeline(
                    "Test script about amazing content", 30.0, "cinematic", 3
                )
                enhanced_test = f"✓ Generated {len(enhanced_timeline)} enhanced overlays"
            except Exception as e:
                enhanced_test = f"✗ Enhanced timeline generation failed: {str(e)}"

            # Test animations
            animation_tests = []
            for animation in ["fade_in_out", "slide_in_from_side", "zoom_in_effect"]:
                try:
                    # Create a dummy clip to test animation
                    from moviepy import ColorClip

                    test_clip = ColorClip(size=(100, 50), color=(255, 255, 255), duration=1)
                    animated_clip = self._apply_enhanced_transitions_fixed(test_clip, animation)
                    test_clip.close()
                    animated_clip.close()
                    animation_tests.append(f"✓ {animation} working")
                except Exception as e:
                    animation_tests.append(f"✗ {animation} failed: {str(e)}")

            return {
                "success": len([t for t in font_tests if t.startswith("✓")]) > 0,
                "font_tests": font_tests,
                "style_test": style_test,
                "font_selection_test": font_selection_test,
                "enhanced_test": enhanced_test,
                "animation_tests": animation_tests,
                "available_videos_count": len(available_videos),
                "video_directories_checked": [
                    "output/final_videos",
                    "output/stitched_videos",
                    "output/mixed_videos",
                    "output/text_videos",
                ],
                "paths": {
                    "input": str(self.input_path),
                    "output": str(self.output_path),
                    "temp": str(self.temp_dir),
                },
                "features": {
                    "styles": list(self.available_fonts.keys()),
                    "animations": [
                        "fade_in_out",
                        "slide_in_from_side",
                        "zoom_in_effect",
                    ],
                    "positions": [
                        "top_left",
                        "top_center",
                        "top_right",
                        "center_left",
                        "center",
                        "center_right",
                        "bottom_left",
                        "bottom_center",
                        "bottom_right",
                    ],
                    "background": "TRANSPARENT",
                    "font_priority": "Google Fonts > Ubuntu > DejaVu > Liberation > Noto",
                    "font_selection": "User can choose specific fonts within each style",
                },
                "available_fonts_for_ui": self.get_available_fonts_for_ui(),
                "message": "FIXED Enhanced Text Overlay System ready with working animations, font selection, and easier UI!",
            }

        except Exception as e:
            return {"success": False, "error": f"Enhanced system test failed: {str(e)}"}


def create_text_overlay_system():
    """Factory function to create the enhanced text overlay system"""
    return EnhancedTextOverlaySystem()
