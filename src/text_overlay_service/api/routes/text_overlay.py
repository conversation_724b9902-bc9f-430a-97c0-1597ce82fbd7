# Global imports
import os
import uuid
from typing import Optional
from sqlalchemy import select
from pydantic import BaseModel
from datetime import timed<PERSON><PERSON>, datetime
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends, HTTPException

# Local imports
from src.shared.utils.s3_client import S3Client
from src.shared.core.dependencies import require_auth
from src.shared.config.database import get_database_session
from src.shared.models.database_models import Scrip<PERSON>, VideoAsset
from src.text_overlay_service.services.text_overlay import EnhancedTextOverlaySystem

router = APIRouter(prefix="/text-overlay", tags=["Text Overlay"])


class TextOverlayRequest(BaseModel):
    script_id: str
    style: Optional[str] = "modern"
    animation: Optional[str] = "fade_in_out"
    font_name: Optional[str] = None


class TextOverlayResponse(BaseModel):
    success: bool
    s3_url: Optional[str] = None
    presigned_url: Optional[str] = None
    script_id: Optional[str] = None
    overlays_count: Optional[int] = None
    style: Optional[str] = None
    animation: Optional[str] = None
    font_used: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None


async def store_video_asset(session: AsyncSession, s3_key: str, org_id: str = "default"):
    asset_id = str(uuid.uuid4())
    video_asset = VideoAsset(
        asset_id=asset_id,
        org_id=org_id,
        s3_url=s3_key,
        local_path=None,
        generation_method="audio_mixed",  # Use valid enum value
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    session.add(video_asset)
    await session.commit()
    return asset_id


@router.post("/process", response_model=TextOverlayResponse)
async def process_text_overlay(
    request: TextOverlayRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, email = user_info

    # Validate script access (org-level)
    stmt = select(Script).where(Script.id == request.script_id, Script.org_id == org_id)
    result = await session.execute(stmt)
    script = result.scalar_one_or_none()
    if not script:
        raise HTTPException(status_code=404, detail="Script not found or access denied")

    # Get script text
    script_text = script.title or ""

    # Process overlay with S3 integration
    overlay_system = EnhancedTextOverlaySystem()
    result = overlay_system.process_video_with_enhanced_timeline_s3(
        script_id=request.script_id,
        script=script_text,
        style=request.style,
        animation=request.animation,
        font_name=request.font_name,
    )

    if not result.get("success"):
        return TextOverlayResponse(success=False, error=result.get("error", "Text overlay failed"))

    # Extract S3 key from the S3 URL
    s3_url = result["s3_url"]
    s3_key = s3_url.split(
        f"{overlay_system.s3_bucket}.s3.{overlay_system.s3_client.region}.amazonaws.com/"
    )[1]

    # Store video asset in database
    await store_video_asset(session, s3_key, org_id)

    # Generate presigned URL
    s3_client = S3Client(aws_region="us-east-2")
    presigned_url = s3_client.get_presigned_url(
        bucket=overlay_system.s3_bucket, key=s3_key, ttl=timedelta(hours=1)
    )

    return TextOverlayResponse(
        success=True,
        s3_url=s3_url,
        presigned_url=presigned_url,
        script_id=result.get("script_id"),
        overlays_count=result.get("overlays_count"),
        style=result.get("style"),
        animation=result.get("animation"),
        font_used=result.get("font_used"),
        message=result.get("message"),
    )
