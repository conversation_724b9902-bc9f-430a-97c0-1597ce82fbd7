"""
Celery worker configuration and setup for all VidFlux services
"""

# Global imports
import os
from celery import Celery

# Local imports
from src.shared.config.settings import settings

# Create Celery app
celery_app = Celery("vidflux_worker")

# Get broker and result backend from settings
broker_url = settings.celery_broker_url
result_backend = settings.celery_result_backend

print(f"🔧 Celery Configuration:")
print(f"  Broker URL: {broker_url}")
print(f"  Result Backend: {result_backend}")

# Configure Celery with settings from configuration
celery_app.conf.update(
    broker_url=broker_url,
    result_backend=result_backend,
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_concurrency=settings.celery_worker_concurrency,
    worker_prefetch_multiplier=1,
    task_time_limit=600,
    task_soft_time_limit=540,
    worker_send_task_events=True,
    task_send_sent_event=True,
    worker_enable_remote_control=True,
    worker_disable_rate_limits=False,
    worker_pool_restarts=True,
    worker_cancel_long_running_tasks_on_connection_loss=True,
    worker_max_tasks_per_child=1000,
    task_remote_tracebacks=True,
    worker_direct=True,
    task_default_queue="celery",
    # Fix for task location issues
    task_always_eager=False,
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_errors_even_if_ignored=True,
    task_routes={
        "script_service.generate_script": {"queue": "celery"},
        "script_service.regenerate_script": {"queue": "celery"},
        "scene_service.regenerate_scene": {"queue": "celery"},
    },
    task_annotations={
        "script_service.generate_script": {"rate_limit": "10/m"},
        "script_service.regenerate_script": {"rate_limit": "10/m"},
        "scene_service.regenerate_scene": {"rate_limit": "20/m"},
    },
    # Connection retry settings
    broker_connection_retry_on_startup=True,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
    result_expires=3600,  # Results expire in 1 hour
    # Fix for task location issues
    include=[
        "src.script_service.services.script_service",
        "src.script_service.tasks",
    ],
)

# Auto-discover tasks from service modules
celery_app.autodiscover_tasks(
    [
        "src.script_service.services",
        "src.script_service.tasks",
        "src.audio_service.services",
        "src.image_service",
        "src.video_service.services",
    ]
)

if __name__ == "__main__":
    celery_app.start()
