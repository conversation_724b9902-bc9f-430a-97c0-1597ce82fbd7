# Global imports
import os
import json
import time
import uuid
import shutil
import asyncio
import logging
import requests
import tempfile
from sqlalchemy import asc
from typing import List, Optional
from pydantic import BaseModel
from datetime import timedelta, datetime
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.ext.asyncio import AsyncSession
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import select, and_, desc, update, text
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query

# Local imports
from src.shared.utils.s3_client import S3Client
from src.shared.core.dependencies import require_auth
from src.shared.config.database import get_database_session, get_sync_db
from src.audio_service.services.background_music import BackgroundMusicGenerator
from src.shared.utils.prompt_cleaner import format_audio_prompt_for_display
from src.audio_service.services.background_sound import EnhancedBackgroundSoundGenerator
from src.shared.models.database_models import <PERSON>, Script, VideoAsset, TextAsset, AudioAsset

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Audio Generation"])

# Remove all versioning helpers and logic: get_next_version_number, mark_previous_versions_not_latest, create_audio_asset (if only for versioning), get_latest_audio_asset (if only for versioning), and any version/is_latest logic. Refactor to only check for existence and create if not exists.


# Schemas
class SceneAudioRequest(BaseModel):
    scene_ids: List[str]
    extract_audio_only: bool = False  # Option to extract just audio track


class ScriptAudioRequest(BaseModel):
    script_id: str


class AudioResponse(BaseModel):
    scene_id: Optional[str] = None
    project_id: Optional[str] = None
    audio_url: str
    status: str
    message: Optional[str] = None
    download_url: Optional[str] = None  # New field for direct download
    file_path: Optional[str] = None  # New field for file path
    file_size: Optional[int] = None  # New field for file size


class AudioVersionResponse(BaseModel):
    asset_id: str
    version: int
    audio_url: str
    is_latest: bool
    created_at: str


class BackgroundMusicPromptUpdateRequest(BaseModel):
    prompt: str
    generation_metadata: Optional[dict] = None


class UserBackgroundMusicRequest(BaseModel):
    script_id: str
    presigned_url: str
    filename: Optional[str] = None
    description: Optional[str] = None


class UserBackgroundMusicResponse(BaseModel):
    asset_id: str
    script_id: str
    s3_url: str
    status: str
    message: str


# New schemas for scene-wise voiceover
class VoiceoverGenerationRequest(BaseModel):
    scene_ids: List[str]


class VoiceoverPromptUpdateRequest(BaseModel):
    prompt: str


class SceneVoiceoverResponse(BaseModel):
    scene_id: str
    voiceover_prompt: Optional[str] = None
    voiceover_status: str
    s3_presigned_url: Optional[str] = None
    voiceover_generated_at: Optional[str] = None


class VoiceoverStatusResponse(BaseModel):
    script_id: str
    scenes: List[SceneVoiceoverResponse]


class BackgroundSoundBatchRequest(BaseModel):
    scene_ids: List[str]
    extract_audio_only: bool = False


class SceneAudioStatusResponse(BaseModel):
    scene_id: str
    audio_url: str
    status: str
    message: Optional[str] = None
    success: bool = False  # Added success field
    scene_number: Optional[int] = None  # Allow scene_number to be set


# New schemas for enhanced background sound functionality
class BackgroundSoundGenerationInitResponse(BaseModel):
    scene_ids: List[str]
    message: str
    estimated_completion_time: int  # in seconds
    status: str


class BackgroundSoundStatusResponse(BaseModel):
    scene_id: str
    status: str  # "pending", "queued", "in_progress", "completed", "failed"
    bgsound_prompt: Optional[str] = None  # Formatted prompt for frontend
    audio_url: Optional[str] = None  # Presigned URL when completed
    message: Optional[str] = None
    scene_number: Optional[int] = None
    created_at: Optional[str] = None
    file_size: Optional[int] = None


# New response models for script-level status endpoint (matching video pattern)
class SceneBgSoundStatusResponse(BaseModel):
    """Response for individual scene background sound status"""

    id: str
    scene_number: int
    generation_status: Optional[str] = None
    presigned_url: Optional[str] = None  # Audio URL when completed
    bgsound_prompt: Optional[str] = None  # Formatted background sound prompt


class BackgroundSoundScriptStatusResponse(BaseModel):
    """Response for script background sound generation status check"""

    status: str = "success"
    script_id: str
    scenes: List[SceneBgSoundStatusResponse]


class UpdateBackgroundSoundPromptRequest(BaseModel):
    bgsound_prompt: str


def generate_multiple_bgsounds_controlled_parallel(scene_ids: List[str], org_id: str, user_id: str):
    """
    Generate background sounds using fully parallel processing with MMAudio only.
    Processes all scenes simultaneously for maximum speed.
    """
    logger.info(
        f"🎵 Starting fully parallel background sound generation for {len(scene_ids)} scenes"
    )

    import concurrent.futures

    def generate_single_scene_parallel(scene_id: str) -> dict:
        """Generate background sound for a single scene without concurrency limits"""
        try:
            logger.info(f"🎵 Starting generation for scene {scene_id}")

            # Generate background sound for this scene
            _generate_bgsound_background(scene_id, org_id, user_id)

            # Check if generation actually succeeded
            db = next(get_sync_db())
            try:
                audio_asset = (
                    db.query(AudioAsset)
                    .filter(
                        and_(
                            AudioAsset.org_id == org_id,
                            AudioAsset.scene_id == scene_id,
                            AudioAsset.source_type == "background_sound",
                        )
                    )
                    .first()
                )

                if audio_asset and audio_asset.generation_status == "completed":
                    logger.info(f"✅ Background sound for scene {scene_id} completed successfully")
                    return {"scene_id": scene_id, "success": True}
                elif audio_asset and audio_asset.generation_status == "failed":
                    error_metadata = audio_asset.generation_metadata or {}
                    error_msg = error_metadata.get("error", "Unknown error")
                    logger.error(f"❌ Background sound for scene {scene_id} failed: {error_msg}")
                    return {"scene_id": scene_id, "success": False, "error": error_msg}
                else:
                    logger.error(f"❌ Background sound for scene {scene_id} - unknown status")
                    return {"scene_id": scene_id, "success": False, "error": "Unknown status"}
            finally:
                db.close()

        except Exception as e:
            logger.error(
                f"❌ Background sound for scene {scene_id} failed with exception: {str(e)}"
            )
            return {"scene_id": scene_id, "success": False, "error": str(e)}

    # Use ThreadPoolExecutor with enough workers for all scenes
    max_workers = len(scene_ids)  # One worker per scene for maximum parallelism
    successful_scenes = []
    failed_scenes = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks simultaneously
        future_to_scene = {
            executor.submit(generate_single_scene_parallel, scene_id): scene_id
            for scene_id in scene_ids
        }

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_scene):
            scene_id = future_to_scene[future]
            try:
                result = future.result()
                if result.get("success"):
                    successful_scenes.append(scene_id)
                else:
                    failed_scenes.append(scene_id)
            except Exception as e:
                logger.error(f"❌ Exception processing scene {scene_id}: {str(e)}")
                failed_scenes.append(scene_id)

    logger.info(
        f"🎵 Fully parallel background sound generation completed: {len(successful_scenes)} successful, {len(failed_scenes)} failed"
    )

    # Log details about failed scenes
    if failed_scenes:
        logger.error(f"❌ Failed scenes: {failed_scenes}")

    return {"successful": successful_scenes, "failed": failed_scenes}


def delete_previous_bgsounds_for_scene(scene_id: str, org_id: str) -> int:
    """
    Delete all previous background sound files and database records for a scene
    Returns the number of deleted records
    """
    try:
        db_session = next(get_sync_db())

        # Get all existing background sound assets for this scene
        existing_bgsounds = (
            db_session.query(AudioAsset)
            .filter(
                and_(
                    AudioAsset.scene_id == scene_id,
                    AudioAsset.org_id == org_id,
                    AudioAsset.source_type == "background_sound",
                )
            )
            .all()
        )

        if not existing_bgsounds:
            logger.info(f"ℹ️ No previous background sounds found for scene {scene_id}")
            return 0

        logger.info(
            f"🎵 Deleting {len(existing_bgsounds)} previous background sounds for scene {scene_id}"
        )

        # Delete files from S3 and database records
        s3_client = S3Client("us-east-2")
        deleted_count = 0

        for bgsound in existing_bgsounds:
            try:
                # Delete from S3 if URL exists
                if bgsound.s3_url:
                    logger.info(f"🗑️ Deleting S3 file: {bgsound.s3_url}")
                    s3_client.delete_file("assets-vidflux", bgsound.s3_url)
                    logger.info(f"✅ Deleted S3 file: {bgsound.s3_url}")

                # Delete database record
                db_session.delete(bgsound)
                deleted_count += 1
                logger.info(f"🗑️ Deleted AudioAsset record: {bgsound.asset_id}")

            except Exception as e:
                logger.error(f"❌ Error processing background sound {bgsound.asset_id}: {e}")

        # Commit database changes
        db_session.commit()
        logger.info(f"✅ Deleted {deleted_count} previous background sounds for scene {scene_id}")
        return deleted_count

    except Exception as e:
        logger.error(f"❌ Error deleting previous background sounds for scene {scene_id}: {e}")
        return 0


# --- Enhanced Background Sound Endpoints with Concurrent Processing ---


@router.post("/background-sound/generate", response_model=BackgroundSoundGenerationInitResponse)
async def generate_background_sounds_concurrent(
    request: SceneAudioRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Generate background sounds for multiple scenes concurrently.
    Returns immediately with scene IDs and starts concurrent generation.
    """
    org_id, user_id, _ = user_info

    if not request.scene_ids:
        raise HTTPException(status_code=400, detail="No scene_ids provided")

    # Validate all scenes exist and belong to user
    valid_scene_ids = []
    for scene_id in request.scene_ids:
        scene_stmt = select(Scene).where(and_(Scene.id == scene_id, Scene.status == "active"))
        scene_result = await session.execute(scene_stmt)
        scene = scene_result.scalar_one_or_none()

        if not scene:
            logger.warning(f"Scene {scene_id} not found or inactive")
            continue

        # Check if scene belongs to user's script
        script_stmt = select(Script).where(
            and_(Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id)
        )
        script_result = await session.execute(script_stmt)
        script = script_result.scalar_one_or_none()

        if not script:
            logger.warning(
                f"Script {scene.script_id} not found or access denied for scene {scene_id}"
            )
            continue

        valid_scene_ids.append(scene_id)

    if not valid_scene_ids:
        raise HTTPException(status_code=400, detail="No valid scene_ids provided")

    # Set all valid scenes to 'queued' status
    for scene_id in valid_scene_ids:
        # Use a more robust approach to handle potential duplicates
        try:
            # First, try to get existing asset
            stmt = select(AudioAsset).where(
                and_(
                    AudioAsset.org_id == org_id,
                    AudioAsset.scene_id == scene_id,
                    AudioAsset.source_type == "background_sound",
                )
            )
            result = await session.execute(stmt)
            audio_assets = result.scalars().all()

            if len(audio_assets) > 1:
                # Handle duplicates by keeping the first one and deleting others
                logger.warning(
                    f"Found {len(audio_assets)} duplicate background sound assets for scene {scene_id}, cleaning up"
                )
                audio_asset = audio_assets[0]
                for duplicate in audio_assets[1:]:
                    await session.delete(duplicate)
            elif len(audio_assets) == 1:
                audio_asset = audio_assets[0]
            else:
                audio_asset = None

            if audio_asset:
                await session.execute(
                    update(AudioAsset)
                    .where(AudioAsset.asset_id == audio_asset.asset_id)
                    .values(generation_status="queued")
                )
            else:
                asset_id = str(uuid.uuid4())
                new_asset = AudioAsset(
                    org_id=org_id,
                    asset_id=asset_id,
                    local_path=None,
                    s3_url="",
                    source_type="background_sound",
                    scene_id=scene_id,
                    generation_status="queued",
                    generation_metadata={
                        "info": "Scheduled for controlled parallel background sound generation"
                    },
                )
                session.add(new_asset)
        except Exception as e:
            logger.error(f"Error processing scene {scene_id}: {e}")
            continue

    await session.commit()

    # Start controlled parallel background sound generation for all scenes
    logger.info(
        f"🎵 Starting controlled parallel background sound generation for {len(valid_scene_ids)} scenes"
    )
    background_tasks.add_task(
        generate_multiple_bgsounds_controlled_parallel, valid_scene_ids, org_id, user_id
    )

    logger.info(f"✅ Controlled parallel background sound generation task added successfully")

    return BackgroundSoundGenerationInitResponse(
        scene_ids=valid_scene_ids,
        message=f"Controlled parallel background sound generation started for {len(valid_scene_ids)} scenes",
        estimated_completion_time=max(
            120, len(valid_scene_ids) * 30
        ),  # 30 seconds per scene minimum, 2 minutes minimum
        status="processing",
    )


@router.get(
    "/background-sound/status/{script_id}", response_model=BackgroundSoundScriptStatusResponse
)
async def get_background_sound_generation_status(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Get background sound generation status for all scenes in a script
    - **script_id**: UUID of the script
    Returns: Current generation status of all scenes with background sound prompts and audio URLs
    """
    org_id, user_id, _ = user_info

    logger.info(f"🔍 Checking background sound generation status for script {script_id}")

    # Validate script exists and belongs to user
    script_stmt = select(Script).where(
        and_(Script.id == script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        logger.warning(f"Script {script_id} not found or access denied")
        raise HTTPException(status_code=404, detail="Script not found")

    # Query all scenes for this script with background sound information
    scenes_stmt = (
        select(Scene.id, Scene.scene_number, Scene.bgsound_prompt)
        .where(Scene.script_id == script_id)
        .order_by(Scene.scene_number)
    )

    scenes_result = await session.execute(scenes_stmt)
    scenes = scenes_result.all()

    logger.info(f"📊 Found {len(scenes)} scenes for script {script_id}")

    # Get background sound generation status for each scene
    scenes_data = []
    for scene in scenes:
        # Get the latest AudioAsset for this scene
        audio_stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.org_id == org_id,
                    AudioAsset.scene_id == scene.id,
                    AudioAsset.source_type == "background_sound",
                )
            )
            .order_by(AudioAsset.created_at.desc())
            .limit(1)
        )

        audio_result = await session.execute(audio_stmt)
        audio_asset = audio_result.scalar_one_or_none()

        # Determine status and get presigned URL if completed
        presigned_url = None
        actual_status = "pending"  # Default status

        if audio_asset:
            actual_status = audio_asset.generation_status
            if audio_asset.generation_status == "completed" and audio_asset.s3_url:
                # Get presigned URL for completed background sound
                s3_client = S3Client("us-east-2")
                presigned_url = s3_client.get_presigned_url(
                    "assets-vidflux", audio_asset.s3_url, timedelta(hours=1)
                )

        # Format the background sound prompt for display
        formatted_prompt = None
        if scene.bgsound_prompt:
            formatted_prompt = format_audio_prompt_for_display(scene.bgsound_prompt)

        scenes_data.append(
            SceneBgSoundStatusResponse(
                id=str(scene.id),
                scene_number=scene.scene_number,
                generation_status=actual_status,
                presigned_url=presigned_url,
                bgsound_prompt=formatted_prompt,  # Include formatted background sound prompt
            )
        )

    logger.info(f"✅ Returning background sound status for {len(scenes_data)} scenes")

    return BackgroundSoundScriptStatusResponse(
        status="success", script_id=script_id, scenes=scenes_data
    )


@router.put("/background-sound/update-prompt/{scene_id}")
async def update_background_sound_prompt_and_regenerate(
    scene_id: str,
    request: UpdateBackgroundSoundPromptRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Update background sound prompt for a scene and regenerate immediately.
    Deletes old files and creates new ones with the updated prompt.
    """
    org_id, user_id, _ = user_info

    # Validate scene exists and belongs to user
    scene_stmt = select(Scene).where(and_(Scene.id == scene_id, Scene.status == "active"))
    scene_result = await session.execute(scene_stmt)
    scene = scene_result.scalar_one_or_none()

    if not scene:
        raise HTTPException(status_code=404, detail="Scene not found")

    # Check if scene belongs to user's script
    script_stmt = select(Script).where(
        and_(Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update the audio asset's prompt and set processing status
    await session.execute(
        update(AudioAsset)
        .where(
            and_(
                AudioAsset.org_id == org_id,
                AudioAsset.scene_id == scene_id,
                AudioAsset.source_type == "background_sound",
            )
        )
        .values(generation_status="processing")
    )

    await session.commit()

    # Start background regeneration with custom prompt
    background_tasks.add_task(
        _regenerate_bgsound_with_custom_prompt_background,
        scene_id,
        org_id,
        user_id,
        request.bgsound_prompt,
    )

    logger.info(
        f"✅ Updated background sound prompt for scene {scene_id}: {request.bgsound_prompt}"
    )
    logger.info(
        f"🚀 Started background sound regeneration for scene {scene_id} with updated prompt"
    )

    return {
        "message": "Background sound prompt updated and regeneration started",
        "scene_id": scene_id,
        "new_prompt": request.bgsound_prompt,
        "status": "processing",
    }


# --- Background Task Functions ---


def _generate_bgsound_background(scene_id: str, org_id: str, user_id: str):
    """
    Enhanced background task to generate background sound for a scene with prompt storage.
    """
    logger.info(f"🎵 BACKGROUND TASK STARTED for scene_id: {scene_id}")
    try:
        db = next(get_sync_db())
        try:
            # Set status to in_progress
            db.execute(
                text(
                    """
                UPDATE audio_assets 
                SET generation_status = 'in_progress' 
                WHERE org_id = :org_id 
                AND scene_id = :scene_id 
                AND source_type = 'background_sound'
                """
                ),
                {"org_id": org_id, "scene_id": str(scene_id)},
            )
            db.commit()

            # Fetch scene and script
            scene = db.query(Scene).filter(Scene.id == scene_id).first()
            if not scene:
                logger.error(f"❌ Scene {scene_id} not found")
                return

            script = (
                db.query(Script)
                .filter(
                    Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id
                )
                .first()
            )

            if not script:
                logger.error(f"❌ Script not found or access denied for scene {scene_id}")
                return

            # Fetch the most recent video asset for this scene
            video_asset = (
                db.query(VideoAsset)
                .filter(VideoAsset.org_id == org_id, VideoAsset.scene_id == scene_id)
                .order_by(VideoAsset.created_at.desc())
                .first()
            )

            if not video_asset:
                logger.error(f"❌ No video asset found for scene {scene_id}")

                # Update audio asset status to failed with specific error
                db.execute(
                    text(
                        """
                    UPDATE audio_assets 
                    SET generation_status = 'failed',
                        generation_metadata = :metadata
                    WHERE org_id = :org_id 
                    AND scene_id = :scene_id 
                    AND source_type = 'background_sound'
                    """
                    ),
                    {
                        "org_id": org_id,
                        "scene_id": str(scene_id),
                        "metadata": json.dumps(
                            {
                                "error": "No video asset found for scene - cannot generate background sound",
                                "error_type": "missing_video",
                                "timestamp": time.time(),
                            }
                        ),
                    },
                )
                db.commit()
                return

            # Get video path (S3 URL or local path)
            if video_asset.local_path:
                video_path = video_asset.local_path
            else:
                S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
                S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
                s3_client = S3Client(S3_REGION)
                video_path = s3_client.get_presigned_url(
                    S3_BUCKET, video_asset.s3_url, timedelta(hours=1)
                )

            if not video_path:
                logger.error(f"❌ No valid video path for scene {scene_id}")
                return

            # Generate background sound
            generator = EnhancedBackgroundSoundGenerator()

            # Check if there's an existing audio asset with a prompt
            existing_audio = (
                db.query(AudioAsset)
                .filter(
                    AudioAsset.org_id == org_id,
                    AudioAsset.scene_id == scene_id,
                    AudioAsset.source_type == "background_sound",
                )
                .first()
            )

            # Use existing prompt from scene if available, otherwise generate new one
            if scene.bgsound_prompt:
                logger.info(f"🎵 Using existing background sound prompt: {scene.bgsound_prompt}")
                base_sound_prompt = scene.bgsound_prompt
            else:
                scene_description = scene.description or scene.visual_description or "ambient scene"
                base_sound_prompt = generator.generate_base_sound_prompt(scene_description)

                # Store the generated prompt in the scene
                scene.bgsound_prompt = base_sound_prompt
                db.commit()
                logger.info(
                    f"✅ Updated background sound prompt for scene {scene_id}: {base_sound_prompt}"
                )

            unique_seed = abs(hash(scene_id)) % 1000000
            asset_id = str(uuid.uuid4())

            result = generator.generate_background_sound_for_video_sync(
                video_path=video_path,
                base_sound_prompt=base_sound_prompt,
                scene_id=scene_id,
                asset_id=asset_id,
                seed=unique_seed,
            )

            if result.get("success"):
                # Update AudioAsset with the results
                audio_asset = (
                    db.query(AudioAsset)
                    .filter(
                        AudioAsset.org_id == org_id,
                        AudioAsset.scene_id == scene_id,
                        AudioAsset.source_type == "background_sound",
                    )
                    .first()
                )

                if audio_asset:
                    audio_asset.s3_url = result.get("s3_key", "")
                    audio_asset.generation_status = "completed"
                    audio_asset.generation_metadata = {
                        "sound_prompt": base_sound_prompt,
                        "duration": result.get("duration"),
                        "settings": result.get("settings", {}),
                        "file_size": result.get("file_size"),
                    }

                    # Update scene audio_generation_status to completed
                    scene = db.query(Scene).filter(Scene.id == scene_id).first()
                    if scene:
                        scene.audio_generation_status = "completed"

                    db.commit()
                    logger.info(f"✅ Background sound generation completed for scene {scene_id}")
                else:
                    logger.error(f"❌ AudioAsset not found for scene {scene_id}")
            else:
                # Handle different types of failures
                error_info = result.get("error", "Unknown error")
                error_type = result.get("error_type", "unknown")
                retry_recommended = result.get("retry_recommended", False)

                # Set status to failed for both AudioAsset and Scene
                db.execute(
                    text(
                        """
                    UPDATE audio_assets 
                    SET generation_status = 'failed' 
                    WHERE org_id = :org_id 
                    AND scene_id = :scene_id 
                    AND source_type = 'background_sound'
                    """
                    ),
                    {"org_id": org_id, "scene_id": str(scene_id)},
                )

                # Update scene audio_generation_status to failed
                scene = db.query(Scene).filter(Scene.id == scene_id).first()
                if scene:
                    scene.audio_generation_status = "failed"

                db.commit()

                if error_type == "quota_exceeded":
                    logger.error(f"❌ GPU quota exceeded for scene {scene_id}. Try again later.")
                elif error_type == "gradio_error":
                    logger.error(f"❌ Gradio service error for scene {scene_id}: {error_info}")
                elif retry_recommended:
                    logger.error(f"❌ Retryable error for scene {scene_id}: {error_info}")
                else:
                    logger.error(
                        f"❌ Background sound generation failed for scene {scene_id}: {error_info}"
                    )

                # Store error information in the audio asset metadata
                audio_asset = (
                    db.query(AudioAsset)
                    .filter(
                        AudioAsset.org_id == org_id,
                        AudioAsset.scene_id == scene_id,
                        AudioAsset.source_type == "background_sound",
                    )
                    .first()
                )

                if audio_asset:
                    audio_asset.generation_metadata = {
                        "error": error_info,
                        "error_type": error_type,
                        "retry_recommended": retry_recommended,
                        "failed_at": time.time(),
                        "sound_prompt": base_sound_prompt,
                    }
                    db.commit()

        finally:
            db.close()

    except Exception as e:
        logger.error(f"❌ Background sound generation failed for scene {scene_id}: {e}")


def _regenerate_bgsound_with_custom_prompt_background(
    scene_id: str, org_id: str, user_id: str, custom_prompt: str
):
    """
    Background task to regenerate background sound with a custom prompt.
    Updates the existing AudioAsset instead of deleting and recreating.
    """
    logger.info(
        f"🎵 Starting background sound regeneration with custom prompt for scene {scene_id}"
    )
    logger.info(f"🎯 Custom prompt: {custom_prompt}")

    try:
        db = next(get_sync_db())
        try:
            # Check if AudioAsset exists, if not create one
            existing_audio = (
                db.query(AudioAsset)
                .filter(
                    AudioAsset.org_id == org_id,
                    AudioAsset.scene_id == scene_id,
                    AudioAsset.source_type == "background_sound",
                )
                .first()
            )

            if existing_audio:
                # Update existing asset and scene with custom prompt and reset status
                scene.bgsound_prompt = custom_prompt  # Store prompt in scene
                existing_audio.generation_status = "in_progress"
                existing_audio.generation_metadata = {
                    "info": "Regenerating with custom prompt",
                    "custom_prompt": custom_prompt,
                    "regenerated_at": time.time(),
                }
                existing_audio.s3_url = ""  # Clear old URL
                logger.info(f"✅ Updated existing AudioAsset with custom prompt: {custom_prompt}")
            else:
                # Create new AudioAsset if none exists and update scene prompt
                scene.bgsound_prompt = custom_prompt  # Store prompt in scene
                existing_audio = AudioAsset(
                    org_id=org_id,
                    asset_id=str(uuid.uuid4()),
                    local_path=None,
                    s3_url="",
                    source_type="background_sound",
                    scene_id=scene_id,
                    generation_status="in_progress",
                    generation_metadata={
                        "info": "Creating with custom prompt",
                        "custom_prompt": custom_prompt,
                    },
                )
                db.add(existing_audio)
                logger.info(f"✅ Created new AudioAsset with custom prompt: {custom_prompt}")

            db.commit()

        finally:
            db.close()

        # Generate with the custom prompt
        _generate_bgsound_with_custom_prompt(scene_id, org_id, user_id, custom_prompt)

        logger.info(f"✅ Background sound regeneration completed for scene {scene_id}")

    except Exception as e:
        logger.error(f"❌ Background sound regeneration failed for scene {scene_id}: {str(e)}")


def _generate_bgsound_with_custom_prompt(
    scene_id: str, org_id: str, user_id: str, custom_prompt: str
):
    """
    Generate background sound using a specific custom prompt (bypasses prompt generation)
    """
    logger.info(f"🎵 Generating background sound with custom prompt for scene {scene_id}")

    try:
        db = next(get_sync_db())
        try:
            # Set status to in_progress
            db.execute(
                text(
                    """
                UPDATE audio_assets 
                SET generation_status = 'in_progress' 
                WHERE org_id = :org_id 
                AND scene_id = :scene_id 
                AND source_type = 'background_sound'
                """
                ),
                {"org_id": org_id, "scene_id": str(scene_id)},
            )
            db.commit()

            # Fetch scene and script
            scene = db.query(Scene).filter(Scene.id == scene_id).first()
            if not scene:
                logger.error(f"❌ Scene {scene_id} not found")
                return

            script = (
                db.query(Script)
                .filter(
                    Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id
                )
                .first()
            )

            if not script:
                logger.error(f"❌ Script not found or access denied for scene {scene_id}")
                return

            # Get video asset for the scene
            video_asset = (
                db.query(VideoAsset)
                .filter(VideoAsset.scene_id == scene_id, VideoAsset.org_id == org_id)
                .first()
            )

            if not video_asset:
                logger.error(f"❌ No video asset found for scene {scene_id}")

                # Update audio asset status to failed with specific error
                db.execute(
                    text(
                        """
                    UPDATE audio_assets 
                    SET generation_status = 'failed',
                        generation_metadata = :metadata
                    WHERE org_id = :org_id 
                    AND scene_id = :scene_id 
                    AND source_type = 'background_sound'
                    """
                    ),
                    {
                        "org_id": org_id,
                        "scene_id": str(scene_id),
                        "metadata": json.dumps(
                            {
                                "error": "No video asset found for scene - cannot generate background sound",
                                "error_type": "missing_video",
                                "timestamp": time.time(),
                            }
                        ),
                    },
                )
                db.commit()
                return

            # Get video path (S3 URL or local path)
            if video_asset.local_path:
                video_path = video_asset.local_path
            else:
                S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
                S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
                s3_client = S3Client(S3_REGION)
                video_path = s3_client.get_presigned_url(
                    S3_BUCKET, video_asset.s3_url, timedelta(hours=1)
                )

            if not video_path:
                logger.error(f"❌ No valid video path for scene {scene_id}")
                return

            # Generate background sound using the custom prompt
            generator = EnhancedBackgroundSoundGenerator()
            logger.info(f"🎯 Using custom prompt for generation: {custom_prompt}")

            unique_seed = abs(hash(scene_id)) % 1000000
            asset_id = str(uuid.uuid4())

            result = generator.generate_background_sound_for_video_sync(
                video_path=video_path,
                base_sound_prompt=custom_prompt,  # Use the custom prompt directly
                scene_id=scene_id,
                asset_id=asset_id,
                seed=unique_seed,
            )

            if result.get("success"):
                # Update AudioAsset with the results
                audio_asset = (
                    db.query(AudioAsset)
                    .filter(
                        AudioAsset.org_id == org_id,
                        AudioAsset.scene_id == scene_id,
                        AudioAsset.source_type == "background_sound",
                    )
                    .first()
                )

                if audio_asset:
                    audio_asset.s3_url = result.get("s3_key", "")
                    audio_asset.generation_status = "completed"
                    audio_asset.generation_metadata = {
                        "sound_prompt": custom_prompt,
                        "duration": result.get("duration"),
                        "settings": result.get("settings", {}),
                        "file_size": result.get("file_size"),
                        "custom_prompt_used": True,
                    }

                    # Update scene audio_generation_status to completed
                    scene = db.query(Scene).filter(Scene.id == scene_id).first()
                    if scene:
                        scene.audio_generation_status = "completed"

                    db.commit()
                    logger.info(
                        f"✅ Background sound generation completed for scene {scene_id} with custom prompt"
                    )
                else:
                    logger.error(f"❌ AudioAsset not found for scene {scene_id}")
            else:
                # Handle different types of failures
                error_info = result.get("error", "Unknown error")
                error_type = result.get("error_type", "unknown")
                retry_recommended = result.get("retry_recommended", False)

                # Set status to failed for both AudioAsset and Scene
                db.execute(
                    text(
                        """
                    UPDATE audio_assets 
                    SET generation_status = 'failed' 
                    WHERE org_id = :org_id 
                    AND scene_id = :scene_id 
                    AND source_type = 'background_sound'
                    """
                    ),
                    {"org_id": org_id, "scene_id": str(scene_id)},
                )

                # Update scene audio_generation_status to failed
                scene = db.query(Scene).filter(Scene.id == scene_id).first()
                if scene:
                    scene.audio_generation_status = "failed"

                db.commit()
                logger.error(
                    f"❌ Background sound generation failed for scene {scene_id} with custom prompt: {error_info}"
                )

        finally:
            db.close()

    except Exception as e:
        logger.error(
            f"❌ Background sound generation with custom prompt failed for scene {scene_id}: {e}"
        )


# --- Legacy Background task function (keeping for compatibility) ---
def run_background_sound_generation(org_id, user_id, scene_id):
    import asyncio
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker
    from src.shared.models.database_models import Scene, Script, VideoAsset, AudioAsset
    from src.audio_service.services.background_sound import EnhancedBackgroundSoundGenerator
    from sqlalchemy import select, and_, desc, update
    from src.shared.utils.s3_client import S3Client
    from datetime import timedelta
    import uuid

    # Set up DB connection using shared settings
    from src.shared.config.settings import settings

    engine = create_async_engine(settings.database_url, future=True, echo=False)
    async_session = sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)

    async def _run():
        async with async_session() as session:
            # Set AudioAsset status to in_progress
            await session.execute(
                update(AudioAsset)
                .where(
                    and_(
                        AudioAsset.org_id == org_id,
                        AudioAsset.scene_id == scene_id,
                        AudioAsset.source_type == "background_sound",
                    )
                )
                .values(generation_status="in_progress")
            )

            # Set Scene audio_generation_status to in_progress
            await session.execute(
                update(Scene)
                .where(Scene.id == scene_id)
                .values(audio_generation_status="in_progress")
            )

            await session.commit()
            # Fetch scene and script
            stmt = (
                select(Scene, Script)
                .join(Script, Scene.script_id == Script.id)
                .where(Scene.id == scene_id, Script.org_id == org_id, Script.user_id == user_id)
            )
            result = await session.execute(stmt)
            row = result.first()
            if not row:
                return
            scene, script = row
            # Fetch the most recent video asset for this scene
            video_stmt = (
                select(VideoAsset)
                .where(and_(VideoAsset.org_id == org_id, VideoAsset.scene_id == scene_id))
                .order_by(desc(VideoAsset.created_at))
                .limit(1)
            )
            video_result = await session.execute(video_stmt)
            video_asset = video_result.scalar_one_or_none()
            if not video_asset:
                return
            # Use local path if available, otherwise fall back to s3_url (which is now just the S3 key)
            if video_asset.local_path:
                video_path = video_asset.local_path
            else:
                S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
                S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
                s3_client = S3Client(S3_REGION)
                video_path = s3_client.get_presigned_url(
                    S3_BUCKET, video_asset.s3_url, timedelta(hours=1)
                )
            if not video_path:
                return
            # Generate sound prompt from scene description
            generator = EnhancedBackgroundSoundGenerator()
            scene_description = scene.description or scene.visual_description or "ambient scene"
            base_sound_prompt = generator.generate_base_sound_prompt(scene_description)
            unique_seed = abs(hash(scene_id)) % 1000000
            asset_id = str(uuid.uuid4())
            result = generator.generate_background_sound_for_video_sync(
                video_path=video_path,
                base_sound_prompt=base_sound_prompt,
                scene_id=scene_id,
                asset_id=asset_id,
                seed=unique_seed,
            )
            if result.get("success"):
                # Save audio asset to DB (simplified, only for success)
                s3_key = result.get("s3_key")
                s3_url = result.get("s3_url")
                if not s3_key or not s3_url:
                    logger.error(
                        f"Background sound S3 upload failed for scene {scene_id}. Not saving AudioAsset.",
                        exc_info=True,
                    )
                    await session.execute(
                        update(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.org_id == org_id,
                                AudioAsset.scene_id == scene_id,
                                AudioAsset.source_type == "background_sound",
                            )
                        )
                        .values(generation_status="failed")
                    )
                    await session.commit()
                    return
                await session.execute(
                    update(AudioAsset)
                    .where(
                        and_(
                            AudioAsset.org_id == org_id,
                            AudioAsset.scene_id == scene_id,
                            AudioAsset.source_type == "background_sound",
                        )
                    )
                    .values(
                        s3_url=s3_key,
                        generation_status="completed",
                        generation_metadata={
                            "sound_prompt": base_sound_prompt,
                            "scene_id": scene_id,
                            "scene_description": scene_description,
                            "generation_result": result,
                        },
                    )
                )

                # Update scene audio_generation_status to completed
                await session.execute(
                    update(Scene)
                    .where(Scene.id == scene_id)
                    .values(audio_generation_status="completed")
                )

                await session.commit()
            else:
                await session.execute(
                    update(AudioAsset)
                    .where(
                        and_(
                            AudioAsset.org_id == org_id,
                            AudioAsset.scene_id == scene_id,
                            AudioAsset.source_type == "background_sound",
                        )
                    )
                    .values(generation_status="failed")
                )

                # Update scene audio_generation_status to failed
                await session.execute(
                    update(Scene)
                    .where(Scene.id == scene_id)
                    .values(audio_generation_status="failed")
                )

                await session.commit()

    asyncio.run(_run())


# --- Background Sound Status (by scene_ids) ---
@router.get(
    "/background-sound/status",
    response_model=List[SceneAudioStatusResponse],
    include_in_schema=False,
)
async def get_background_sound_status(
    scene_ids: str = Query(..., description="Comma-separated list of scene IDs"),
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Get background sound status for a list of scene IDs."""
    org_id, user_id, _ = user_info
    from src.shared.models.database_models import AudioAsset
    from sqlalchemy import desc, and_, select

    scene_id_list = [sid.strip() for sid in scene_ids.split(",") if sid.strip()]
    responses = []
    S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
    S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
    s3_client = S3Client(S3_REGION)
    from datetime import timedelta

    for scene_id in scene_id_list:
        stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.org_id == org_id,
                    AudioAsset.scene_id == scene_id,
                    AudioAsset.source_type == "background_sound",
                )
            )
            .order_by(desc(AudioAsset.created_at))
            .limit(1)
        )
        result = await session.execute(stmt)
        audio_asset = result.scalar_one_or_none()
        audio_url = ""
        status = "pending"
        message = "Background sound not yet generated."
        if audio_asset:
            s3_url = audio_asset.s3_url
            if s3_url and "X-Amz-" not in s3_url:
                if s3_url.startswith("http") and ".amazonaws.com/" in s3_url:
                    s3_key = s3_url.split(".amazonaws.com/")[-1]
                else:
                    s3_key = s3_url
                audio_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
            else:
                audio_url = s3_url
            status = audio_asset.generation_status or "pending"
            message = None
        responses.append(
            SceneAudioStatusResponse(
                scene_id=str(scene_id),
                audio_url=audio_url,
                status=status,
                message=message,
                success=bool(audio_url),
            )
        )
    return responses


# --- Background Sound Status (per script_id, for internal use only) ---
@router.get(
    "/background-sound/{script_id}",
    response_model=List[SceneAudioStatusResponse],
    include_in_schema=False,
)
async def get_background_sounds(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """(Internal) Get all background sounds for all scenes in a script."""
    org_id, user_id, _ = user_info
    from src.shared.models.database_models import Scene
    from sqlalchemy import select

    result = await session.execute(select(Scene).where(Scene.script_id == script_id))
    scenes = result.scalars().all()
    scene_id_to_number = {str(scene.id): scene.scene_number for scene in scenes}
    scene_id_list = [str(scene.id) for scene in scenes]
    # Call the new endpoint logic
    responses = await get_background_sound_status(
        scene_ids=",".join(scene_id_list), user_info=user_info, session=session
    )
    # Add scene_number to each response
    for resp in responses:
        resp.scene_number = scene_id_to_number.get(resp.scene_id)
    return responses


# --- Download Background Sound MP4 ---
# @router.get("/background-sound/download/{scene_id}")
# async def download_background_sound_mp4(
#     scene_id: str,
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session)
# ):
#     """
#     Download the MP4 file with background audio for a specific scene.
#     Returns the MP4 file as a downloadable attachment.
#     """
#     org_id, user_id, _ = user_info
#
#     # Verify scene exists and belongs to user
#     scene_stmt = (
#         select(Scene, Script)
#         .join(Script, Scene.script_id == Script.id)
#         .where(
#             Scene.id == scene_id,
#             Script.org_id == org_id,
#             Script.user_id == user_id
#         )
#     )
#     result = await session.execute(scene_stmt)
#     row = result.first()
#
#     if not row:
#         raise HTTPException(status_code=404, detail="Scene not found or access denied")
#
#     scene, script = row
#
#     # Get the audio asset for this specific scene by filtering metadata
#     audio_stmt = select(AudioAsset).where(
#         and_(
#             AudioAsset.org_id == org_id,
#             AudioAsset.source_type == "background_sound",
#             # Filter by scene_id directly
#             AudioAsset.scene_id == scene_id
#         )
#     ).order_by(desc(AudioAsset.created_at)).limit(1)
#     audio_result = await session.execute(audio_stmt)
#     audio_asset = audio_result.scalar_one_or_none()
#
#     if not audio_asset or not audio_asset.local_path:
#         raise HTTPException(status_code=404, detail="No background sound file found for this scene")
#
#     if not os.path.exists(audio_asset.local_path):
#         raise HTTPException(status_code=404, detail="Background sound file not found on server")
#
#     # Log the found audio asset for debugging
#     logger.info(f"🎵 Found audio asset for scene {scene_id}:")
#     logger.info(f"   Asset ID: {audio_asset.asset_id}")
#     logger.info(f"   Local path: {audio_asset.local_path}")
#     logger.info(f"   Created at: {audio_asset.created_at}")
#     if audio_asset.generation_metadata:
#         logger.info(f"   Scene ID in metadata: {audio_asset.generation_metadata.get('scene_id')}")
#         logger.info(f"   Sound prompt: {audio_asset.generation_metadata.get('sound_prompt')}")
#
#     # Determine filename based on file type
#     if audio_asset.local_path.endswith('.mp4'):
#         filename = f"scene_{scene_id}_with_background_audio.mp4"
#     else:
#         filename = f"scene_{scene_id}_background_audio.wav"
#
#     # Return the file as a downloadable response
#     return FileResponse(
#         path=audio_asset.local_path,
#         filename=filename,
#         media_type="video/mp4" if filename.endswith('.mp4') else "audio/wav",
#         headers={
#             "Content-Disposition": f"attachment; filename={filename}",
#             "Cache-Control": "no-cache"
#         }
#     )


# --- Background Music (per project/script) ---
@router.post("/background-music/generate", response_model=AudioResponse)
async def generate_background_music(
    request: ScriptAudioRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, _ = user_info
    # Fetch script from database
    script_stmt = select(Script).where(
        and_(Script.id == request.script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()
    if not script:
        return AudioResponse(
            audio_url="", status="error", message="Script not found or access denied"
        )
    # Fetch script content from TextAsset
    script_content = ""
    if script.text_asset_id:
        text_asset_stmt = select(TextAsset).where(
            and_(TextAsset.asset_id == script.text_asset_id, TextAsset.org_id == org_id)
        )
        text_asset_result = await session.execute(text_asset_stmt)
        text_asset = text_asset_result.scalar_one_or_none()
        if text_asset:
            script_content = text_asset.content or ""
    generator = BackgroundMusicGenerator()
    result = generator.generate_script_background_music(
        script=script_content, video_style="cinematic"
    )
    if result.get("success"):
        s3_url = result.get("s3_url")
        music_id = result.get("music_id")  # <-- Use this as asset_id
        background_music_prompt = result.get("prompt", "")  # Get the Gemini-generated prompt

        # Update script with background music prompt
        if background_music_prompt:
            try:
                update_stmt = (
                    update(Script)
                    .where(
                        and_(
                            Script.id == request.script_id,
                            Script.org_id == org_id,
                            Script.user_id == user_id,
                        )
                    )
                    .values(background_music_prompt=background_music_prompt)
                )
                await session.execute(update_stmt)
                logger.info(f"✅ Saved background music prompt to script {request.script_id}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to save background music prompt to script: {e}")

        if s3_url and music_id:
            try:
                # Store in database
                audio_asset = AudioAsset(
                    org_id=org_id,
                    asset_id=music_id,  # <-- Use music_id, not a new uuid
                    local_path=None,  # No local path since it's uploaded to S3
                    s3_url=s3_url,  # Set the S3 URL
                    source_type="background_music",
                    script_id=request.script_id,  # <-- Ensure script_id is set
                    generation_status="completed",  # <-- Explicitly set status to completed
                    generation_metadata={
                        "script_id": request.script_id,
                        "video_style": "cinematic",
                        "script_content_preview": script_content[:200] if script_content else "",
                        "generation_result": result,
                    },
                )
                session.add(audio_asset)
                await session.commit()
                logger.info(
                    f"✅ Successfully generated background music for script {request.script_id}"
                )
                logger.info(f"   S3 URL: {s3_url}")
                s3_key = f"Vidflux-Assets/background-music-assets/music_{music_id}.wav"
                logger.info(f"Uploading to S3 key: {s3_key}")
                return AudioResponse(
                    audio_url=s3_url,  # Return the S3 URL
                    status="success",
                    download_url=None,  # No download URL needed since we have S3 URL
                    file_path=None,  # No local file path
                    file_size=None,  # File size not available
                )
            except Exception as e:
                logger.error(f"Failed to store background music asset in database: {e}")
                return AudioResponse(
                    audio_url="", status="error", message=f"Failed to store audio asset: {str(e)}"
                )
        else:
            return AudioResponse(
                audio_url="", status="error", message="Generated audio file not found"
            )
    else:
        return AudioResponse(
            audio_url="",
            status="error",
            message=result.get("error", "Failed to generate background music"),
        )


# --- Background Music Download ---
# @router.get("/background-music/download/{asset_id}")
# async def download_background_music(
#     asset_id: str,
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session)
# ):
#     org_id, user_id, _ = user_info
#
#     # Fetch audio asset from database
#     audio_stmt = select(AudioAsset).where(
#         and_(
#             AudioAsset.asset_id == asset_id,
#             AudioAsset.org_id == org_id,
#             AudioAsset.source_type == "background_music"
#         )
#     )
#     audio_result = await session.execute(audio_stmt)
#     audio_asset = audio_result.scalar_one_or_none()
#
#     if not audio_asset:
#         raise HTTPException(status_code=404, detail="Background music not found or access denied")
#
#     # Check if file exists
#     if not audio_asset.local_path or not os.path.exists(audio_asset.local_path):
#         raise HTTPException(status_code=404, detail="Background music file not found")
#
#     # Get file info
#     file_size = os.path.getsize(audio_asset.local_path)
#     file_name = os.path.basename(audio_asset.local_path)
#
#     logger.info(f"📥 Downloading background music: {file_name}")
#     logger.info(f"   File path: {audio_asset.local_path}")
#     logger.info(f"   File size: {file_size} bytes")
#
#     # Return file as downloadable response
#     return FileResponse(
#         path=audio_asset.local_path,
#         filename=file_name,
#         media_type="audio/mpeg",
#         headers={
#             "Content-Disposition": f"attachment; filename={file_name}",
#             "Content-Length": str(file_size)
#         }
#     )

# --- Scene-wise Voiceover Endpoints ---


@router.post("/voiceover/scenes/generate")
async def generate_voiceover_for_scenes(
    request: VoiceoverGenerationRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Generate voiceover for multiple scenes separately"""
    org_id, user_id, _ = user_info

    # Validate scenes exist and belong to user
    scenes_stmt = select(Scene).where(Scene.id.in_(request.scene_ids))
    scenes_result = await session.execute(scenes_stmt)
    scenes = scenes_result.scalars().all()

    if len(scenes) != len(request.scene_ids):
        raise HTTPException(status_code=404, detail="One or more scenes not found")

    # Check if scenes belong to scripts owned by the user
    script_ids = [scene.script_id for scene in scenes]
    scripts_stmt = select(Script).where(
        and_(Script.id.in_(script_ids), Script.org_id == org_id, Script.user_id == user_id)
    )
    scripts_result = await session.execute(scripts_stmt)
    user_scripts = {script.id for script in scripts_result.scalars().all()}

    # Verify all scenes belong to user's scripts
    for scene in scenes:
        if scene.script_id not in user_scripts:
            raise HTTPException(status_code=403, detail=f"Access denied for scene {scene.id}")

    # Update status to 'generating' for all scenes
    for scene_id in request.scene_ids:
        update_stmt = (
            update(Scene).where(Scene.id == scene_id).values(voiceover_status="generating")
        )
        await session.execute(update_stmt)

    await session.commit()

    # Start background generation for each scene
    background_tasks.add_task(
        generate_voiceovers_for_scenes_background, request.scene_ids, org_id, user_id
    )

    return {
        "status": "started",
        "message": f"Voiceover generation started for {len(request.scene_ids)} scenes",
        "scene_ids": request.scene_ids,
    }


@router.get("/voiceover/status/{script_id}", response_model=VoiceoverStatusResponse)
async def get_voiceover_status(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Get voiceover status for all scenes in a script"""
    org_id, user_id, _ = user_info

    # Verify script ownership
    script_stmt = select(Script).where(
        and_(Script.id == script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        raise HTTPException(status_code=404, detail="Script not found or access denied")

    # Get all scenes for the script
    scenes_stmt = select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
    scenes_result = await session.execute(scenes_stmt)
    scenes = scenes_result.scalars().all()

    # Prepare S3 client for generating presigned URLs
    S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
    S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
    s3_client = S3Client(S3_REGION)

    scene_responses = []
    for scene in scenes:
        s3_presigned_url = None

        # If voiceover is completed, get the audio asset and generate presigned URL
        if scene.voiceover_status == "completed":
            # Find the latest voiceover audio asset for this scene
            # Look for voiceover assets first
            audio_stmt = (
                select(AudioAsset)
                .where(
                    and_(
                        AudioAsset.org_id == org_id,
                        AudioAsset.scene_id == scene.id,
                        AudioAsset.source_type == "voiceover",
                    )
                )
                .order_by(desc(AudioAsset.created_at))
            )
            audio_result = await session.execute(audio_stmt)
            audio_asset = audio_result.scalars().first()

            # If not found with voiceover, try background_sound for backward compatibility
            if not audio_asset:
                audio_stmt_fallback = (
                    select(AudioAsset)
                    .where(
                        and_(
                            AudioAsset.org_id == org_id,
                            AudioAsset.scene_id == scene.id,
                            AudioAsset.source_type == "background_sound",
                        )
                    )
                    .order_by(desc(AudioAsset.created_at))
                )
                audio_result_fallback = await session.execute(audio_stmt_fallback)
                # Check if this is actually a voiceover by looking at metadata
                potential_assets = audio_result_fallback.scalars().all()
                for asset in potential_assets:
                    if (
                        asset.generation_metadata
                        and isinstance(asset.generation_metadata, dict)
                        and asset.generation_metadata.get("audio_type") == "voiceover"
                    ):
                        audio_asset = asset
                        break

            if audio_asset and audio_asset.s3_url:
                # Extract S3 key from URL if it's a full URL
                if (
                    audio_asset.s3_url.startswith("http")
                    and ".amazonaws.com/" in audio_asset.s3_url
                ):
                    s3_key = audio_asset.s3_url.split(".amazonaws.com/")[-1]
                else:
                    s3_key = audio_asset.s3_url

                s3_presigned_url = s3_client.get_presigned_url(
                    S3_BUCKET, s3_key, timedelta(hours=1)
                )

        scene_responses.append(
            SceneVoiceoverResponse(
                scene_id=str(scene.id),
                voiceover_prompt=scene.voiceover_prompt
                or scene.narration,  # Show narration if no custom prompt
                voiceover_status=scene.voiceover_status or "pending",
                s3_presigned_url=s3_presigned_url,
                voiceover_generated_at=(
                    scene.voiceover_generated_at.isoformat()
                    if scene.voiceover_generated_at
                    else None
                ),
            )
        )

    return VoiceoverStatusResponse(script_id=script_id, scenes=scene_responses)


@router.put("/voiceover/scenes/{scene_id}/prompt")
async def update_voiceover_prompt(
    scene_id: str,
    request: VoiceoverPromptUpdateRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Update voiceover prompt for a scene and regenerate voiceover"""
    org_id, user_id, _ = user_info

    # Get scene and verify ownership through script
    scene_stmt = select(Scene).where(Scene.id == scene_id)
    scene_result = await session.execute(scene_stmt)
    scene = scene_result.scalar_one_or_none()

    if not scene:
        raise HTTPException(status_code=404, detail="Scene not found")

    # Verify script ownership
    script_stmt = select(Script).where(
        and_(Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        raise HTTPException(status_code=403, detail="Access denied for this scene")

    # Update the voiceover prompt and set status to generating
    update_stmt = (
        update(Scene)
        .where(Scene.id == scene_id)
        .values(voiceover_prompt=request.prompt, voiceover_status="generating")
    )
    await session.execute(update_stmt)
    await session.commit()

    # Start background generation for this scene
    background_tasks.add_task(
        generate_voiceovers_for_scenes_background, [scene_id], org_id, user_id
    )

    return {
        "status": "updated",
        "message": "Voiceover prompt updated and generation started",
        "scene_id": scene_id,
        "new_prompt": request.prompt,
    }


async def generate_voiceovers_for_scenes_background(
    scene_ids: List[str], org_id: str, user_id: str
):
    """Background task to generate voiceovers for multiple scenes"""

    # Get voice_id from environment
    voice_id = os.getenv("ELEVENLABS_DEFAULT_VOICE_ID")
    if not voice_id:
        logger.error("No ELEVENLABS_DEFAULT_VOICE_ID found in environment")
        return

    def process_single_scene(scene_id: str):
        """Process a single scene voiceover generation"""
        try:
            # Import and create fresh TTS generator instance within the function
            from src.audio_service.services.tts_service import ElevenLabsAudioGenerator

            # Get sync database session
            db_session = next(get_sync_db())

            try:
                # Get scene
                scene = db_session.query(Scene).filter(Scene.id == scene_id).first()
                if not scene:
                    logger.error(f"Scene {scene_id} not found")
                    return

                # Use voiceover_prompt if available, fallback to narration
                text_to_speak = scene.voiceover_prompt or scene.narration

                if not text_to_speak or not text_to_speak.strip():
                    logger.error(f"No text found for scene {scene_id}")
                    # Update status to failed
                    scene.voiceover_status = "failed"
                    db_session.commit()
                    return

                logger.info(f"🎤 Generating voiceover for scene {scene_id}")
                logger.info(f"   Text length: {len(text_to_speak)} characters")
                logger.info(f"   Text preview: {text_to_speak[:100]}...")

                # Create fresh TTS generator instance
                tts_generator = ElevenLabsAudioGenerator()

                # Generate voiceover using TTS service
                result = tts_generator.generate_audio_for_script(
                    script=text_to_speak.strip(), voice=voice_id
                )

                if result.get("success"):
                    s3_url = result.get("s3_url")
                    if s3_url:
                        # Store in database as AudioAsset
                        asset_id = str(uuid.uuid4())

                        # Store raw S3 URL without presigning in database
                        # The S3 URL from TTS service is already the raw URL without query parameters
                        audio_asset = AudioAsset(
                            org_id=org_id,
                            asset_id=asset_id,
                            local_path=None,
                            s3_url=s3_url,  # Store raw S3 URL, presign only when needed for API responses
                            source_type="voiceover",  # Use proper enum value for voiceover
                            scene_id=scene.id,
                            script_id=scene.script_id,
                            generation_metadata={
                                "scene_id": str(scene.id),
                                "voice_id": voice_id,
                                "text": text_to_speak[:200],
                                "generation_result": result,
                                "audio_type": "voiceover",  # Store actual type in metadata
                            },
                        )
                        db_session.add(audio_asset)

                        # Update scene status
                        scene.voiceover_status = "completed"
                        scene.voiceover_generated_at = datetime.utcnow()
                        db_session.commit()

                        logger.info(f"✅ Successfully generated voiceover for scene {scene_id}")
                    else:
                        logger.error(f"No S3 URL in result for scene {scene_id}")
                        scene.voiceover_status = "failed"
                        db_session.commit()
                else:
                    error_msg = result.get("error", "Unknown error")
                    logger.error(
                        f"❌ Failed to generate voiceover for scene {scene_id}: {error_msg}"
                    )
                    scene.voiceover_status = "failed"
                    db_session.commit()

            finally:
                db_session.close()

        except Exception as e:
            logger.error(f"Exception processing scene {scene_id}: {str(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            try:
                db_session = next(get_sync_db())
                scene = db_session.query(Scene).filter(Scene.id == scene_id).first()
                if scene:
                    scene.voiceover_status = "failed"
                    db_session.commit()
                db_session.close()
            except:
                pass

    # Process scenes using ThreadPoolExecutor with max 2 concurrent requests for ElevenLabs
    with ThreadPoolExecutor(max_workers=2) as executor:
        executor.map(process_single_scene, scene_ids)

    logger.info(f"🎉 Completed voiceover generation for {len(scene_ids)} scenes")


# --- Voice Over (Text to Speech, per project/script) ---
@router.post("/voiceover/generate", response_model=AudioResponse)
async def generate_voiceover(
    request: ScriptAudioRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, _ = user_info
    # Fetch script from database
    script_stmt = select(Script).where(
        and_(Script.id == request.script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()
    if not script:
        return AudioResponse(
            audio_url="", status="error", message="Script not found or access denied"
        )
    # Get scenes from database and extract their narrations
    from src.shared.models.database_models import Scene

    scenes_stmt = (
        select(Scene).where(Scene.script_id == request.script_id).order_by(Scene.scene_number)
    )
    scenes_result = await session.execute(scenes_stmt)
    scenes = scenes_result.scalars().all()

    if not scenes:
        return AudioResponse(
            audio_url="", status="error", message="No scenes found for this script"
        )

    # Extract narration from each scene - use only the narration column content
    narrations = []
    for scene in scenes:
        if scene.narration and scene.narration.strip():
            # Use only the narration content, no additional processing
            narrations.append(scene.narration.strip())

    if not narrations:
        return AudioResponse(audio_url="", status="error", message="No narration found in scenes")

    # Combine all narrations into one script - use only narration content
    combined_narration = " ".join(narrations)

    logger.info(f"🎤 Generating voiceover for script {request.script_id}")
    logger.info(f"   Number of scenes with narration: {len(narrations)}")
    logger.info(f"   Combined narration length: {len(combined_narration)} characters")
    logger.info(f"   Narration preview: {combined_narration[:200]}...")

    # Use the enhanced TTS service with the combined narration
    from src.audio_service.services.tts_service import tts_generator

    # Use voice_id from request, fallback to environment variable
    voice_id = os.getenv("ELEVENLABS_DEFAULT_VOICE_ID")

    if not voice_id:
        return AudioResponse(
            audio_url="",
            status="error",
            message="No voice_id provided. Please specify a valid ElevenLabs voice ID.",
        )

    result = tts_generator.generate_audio_for_script(script=combined_narration, voice=voice_id)

    if result.get("success"):
        s3_url = result.get("s3_url")
        if s3_url:
            # Defensive: If s3_url is not a presigned URL, generate one from the S3 key
            from urllib.parse import urlparse
            from src.shared.utils.s3_client import S3Client

            S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
            S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
            s3_client = S3Client(S3_REGION)
            # Check if s3_url is a presigned URL (contains X-Amz-)
            if "X-Amz-" not in s3_url:
                # Extract S3 key from the URL if needed
                if s3_url.startswith("http") and ".amazonaws.com/" in s3_url:
                    s3_key = s3_url.split(".amazonaws.com/")[-1]
                else:
                    s3_key = s3_url
                s3_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
            try:
                asset_id = str(uuid.uuid4())  # Generate ONCE per asset
                # Store in database with voiceover source_type
                audio_asset = AudioAsset(
                    org_id=org_id,
                    asset_id=asset_id,
                    local_path=None,  # No local path since it's uploaded to S3
                    s3_url=s3_url,  # Set the S3 URL
                    source_type="background_sound",
                    script_id=request.script_id,
                    generation_metadata={
                        "script_id": request.script_id,
                        "voice_id": voice_id,
                        "narration_text": combined_narration[:200],
                        "scene_count": len(scenes),
                        "generation_result": result,
                    },
                )
                session.add(audio_asset)
                await session.commit()
                logger.info(f"✅ Successfully generated voiceover for script {request.script_id}")
                logger.info(f"   S3 URL: {s3_url}")
                s3_key = f"Vidflux-Assets/text-to-speech-assets/script_{request.script_id}/tts_{asset_id}.mp3"
                logger.info(f"Uploading to S3 key: {s3_key}")
                return AudioResponse(
                    audio_url=s3_url,  # Return the S3 URL
                    status="success",
                    download_url=None,  # No download URL needed since we have S3 URL
                    file_path=None,  # No local file path
                    file_size=None,  # File size not available
                )
            except Exception as e:
                logger.error(f"Failed to store voiceover asset in database: {e}")
                return AudioResponse(
                    audio_url="", status="error", message=f"Failed to store audio asset: {str(e)}"
                )
        else:
            return AudioResponse(
                audio_url="", status="error", message="Generated audio file not found"
            )
    else:
        return AudioResponse(
            audio_url="",
            status="error",
            message=result.get("error", "Failed to generate voiceover"),
        )


# --- Voiceover Download ---
# @router.get("/voiceover/download/{script_id}")
# async def download_voiceover(
#     script_id: str,
#     user_info: tuple = Depends(require_auth),
#     session: AsyncSession = Depends(get_database_session)
# ):
#     org_id, user_id, _ = user_info
#
#     # Fetch the most recent voiceover audio asset for this script
#     audio_stmt = select(AudioAsset).where(
#         and_(
#             AudioAsset.org_id == org_id,
#             AudioAsset.script_id == script_id,
#             AudioAsset.source_type == "background_sound"  # Voiceover uses background_sound type
#         )
#     ).order_by(desc(AudioAsset.created_at)).limit(1)
#
#     audio_result = await session.execute(audio_stmt)
#     audio_asset = audio_result.scalar_one_or_none()
#
#     if not audio_asset:
#         raise HTTPException(status_code=404, detail="Voiceover not found for this script or access denied")
#
#     # Check if file exists
#     if not audio_asset.local_path or not os.path.exists(audio_asset.local_path):
#         raise HTTPException(status_code=404, detail="Voiceover file not found")
#
#     # Get file info
#     file_size = os.path.getsize(audio_asset.local_path)
#     file_name = os.path.basename(audio_asset.local_path)
#
#     logger.info(f"📥 Downloading voiceover for script {script_id}: {file_name}")
#     logger.info(f"   File path: {audio_asset.local_path}")
#     logger.info(f"   File size: {file_size} bytes")
#
#     # Return file as downloadable response
#     return FileResponse(
#         path=audio_asset.local_path,
#         filename=file_name,
#         media_type="audio/mpeg",
#         headers={
#             "Content-Disposition": f"attachment; filename={file_name}",
#             "Content-Length": str(file_size)
#         }
#     )

# At the top, set S3 bucket and region from env, with fallback and error if missing
S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)

# Remove the /audio/debug/list-background-audio endpoint


@router.get("/{script_id}", include_in_schema=False)
async def get_script_audio(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    db: AsyncSession = Depends(get_database_session),
):
    org_id, user_id, email = user_info
    scenes_stmt = select(Scene).where(and_(Scene.script_id == script_id, Scene.status == "active"))
    scenes_result = await db.execute(scenes_stmt)
    scenes = scenes_result.scalars().all()
    if not scenes:
        return []
    result = []
    for scene in scenes:
        audios_stmt = select(AudioAsset).where(
            and_(AudioAsset.scene_id == str(scene.id), AudioAsset.org_id == org_id)
        )
        audios_result = await db.execute(audios_stmt)
        audios = audios_result.scalars().all()
        audio_objs = []
        for audio in audios:
            s3_url = audio.s3_url
            url = None
            key = None
            # Use the correct S3 key pattern for each asset type
            if getattr(audio, "source_type", None) == "background_music":
                key = f"Vidflux-Assets/background-music-assets/music_{audio.asset_id}.wav"
            elif getattr(audio, "source_type", None) == "background_sound":
                key = f"Vidflux-Assets/background-audio-assets/scene_{audio.scene_id}/audio_{audio.asset_id}.mp3"
            elif getattr(audio, "source_type", None) == "voiceover":
                key = f"Vidflux-Assets/text-to-speech-assets/script_{audio.script_id}/tts_{audio.asset_id}.mp3"
            # Add more elifs for other audio types as needed
            if key:
                logger.info(f"Generating presigned URL for S3 key: {key}")
                url = s3_client.get_presigned_url(S3_BUCKET, key, timedelta(hours=1))
            else:
                url = s3_url
            audio_objs.append({"id": audio.asset_id, "url": url, "created_at": audio.created_at})
        result.append({"scene_id": str(scene.id), "audios": audio_objs})
    return result


# --- Background Sound Status (by script_id, aggregated like video status) ---
@router.get("/background-sound/status/{script_id}")
async def get_background_sound_status_by_script(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Get background sound status for all scenes in a script, video status style."""
    org_id, user_id, _ = user_info
    from src.shared.models.database_models import Scene, AudioAsset
    from sqlalchemy import desc, and_, select
    from datetime import timedelta

    S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
    S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
    s3_client = S3Client(S3_REGION)
    # Get all scenes for this script
    result = await session.execute(
        select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
    )
    scenes = result.scalars().all()
    logger.info(
        f"[AUDIO STATUS DEBUG] Found {len(scenes)} scenes for script {script_id}: {[scene.id for scene in scenes]}"
    )
    scene_statuses = []
    for scene in scenes:
        stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.org_id == org_id,
                    AudioAsset.scene_id == scene.id,
                    AudioAsset.source_type == "background_sound",
                )
            )
            .order_by(desc(AudioAsset.created_at))
            .limit(1)
        )
        audio_result = await session.execute(stmt)
        audio_asset = audio_result.scalar_one_or_none()
        audio_url = ""
        status = "pending"
        message = "Background sound not yet generated."
        if audio_asset:
            s3_url = audio_asset.s3_url
            if s3_url and "X-Amz-" not in s3_url:
                if s3_url.startswith("http") and ".amazonaws.com/" in s3_url:
                    s3_key = s3_url.split(".amazonaws.com/")[-1]
                else:
                    s3_key = s3_url
                audio_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
            else:
                audio_url = s3_url
            status = audio_asset.generation_status or "completed"
            message = None
        scene_statuses.append(
            {
                "id": str(scene.id),
                "scene_number": scene.scene_number,
                "background_sound_status": status,
                "audio_url": audio_url,
                "message": message,
                "success": bool(audio_url),
            }
        )
    return JSONResponse({"status": "success", "script_id": script_id, "scenes": scene_statuses})


# --- Background Music Status (by script_id) ---
@router.get("/background-music/status/{script_id}")
async def get_background_music_status_by_script(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Get background music status for a script."""
    org_id, user_id, _ = user_info
    from src.shared.models.database_models import AudioAsset
    from sqlalchemy import desc, and_, select
    from datetime import timedelta

    S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
    S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
    s3_client = S3Client(S3_REGION)

    # Get the latest background music asset for this script
    stmt = (
        select(AudioAsset)
        .where(
            and_(
                AudioAsset.org_id == org_id,
                AudioAsset.script_id == script_id,
                AudioAsset.source_type == "background_music",
            )
        )
        .order_by(desc(AudioAsset.created_at))
        .limit(1)
    )

    audio_result = await session.execute(stmt)
    audio_asset = audio_result.scalar_one_or_none()

    audio_url = ""
    status = "pending"
    message = "Background music not yet generated."
    prompt = None

    if audio_asset:
        s3_url = audio_asset.s3_url
        if s3_url and "X-Amz-" not in s3_url:
            # Generate presigned URL for S3
            if s3_url.startswith("http") and ".amazonaws.com/" in s3_url:
                s3_key = s3_url.split(".amazonaws.com/")[-1]
            else:
                s3_key = s3_url
            audio_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
        else:
            audio_url = s3_url

        status = audio_asset.generation_status or "completed"
        message = None

    # Get the current prompt from the script table
    script_stmt = select(Script).where(Script.id == script_id)
    script_result = await session.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if script:
        prompt = script.background_music_prompt

    return JSONResponse(
        {
            "status": "success",
            "script_id": script_id,
            "background_music_status": status,
            "audio_url": audio_url,
            "message": message,
            "current_prompt": prompt,
            "success": bool(audio_url),
        }
    )


# --- Background Music Prompt Update and Regenerate ---
@router.put("/background-music/update-prompt/{script_id}")
async def update_background_music_prompt_and_regenerate(
    script_id: str,
    request: BackgroundMusicPromptUpdateRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Update the background music prompt and immediately regenerate the music."""
    org_id, user_id, _ = user_info

    # Extract the new prompt from request body
    new_prompt = request.prompt.strip()
    if not new_prompt:
        return JSONResponse(
            status_code=400, content={"status": "error", "message": "Prompt is required"}
        )

    try:
        # Update the script's background_music_prompt
        stmt = (
            update(Script).where(Script.id == script_id).values(background_music_prompt=new_prompt)
        )
        await session.execute(stmt)
        await session.commit()

        logger.info(f"Updated background music prompt for script {script_id}: {new_prompt}")

        # Now regenerate the background music using the new prompt
        generator = BackgroundMusicGenerator()

        # Generate music with the custom prompt (remove await - method is not async)
        result = generator.generate_background_music_with_custom_prompt(
            script_id=script_id, custom_prompt=new_prompt
        )

        if result.get("success"):
            music_id = str(uuid.uuid4())
            s3_key = f"Vidflux-Assets/background-music-assets/music_{music_id}.wav"

            # Upload to S3
            S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
            S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
            s3_client = S3Client(S3_REGION)

            audio_file_path = result.get("audio_file_path")
            if audio_file_path and os.path.exists(audio_file_path):
                upload_result = s3_client.upload_file(
                    bucket=S3_BUCKET, key=s3_key, file_path=audio_file_path
                )

                if upload_result:
                    s3_url = f"https://{S3_BUCKET}.s3.{S3_REGION}.amazonaws.com/{s3_key}"

                    # Store in database
                    audio_asset = AudioAsset(
                        asset_id=music_id,
                        script_id=script_id,
                        scene_id=None,  # Background music is for the whole script
                        org_id=org_id,
                        s3_url=s3_url,
                        source_type="background_music",
                        generation_status="completed",
                        metadata={
                            "prompt": new_prompt,
                            "generated_at": datetime.utcnow().isoformat(),
                        },
                    )
                    session.add(audio_asset)
                    await session.commit()

                    logger.info(
                        f"✅ Successfully regenerated background music for script {script_id} with new prompt"
                    )

                    return JSONResponse(
                        {
                            "status": "success",
                            "message": "Prompt updated and music regenerated successfully",
                            "script_id": script_id,
                            "audio_url": s3_url,
                            "new_prompt": new_prompt,
                        }
                    )
                else:
                    return JSONResponse(
                        status_code=500,
                        content={
                            "status": "error",
                            "message": "Failed to upload regenerated music to S3",
                        },
                    )
            else:
                return JSONResponse(
                    status_code=500,
                    content={"status": "error", "message": "Generated audio file not found"},
                )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": result.get("error", "Failed to regenerate background music"),
                },
            )

    except Exception as e:
        logger.error(f"Error updating background music prompt and regenerating: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Failed to update prompt and regenerate music: {str(e)}",
            },
        )


@router.post("/background-music/upload", response_model=UserBackgroundMusicResponse)
async def upload_user_background_music(
    request: UserBackgroundMusicRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
) -> UserBackgroundMusicResponse:
    """
    Save user-uploaded background music for a script.
    Takes a script ID and presigned URL from frontend.
    """
    try:
        org_id, user_id, email = user_info

        # Validate script exists and user has access
        script_query = select(Script).where(
            and_(Script.id == request.script_id, Script.org_id == org_id)
        )
        script_result = await session.execute(script_query)
        script = script_result.scalar_one_or_none()

        if not script:
            raise HTTPException(status_code=404, detail="Script not found or access denied")

        # Check if background music already exists for this script
        existing_music_query = select(AudioAsset).where(
            and_(
                AudioAsset.script_id == request.script_id,
                AudioAsset.source_type == "user_uploaded",
                AudioAsset.deleted_at.is_(None),
            )
        )
        existing_music_result = await session.execute(existing_music_query)
        existing_music = existing_music_result.scalar_one_or_none()

        # Generate unique asset ID
        asset_id = f"audio_{uuid.uuid4().hex}"

        # If existing music exists, mark it as deleted (soft delete)
        if existing_music:
            existing_music.deleted_at = datetime.utcnow()
            logger.info(f"Soft deleted existing background music: {existing_music.asset_id}")

        # Create new AudioAsset record
        audio_asset = AudioAsset(
            org_id=org_id,
            asset_id=asset_id,
            s3_url=request.presigned_url,
            source_type="user_uploaded",
            script_id=request.script_id,
            generation_metadata={
                "user_uploaded": True,
                "filename": request.filename,
                "description": request.description,
                "uploaded_by": user_id,
                "upload_timestamp": datetime.utcnow().isoformat(),
            },
            generation_status="completed",
        )

        session.add(audio_asset)
        await session.commit()

        logger.info(
            f"Successfully saved user background music for script {request.script_id}: {asset_id}"
        )

        return UserBackgroundMusicResponse(
            asset_id=asset_id,
            script_id=request.script_id,
            s3_url=request.presigned_url,
            status="success",
            message="Background music uploaded and saved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving user background music: {e}")
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to save background music: {str(e)}")
