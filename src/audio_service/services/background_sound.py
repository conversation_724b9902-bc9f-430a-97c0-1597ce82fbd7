# Global imports
import os
import time
import shutil
import logging
import tempfile
import requests
import subprocess
from pathlib import Path
import google.generativeai as genai
from gradio_client import Client, handle_file
from typing import Dict, List, Any, Optional, Tuple

# Local imports
from src.shared.utils.s3_client import S3Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedBackgroundSoundGenerator:
    """
    Enhanced Background sound generator using MMAudio API with Gemini integration
    Optimized for generating short, contextual base prompts and syncing with videos
    """

    def __init__(self):
        self.client = None
        self.api_endpoint = "hkchengrex/MMAudio"
        self.max_retries = 5  # Increased retries
        self.retry_delay = 3  # Increased initial delay
        self.max_retry_delay = 30  # Maximum retry delay

        # Get Hugging Face token for enhanced authentication and better quota
        self.hf_token = os.getenv("HF_TOKEN") or os.getenv("HUGGINGFACE_TOKEN")
        if not self.hf_token:
            logger.warning(
                "⚠️ No Hugging Face token found. Set HF_TOKEN environment variable for better quota and authentication."
            )

        self.default_settings = {
            "negative_prompt": "music, speech, talking, dialogue, conversation, singing, vocals",
            "seed": -1,
            "num_steps": 25,
            "cfg_strength": 4.5,
            "duration": 5,
        }
        self.logger = logging.getLogger(__name__)

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = (
            1  # Minimum 1 second between requests (reduced from 2 since we're sequential)
        )

        # Initialize LLM service for prompt generation
        try:
            from src.shared.services.llm_service import llm_service

            self.llm_service = llm_service
            if llm_service.is_available():
                logger.info("✅ LLM service initialized for sound analysis")
            else:
                logger.warning("⚠️ No LLM providers available for sound analysis")
                self.llm_service = None
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize LLM service: {e}")
            self.llm_service = None

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 2  # seconds between requests

        # Output directory
        self.output_dir = os.getenv("BACKGROUND_SOUND_OUTPUT_DIR", "output/background_sounds")
        os.makedirs(self.output_dir, exist_ok=True)

        self.s3_bucket = os.getenv("S3_VIDEO_ASSET_BUCKET")
        self.s3_region = os.getenv("AWS_REGION", "us-east-2")
        self.s3_client = S3Client(self.s3_region)

    def _initialize_client(self) -> bool:
        """Initialize the Gradio client for MMAudio with authentication"""
        try:
            if self.client is None:
                logger.info("Initializing MMAudio client...")

                # Try with authentication token if available
                if self.hf_token:
                    logger.info("🔑 Using Hugging Face authentication token")
                    try:
                        from huggingface_hub import login

                        login(token=self.hf_token)
                        logger.info("✅ Logged in to Hugging Face")
                    except Exception as login_error:
                        logger.warning(f"⚠️ Login failed: {login_error}")

                # Initialize client with authentication if available
                self.client = Client(
                    self.api_endpoint, hf_token=self.hf_token if self.hf_token else None
                )
                logger.info("✅ MMAudio client initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize MMAudio client: {str(e)}")
            return False

    def check_quota_status(self) -> Dict[str, Any]:
        """
        Try to check current quota status by making a minimal test request
        """
        try:
            if not self._initialize_client():
                return {"error": "Failed to initialize client"}

            # Make a very minimal request to check quota
            logger.info("🔍 Checking MMAudio quota status...")
            test_result = self.client.predict(api_name="/lambda")

            return {"status": "available", "message": "✅ Quota available - test successful"}

        except Exception as e:
            error_msg = str(e)
            if "exceeded your GPU quota" in error_msg:
                # Extract quota information from error message
                quota_info = {}
                try:
                    # Parse the error message for quota details
                    if "vs." in error_msg and "requested" in error_msg:
                        parts = error_msg.split("(")[1].split(")")[0]
                        if "left vs." in parts:
                            left_part, requested_part = parts.split(" left vs. ")
                            quota_info["remaining"] = left_part.strip()
                            quota_info["requested"] = requested_part.split(" requested")[0].strip()
                except:
                    pass

                return {
                    "status": "quota_exceeded",
                    "message": f"❌ GPU quota exceeded: {error_msg}",
                    "quota_info": quota_info,
                    "raw_error": error_msg,
                }
            else:
                return {"status": "error", "message": f"❌ Unknown error: {error_msg}"}

    def test_api_connection(self) -> Tuple[bool, str]:
        """Test connection to MMAudio API with enhanced authentication"""
        try:
            if not self._initialize_client():
                return False, "❌ Failed to initialize client"

            # Test the lambda endpoint (lightweight test)
            result = self.client.predict(api_name="/lambda")
            return True, "✅ MMAudio API connection successful"

        except Exception as e:
            error_msg = f"❌ MMAudio API connection failed: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def _ensure_client(self):
        """Ensure client is initialized with authentication"""
        if not self.client:
            self._initialize_client()

    def _rate_limit(self):
        """Simple rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()

    def generate_base_sound_prompt(self, image_prompt: str, context: str = "") -> str:
        """
        Generate a very short base sound prompt from image prompt using Gemini
        Focus on creating short, contextual sound descriptions like "waves", "sandwich eating sound"

        Args:
            image_prompt: The image generation prompt to analyze
            context: Optional additional context

        Returns:
            Very short base sound prompt (1-4 words)
        """
        try:
            if not self.llm_service:
                return self._extract_fallback_sound(image_prompt)

            gemini_prompt = f"""
            Based on this image prompt, create a very short sound description (1-4 words maximum):
            
            Image Prompt: {image_prompt}
            {f"Additional Context: {context}" if context else ""}

            Create only the most essential sound that would naturally accompany this scene.
            
            Examples of good short sound prompts:
            - For food scenes: "sandwich eating sound", "sizzling", "chopping"
            - For nature scenes: "waves", "birds", "wind", "rain"
            - For city scenes: "traffic", "footsteps", "crowd"
            - For indoor scenes: "typing", "pages turning", "clock ticking"
            - For action scenes: "footsteps running", "door closing"
            
            Important rules:
            - Maximum 4 words
            - Focus on ambient/background sounds, not music
            - Should complement the visual scene
            - Avoid speech, dialogue, or singing
            - AVOID Any form of vocal talking
            - Mention eating sounds
            
            Output only the short sound description, nothing else.
            """

            result = self.llm_service.generate_content(gemini_prompt)
            if result["success"]:
                base_prompt = result["content"].strip().lower()
            else:
                logger.warning(f"LLM service failed: {result.get('error')}")
                return self._extract_fallback_sound(image_prompt)

            # Clean up the response to ensure it's short
            base_prompt = self._clean_and_shorten_prompt(base_prompt)

            self.logger.info(f"Generated base sound prompt: '{base_prompt}'")
            return base_prompt

        except Exception as e:
            error_msg = f"Error generating base sound prompt: {str(e)}"
            self.logger.error(error_msg)
            return self._extract_fallback_sound(image_prompt)

    def _clean_and_shorten_prompt(self, prompt: str) -> str:
        """Clean and ensure prompt is short enough"""
        # Remove common unnecessary words
        prompt = prompt.replace("the sound of", "").replace("sound of", "")
        prompt = prompt.replace("ambient", "").replace("background", "")
        prompt = prompt.strip()

        # Split and take only first 4 words
        words = prompt.split()
        if len(words) > 4:
            words = words[:4]

        return " ".join(words).strip()

    def _extract_fallback_sound(self, image_prompt: str) -> str:
        """Fallback method for sound extraction"""
        image_lower = image_prompt.lower()

        # Food-related sounds
        if any(
            word in image_lower for word in ["sandwich", "eating", "food", "meal", "bite", "chew"]
        ):
            return "eating sound"
        elif any(word in image_lower for word in ["cooking", "kitchen", "sizzle", "fry"]):
            return "sizzling"
        elif any(word in image_lower for word in ["drink", "pour", "liquid"]):
            return "pouring"

        # Nature sounds
        elif any(word in image_lower for word in ["ocean", "sea", "beach", "waves"]):
            return "waves"
        elif any(word in image_lower for word in ["forest", "woods", "trees", "nature"]):
            return "birds"
        elif any(word in image_lower for word in ["rain", "storm", "weather"]):
            return "rain"
        elif any(word in image_lower for word in ["fire", "campfire", "flame"]):
            return "fire crackling"
        elif any(word in image_lower for word in ["wind", "breeze"]):
            return "wind"

        # Urban sounds
        elif any(word in image_lower for word in ["city", "urban", "street", "traffic"]):
            return "city ambience"
        elif any(word in image_lower for word in ["walk", "walking", "step"]):
            return "footsteps"
        elif any(word in image_lower for word in ["car", "vehicle", "driving"]):
            return "engine"

        # Indoor sounds
        elif any(word in image_lower for word in ["office", "work", "desk", "computer"]):
            return "typing"
        elif any(word in image_lower for word in ["book", "reading", "page"]):
            return "pages turning"
        elif any(word in image_lower for word in ["clock", "time"]):
            return "clock ticking"

        else:
            return "ambient sound"

    def get_video_duration(self, video_path: str) -> float:
        """Get video duration using ffprobe"""
        try:
            cmd = [
                "ffprobe",
                "-v",
                "quiet",
                "-show_entries",
                "format=duration",
                "-of",
                "default=noprint_wrappers=1:nokey=1",
                video_path,
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
            else:
                self.logger.warning(f"Could not get video duration, using default 8 seconds")
                return 8.0
        except Exception as e:
            self.logger.warning(f"Error getting video duration: {e}, using default 8 seconds")
            return 8.0

    def store_bgsound_prompt_in_scene(self, scene_id: str, bgsound_prompt: str):
        """
        Store the generated background sound prompt in the scene's bgsound_prompt column
        """
        try:
            from src.shared.config.database import get_sync_db
            from src.shared.models.database_models import Scene

            db = next(get_sync_db())
            try:
                scene = db.query(Scene).filter(Scene.id == scene_id).first()
                if scene:
                    scene.bgsound_prompt = bgsound_prompt
                    db.commit()
                    self.logger.info(
                        f"✅ Stored background sound prompt in scene {scene_id}: {bgsound_prompt}"
                    )
                else:
                    self.logger.warning(f"⚠️ Scene {scene_id} not found, could not store prompt")
            finally:
                db.close()
        except Exception as e:
            self.logger.error(f"❌ Error storing background sound prompt in scene {scene_id}: {e}")

    def upload_background_sound_to_s3(self, file_path: str, scene_id: str, asset_id: str) -> str:
        """
        Uploads the generated background sound file to the standard S3 path for background audio assets.
        Returns the S3 key.
        """
        s3_bucket = "assets-vidflux"
        s3_key = f"Vidflux-Assets/background-audio-assets/scene_{scene_id}/audio_{asset_id}.mp3"
        self.s3_client.upload_file(bucket=s3_bucket, key=s3_key, file_path=file_path)
        return s3_key

    def generate_background_sound_for_video_sync(
        self,
        video_path: str,
        base_sound_prompt: str,
        scene_id: str,
        asset_id: str,
        negative_prompt: Optional[str] = None,
        seed: int = -1,
        num_steps: int = 25,
        cfg_strength: float = 4.5,
        auto_duration: bool = True,
    ) -> Dict[str, Any]:
        for attempt in range(self.max_retries):
            try:
                self._ensure_client()
                self._rate_limit()
                if negative_prompt is None:
                    negative_prompt = self.default_settings["negative_prompt"]
                duration = self.get_video_duration(video_path) if auto_duration else 8.0
                duration = min(duration, 8.0)
                self.logger.info(
                    f"Generating synchronized background sound for video: {video_path} (attempt {attempt + 1})"
                )
                self.logger.info(f"Sound prompt: '{base_sound_prompt}', Duration: {duration}s")
                result = self.client.predict(
                    video={"video": handle_file(video_path), "subtitles": None},
                    prompt=base_sound_prompt,
                    negative_prompt=negative_prompt,
                    seed=seed,
                    num_steps=num_steps,
                    cfg_strength=cfg_strength,
                    duration=duration,
                    api_name="/predict",
                )
                if result:
                    logger.info("✅ Background sound generation successful")
                    logger.info(f"[DEBUG] Full MMAudio result: {result}")
                    video_with_audio_path = None
                    if isinstance(result, dict):
                        video_with_audio_path = (
                            result.get("video_with_audio")
                            or result.get("output_path")
                            or result.get("video")
                        )
                    logger.info(f"[DEBUG] video_with_audio_path: {video_with_audio_path}")
                    if video_with_audio_path is None:
                        self.logger.error(
                            f"[DEBUG] MMAudio output path is None, skipping S3 upload."
                        )
                    elif not os.path.exists(video_with_audio_path):
                        self.logger.error(
                            f"[DEBUG] MMAudio output file does NOT exist at path: {video_with_audio_path}, skipping S3 upload."
                        )
                    elif video_with_audio_path and os.path.exists(video_with_audio_path):
                        s3_key = self.upload_background_sound_to_s3(
                            video_with_audio_path, scene_id, asset_id
                        )
                        file_size = os.path.getsize(video_with_audio_path)
                        self.logger.info(
                            f"[DEBUG] MMAudio output file exists: {video_with_audio_path}, size={file_size} bytes"
                        )
                        from datetime import timedelta

                        s3_url = self.s3_client.get_presigned_url(
                            "assets-vidflux", s3_key, timedelta(hours=1)
                        )
                        self.logger.info(f"✅ Uploaded MMAudio output to S3: {s3_url}")
                        try:
                            os.remove(video_with_audio_path)
                        except Exception:
                            pass
                        return {
                            "success": True,
                            "s3_url": s3_url,
                            "s3_key": s3_key,
                            "source_video": video_path,
                            "sound_prompt": base_sound_prompt,
                            "duration": duration,
                            "synchronized": True,
                            "settings": {
                                "negative_prompt": negative_prompt,
                                "seed": seed,
                                "num_steps": num_steps,
                                "cfg_strength": cfg_strength,
                            },
                        }
                    return {
                        "success": True,
                        "video_with_audio": result,
                        "source_video": video_path,
                        "sound_prompt": base_sound_prompt,
                        "duration": duration,
                        "synchronized": True,
                        "settings": {
                            "negative_prompt": negative_prompt,
                            "seed": seed,
                            "num_steps": num_steps,
                            "cfg_strength": cfg_strength,
                        },
                    }
                else:
                    logger.warning(f"❌ Generation attempt {attempt + 1} returned empty result")
            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"Generation attempt {attempt + 1} failed: {error_msg}")

                # Check for specific error types
                if "has not enabled verbose error reporting" in error_msg:
                    self.logger.error(
                        "🚨 Upstream Gradio app raised an exception without verbose errors."
                    )
                    if attempt < self.max_retries - 1:
                        # For Gradio errors, try with longer delay
                        delay = min(self.retry_delay * (2**attempt), self.max_retry_delay)
                        self.logger.info(
                            f"Retrying Gradio error in {delay} seconds... (attempt {attempt + 1}/{self.max_retries})"
                        )
                        time.sleep(delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "error": f"Upstream Gradio service error: {error_msg}",
                            "error_type": "gradio_error",
                            "source_video": video_path,
                            "sound_prompt": base_sound_prompt,
                            "retry_recommended": True,
                        }

                if "exceeded your GPU quota" in error_msg:
                    self.logger.warning("🚫 GPU quota exceeded")
                    return {
                        "success": False,
                        "error": f"GPU quota exceeded: {error_msg}",
                        "error_type": "quota_exceeded",
                        "source_video": video_path,
                        "sound_prompt": base_sound_prompt,
                        "quota_exceeded": True,
                        "retry_recommended": True,
                    }

                if "Connection" in error_msg or "timeout" in error_msg.lower():
                    self.logger.warning("🌐 Network/connection error detected")
                    if attempt < self.max_retries - 1:
                        delay = min(self.retry_delay * (2**attempt), self.max_retry_delay)
                        self.logger.info(
                            f"Retrying connection error in {delay} seconds... (attempt {attempt + 1}/{self.max_retries})"
                        )
                        time.sleep(delay)
                        continue

                if attempt < self.max_retries - 1:
                    # Exponential backoff for other errors
                    delay = min(self.retry_delay * (2**attempt), self.max_retry_delay)
                    self.logger.info(
                        f"Retrying in {delay} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    return {
                        "success": False,
                        "error": error_msg,
                        "error_type": "generation_failed",
                        "source_video": video_path,
                        "sound_prompt": base_sound_prompt,
                        "retry_recommended": True,
                    }
        return {
            "success": False,
            "error": "All generation attempts failed",
            "error_type": "max_retries_exceeded",
            "source_video": video_path,
            "sound_prompt": base_sound_prompt,
            "retry_recommended": False,  # Don't recommend retrying if we've exhausted all attempts
        }

    def smart_generate_with_quota_check(
        self, video_path: str, base_sound_prompt: str, **kwargs
    ) -> Dict[str, Any]:
        """Generate background sound with automatic quota checking"""

        # Check quota first
        quota_status = self.check_quota_status()

        if quota_status.get("status") == "quota_exceeded":
            logger.warning("🚫 Quota exceeded, cannot generate background sound right now")
            return {
                "success": False,
                "error": "GPU quota exceeded",
                "quota_status": quota_status,
                "source_video": video_path,
                "sound_prompt": base_sound_prompt,
            }

        # Generate with provided settings
        return self.generate_background_sound_for_video_sync(
            video_path=video_path, base_sound_prompt=base_sound_prompt, **kwargs
        )

    def get_quota_friendly_settings(self) -> Dict[str, Any]:
        """Get recommended settings for quota-conscious generation"""
        return {
            "num_steps": 20,  # Reduced steps for faster generation
            "cfg_strength": 4.0,  # Lower CFG for efficiency
            "duration": 6,  # Shorter duration
            "description": "Optimized for quota-conscious background sound generation",
        }

    def format_results_for_display(self, results: List[Dict[str, Any]]) -> str:
        """Format results for display"""
        if not results:
            return "No audio generation results to display."

        formatted_parts = ["🎵🎬 Enhanced Background Sound Generation Results:\n" + "=" * 70 + "\n"]

        successful = sum(1 for r in results if r.get("success", False))
        failed = len(results) - successful
        quota_exceeded = sum(1 for r in results if r.get("quota_exceeded", False))

        # Summary
        formatted_parts.extend(
            [
                f"✅ Successful: {successful}",
                f"❌ Failed: {failed}",
                f"🚫 Quota Exceeded: {quota_exceeded}",
                f"📊 Total Scenes: {len(results)}",
                "",
            ]
        )

        # Show base sound prompt (all scenes share the same base)
        base_prompt = next(
            (r.get("base_sound_prompt", "") for r in results if r.get("base_sound_prompt")), ""
        )
        if base_prompt:
            formatted_parts.append(f"🎼 Base Sound Theme: '{base_prompt}'")
            formatted_parts.append("")

        # Individual scene statuses
        for result in results:
            scene_num = result.get("scene_number", "N/A")

            if result.get("success", False):
                formatted_parts.extend(
                    [
                        f"🎵✅ Scene {scene_num} - SUCCESS",
                        f"   🎼 Sound: '{result.get('base_sound_prompt', 'N/A')}'",
                        f"   🎬 Video with Audio: {result.get('video_with_audio_path', 'N/A')}",
                        f"   🔗 Audio URL: {result.get('video_with_audio_url', 'N/A')}",
                        f"   🎯 Synchronized: {result.get('synchronized', False)}",
                        f"   ⚙️ Method: {result.get('method', 'N/A')}",
                        "",
                    ]
                )
            else:
                error = result.get("error", "Unknown error")
                formatted_parts.extend(
                    [
                        f"❌ Scene {scene_num} - FAILED",
                        f"   🎼 Sound Theme: '{result.get('base_sound_prompt', 'N/A')}'",
                        f"   ❌ Error: {error}",
                    ]
                )

                if result.get("quota_exceeded"):
                    formatted_parts.append("   🚫 Quota Exceeded - try again later")

                formatted_parts.append("")

                if result.get("note"):
                    formatted_parts.append(f"   💡 Note: {result['note']}")
                    formatted_parts.append("")

        return "\n".join(formatted_parts)

    def extract_audio_from_video(self, video_path: str, output_path: str = None) -> str:
        """
        Extract audio track from video file using ffmpeg, upload to S3, and return the S3 URL.
        """
        try:
            import subprocess
            import os

            if not output_path:
                # Generate output path based on video path
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_path = os.path.join(self.output_dir, f"{base_name}_audio.wav")
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            # Use ffmpeg to extract audio
            cmd = [
                "ffmpeg",
                "-i",
                video_path,
                "-vn",  # No video
                "-acodec",
                "pcm_s16le",  # Audio codec
                "-ar",
                "44100",  # Sample rate
                "-ac",
                "2",  # Stereo
                "-y",  # Overwrite output
                output_path,
            ]
            self.logger.info(f"Extracting audio from {video_path} to {output_path}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0 and os.path.exists(output_path):
                self.logger.info(f"✅ Audio extracted successfully: {output_path}")
                # Upload to S3
                s3_key = f"background_sounds/{os.path.basename(output_path)}"
                try:
                    s3_url = self.s3_client.upload_file(
                        bucket=self.s3_bucket, key=s3_key, file_path=output_path
                    )
                    self.logger.info(f"✅ Uploaded background sound to S3: {s3_url}")
                except Exception as e:
                    self.logger.error(f"S3 upload failed: {e}", exc_info=True)
                    return None
                # Clean up local file
                if os.path.exists(output_path):
                    os.remove(output_path)
                return s3_url
            else:
                self.logger.error(f"❌ Failed to extract audio: {result.stderr}")
                return None
        except Exception as e:
            self.logger.error(f"❌ Error extracting audio: {str(e)}")
            return None
