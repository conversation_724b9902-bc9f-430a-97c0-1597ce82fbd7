"""
Enhanced TTS (Text-to-Speech) service using ElevenLabs API
Handles voiceover generation with professional regex parsing and scene-based extraction
Now includes enhanced narration extraction with improved regex patterns
"""

# Global imports
import re
import os
import time
import base64
import requests
import tempfile
from pathlib import Path
from loguru import logger
from typing import List, Dict, Any, Tuple, Optional

# Local imports
from src.shared.config.settings import settings
from src.shared.utils.s3_client import S3Client


class ElevenLabsAudioGenerator:
    """Handles professional voiceover generation with enhanced regex parsing and scene-based extraction"""

    def __init__(self):
        # ——————————————
        # 1) Read API key from environment
        # ——————————————
        self.api_key = os.getenv("ELEVENLABS_API_KEY") or getattr(
            settings, "elevenlabs_api_key", None
        )
        if not self.api_key:
            raise ValueError("ELEVENLABS_APIKEY not found in environment variables")

        # Base ElevenLabs endpoints
        self.base_url = "https://api.elevenlabs.io/v1"

        # ——————————————————————————————
        # 2) Default voice_id (from ENV or hardcode)
        # ——————————————————————————————
        #    This should be a valid ElevenLabs voice_id, e.g. "JBFqnCBsd6RMkjVDRZzb"
        #    If you want a friendly‐name map, do it in your own code.
        self.default_voice_id = os.getenv("ELEVENLABS_DEFAULT_VOICE_ID", "")
        # Expose default_voice so Gradio can do: value=audio_gen.default_voice
        # (must match one of available_voices below).
        self.default_voice = self.default_voice_id

        # ——————————————————————————————————————————————
        # 3) Default numeric parameters for ElevenLabs "voice_settings"
        # ——————————————————————————————————————————————
        self.default_stability = 0.5
        self.default_similarity_boost = 0.75
        self.default_speed = 1.0
        self.default_style = 0.0

        # —————————————————————————————————————————————————————————
        # 4) A list of "voice_ids" you want to expose in Gradio's dropdown. Each entry
        #    must be a valid ElevenLabs voice_id. For example, if "Rachel" is  JBFqnC...,
        #    put that ID here. If you want friendly labels, keep a separate mapping.
        # —————————————————————————————————————————————————————————
        self.available_voices = [
            # Replace these with real ElevenLabs voice IDs that your account has access to:
            "JBFqnCBsd6RMkjVDRZzb",  # (e.g. "Rachel")
            "EXAMPLE_VOICE_ID_2",  # (e.g. "Domi")
            # …add as many IDs as you like…
        ]

        self.s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
        self.s3_region = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
        self.s3_client = S3Client(self.s3_region)

    def _headers(self) -> Dict[str, str]:
        return {
            "Content-Type": "application/json",
            "xi-api-key": self.api_key,
        }

    def test_api_connection(self) -> Tuple[bool, str]:
        """
        Test connection by listing available voices.
        """
        if not self.api_key:
            return False, "❌ ELEVENLABS_API_KEY not found in environment variables"

        try:
            resp = requests.get(f"{self.base_url}/voices", headers=self._headers(), timeout=10)
            if resp.status_code == 200:
                data = resp.json()
                if "voices" in data:
                    return True, "✅ ElevenLabs API connection successful!"
                else:
                    return False, f"❌ Unexpected response structure: {data}"
            else:
                return False, f"❌ API connection failed: HTTP {resp.status_code} - {resp.text}"
        except Exception as e:
            return False, f"❌ API connection failed: {str(e)}"

    def _normalize_script(self, script: str) -> str:
        """Normalize script formatting for better parsing"""
        # Normalize line endings
        script = re.sub(r"\r\n", "\n", script)
        script = re.sub(r"\r", "\n", script)

        # Clean up excessive whitespace
        script = re.sub(r"\n\s*\n\s*\n+", "\n\n", script)  # Multiple empty lines to double
        script = re.sub(r"[ \t]+", " ", script)  # Multiple spaces/tabs to single space

        # Normalize common formatting variations
        script = re.sub(r"Scene\s*(\d+)\s*[-–—:]\s*", r"Scene \1: ", script)
        script = re.sub(r"(\d+)\.\s*Scene\s*[-–—:]?\s*", r"Scene \1: ", script)

        return script.strip()

    def _parse_with_multiple_strategies(self, script: str) -> List[Dict]:
        """Try multiple parsing strategies to extract narrations"""

        # Strategy 1: Standard scene-based parsing
        narrations = self._parse_standard_scenes(script)
        if narrations:
            return narrations

        # Strategy 2: Block-based parsing (for scripts with clear blocks)
        narrations = self._parse_block_based(script)
        if narrations:
            return narrations

        # Strategy 3: Line-by-line intelligent parsing
        narrations = self._parse_intelligent_lines(script)
        if narrations:
            return narrations

        # Strategy 4: Fallback - create narrations from any identifiable content
        return self._parse_fallback(script)

    def _parse_standard_scenes(self, script: str) -> List[Dict]:
        """Parse script using standard scene markers with enhanced regex"""
        narrations = []
        lines = script.split("\n")
        current_scene = None
        current_narration = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Enhanced scene detection patterns - more strict and precise
            scene_patterns = [
                r"^\*?\*?Scene\s+(\d+)\s*:?\*?\*?\s*(.*)$",  # **Scene 1:** or Scene 1: with optional markdown
                r"^\*?\*?Scene\s+(\d+)\s+[-–—]\s*(.*)$",  # **Scene 1 -** with optional markdown
                r"^\*?\*?Scene\s+(\d+)\s*\.?\*?\*?\s*(.*)$",  # **Scene 1.** with optional markdown
                r"^\*?\*?Scene\s+(\d+)\s*\*?\*?$",  # Just **Scene 1** on its own line
            ]

            scene_found = False
            for pattern in scene_patterns:
                match = re.match(pattern, line, re.IGNORECASE)
                if match:
                    # Additional validation - skip if this looks like content within a scene
                    if self._is_likely_scene_content(line):
                        continue

                    # Save previous scene narration
                    if current_scene and current_narration:
                        narrations.append(
                            {"scene_number": current_scene, "narration": current_narration.strip()}
                        )

                    # Start new scene
                    scene_num = int(match.group(1))
                    current_scene = scene_num
                    current_narration = ""
                    scene_found = True
                    break

            if scene_found:
                continue

            # Process narration lines if we have a current scene
            if current_scene and self._is_narration_line(line):
                narration_text = self._extract_narration_text(line)
                if narration_text:
                    current_narration = narration_text

        # Don't forget the last scene
        if current_scene and current_narration:
            narrations.append(
                {"scene_number": current_scene, "narration": current_narration.strip()}
            )

        return narrations

    def _is_likely_scene_content(self, line: str) -> bool:
        """Check if a line that matches scene pattern is actually scene content"""
        # Skip lines that contain common non-scene keywords
        content_indicators = [
            "overview:",
            "title:",
            "style:",
            "input:",
            "total duration:",
            "approximately",
            "weighing",
            "measuring",
            "showing",
            "including",
            "background music",
            "sounds",
            "imagine",
            "right?",
            "but",
            "so while",
            "this leaves room",
            "you could add",
            "adjust the length",
            "achieve the exact",
            "target",
            "upbeat jingle",
            "throughout",
            "refers to",
            "capturing",
            "screenshot",
        ]

        line_lower = line.lower()
        for indicator in content_indicators:
            if indicator in line_lower:
                return True

        # Skip lines that look like metadata or instructions
        if any(
            keyword in line_lower
            for keyword in ["**total duration:**", "total duration:", "approximately"]
        ):
            return True

        # If line contains "duration" but doesn't start with Scene, it's probably metadata
        if "duration" in line_lower and not re.match(r"^\*?\*?scene\s+\d+", line_lower):
            return True

        # If line is very long (>150 chars), it's probably content, not a scene header
        if len(line) > 150:
            return True

        # Skip lines that look like instructions or examples
        instruction_patterns = [
            r"the user requested",
            r"therefore",
            r"will be adjusted",
            r"this should",
            r"example",
            r"note:",
            r"fix it",
            r"improve",
            r"sometimes",
        ]

        for pattern in instruction_patterns:
            if re.search(pattern, line_lower):
                return True

        return False

    def _is_narration_line(self, line: str) -> bool:
        """Check if a line contains narration content"""
        # Enhanced narration detection patterns
        narration_patterns = [
            r"^\*?\*?(?:Narration|Text|Voice\s*over|Audio|Script|Dialogue)\*?\*?\s*[:\-]\s*(.+)$",
            r"^\*?\*?Narration\*?\*?\s*[:\-]\s*(.+)$",
            r"^\*?\*?Voice\s*over\*?\*?\s*[:\-]\s*(.+)$",
        ]

        for pattern in narration_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return True

        return False

    def _extract_narration_text(self, line: str) -> str:
        """Extract quoted narration text from a narration line"""
        # Pattern: Find text within double quotes, handling voice-over annotations
        # Example: ((Voice-over, slightly exasperated)) "Another long day. What's for dinner... again?"
        quote_pattern = r'"([^"]*)"'
        quote_matches = re.findall(quote_pattern, line)

        if quote_matches:
            # Take the first (and usually only) quoted text
            return quote_matches[0].strip()

        return ""

    def _parse_block_based(self, script: str) -> List[Dict]:
        """Parse script by identifying content blocks"""
        # Split script into blocks (double newlines typically separate blocks)
        blocks = re.split(r"\n\s*\n", script)
        narrations = []
        scene_num = 1

        for block in blocks:
            block = block.strip()
            if not block or len(block) < 20:  # Skip very short blocks
                continue

            # Skip header blocks (title, overview, etc.)
            if any(
                keyword in block.lower()
                for keyword in [
                    "title:",
                    "overview:",
                    "introduction:",
                    "total duration:",
                    "approximately",
                ]
            ):
                continue

            # Only process blocks that look like scenes
            if self._block_looks_like_scene(block):
                narration = self._extract_narration_from_block(block, scene_num)
                if narration:
                    narrations.append(narration)
                    scene_num += 1

        return narrations

    def _block_looks_like_scene(self, block: str) -> bool:
        """Check if a block looks like it contains scene information"""
        # Look for scene indicators
        scene_indicators = ["visual:", "narration:", "duration:", "scene"]
        block_lower = block.lower()

        # Must contain "scene" and at least one other indicator
        has_scene = "scene" in block_lower
        indicator_count = sum(
            1 for indicator in scene_indicators[:-1] if indicator in block_lower
        )  # Exclude 'scene' from count

        # A block with scene + 2+ other indicators is likely a scene
        return has_scene and indicator_count >= 2

    def _extract_narration_from_block(self, block: str, scene_num: int) -> Optional[Dict]:
        """Extract narration information from a text block"""
        lines = [line.strip() for line in block.split("\n") if line.strip()]
        if not lines:
            return None

        narration_text = ""

        # Process each line in the block
        for line in lines:
            if self._is_narration_line(line):
                extracted_text = self._extract_narration_text(line)
                if extracted_text:
                    narration_text = extracted_text
                    break

        if narration_text:
            return {"scene_number": scene_num, "narration": narration_text}

        return None

    def _parse_intelligent_lines(self, script: str) -> List[Dict]:
        """Intelligent line-by-line parsing for various formats"""
        lines = script.split("\n")
        narrations = []
        current_content = []
        scene_num = 1

        in_scenes_section = False

        for line in lines:
            line = line.strip()
            if not line:
                if current_content and in_scenes_section:
                    # Process accumulated content as a potential scene
                    narration = self._create_narration_from_content(current_content, scene_num)
                    if narration:
                        narrations.append(narration)
                        scene_num += 1
                    current_content = []
                continue

            # Detect when we've reached the scenes section
            if line.lower().startswith("scenes:") or line.lower() == "scenes":
                in_scenes_section = True
                continue

            # Skip header lines before scenes section and metadata lines
            skip_keywords = [
                "title:",
                "overview:",
                "introduction:",
                "input:",
                "total duration:",
                "approximately",
                "this leaves room",
                "you could add",
            ]
            if not in_scenes_section and any(keyword in line.lower() for keyword in skip_keywords):
                continue

            # Skip metadata lines even in scenes section
            if any(
                keyword in line.lower()
                for keyword in [
                    "total duration:",
                    "approximately",
                    "this leaves room",
                    "you could add",
                    "adjust the length",
                    "achieve the exact",
                ]
            ):
                continue

            # If we're in scenes section, collect content
            if in_scenes_section:
                current_content.append(line)

        # Process final content
        if current_content and in_scenes_section:
            narration = self._create_narration_from_content(current_content, scene_num)
            if narration:
                narrations.append(narration)

        return narrations

    def _create_narration_from_content(
        self, content_lines: List[str], scene_num: int
    ) -> Optional[Dict]:
        """Create a narration from a list of content lines"""
        if not content_lines:
            return None

        # Filter out metadata lines
        filtered_lines = []
        for line in content_lines:
            if not any(
                keyword in line.lower()
                for keyword in [
                    "total duration:",
                    "approximately",
                    "this leaves room",
                    "you could add",
                ]
            ):
                filtered_lines.append(line)

        # Look for narration lines
        for line in filtered_lines:
            if self._is_narration_line(line):
                narration_text = self._extract_narration_text(line)
                if narration_text:
                    return {"scene_number": scene_num, "narration": narration_text}

        return None

    def _parse_fallback(self, script: str) -> List[Dict]:
        """Fallback parsing - create basic narrations from any content"""
        logger.info("🔍 Using fallback parsing strategy")

        # Remove headers and split into meaningful chunks
        lines = script.split("\n")
        content_lines = []

        in_content = False
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Skip metadata and instruction lines
            skip_patterns = [
                r"title:",
                r"overview:",
                r"duration:",
                r"style:",
                r"input:",
                r"total duration:",
                r"approximately",
                r"this leaves room",
                r"you could add",
                r"adjust the length",
                r"the user requested",
                r"therefore",
                r"will be adjusted",
                r"this should",
                r"example",
                r"note:",
                r"fix it",
                r"improve",
                r"sometimes",
            ]

            should_skip = False
            for pattern in skip_patterns:
                if re.search(pattern, line.lower()):
                    should_skip = True
                    break

            if should_skip:
                continue

            # Start collecting after we pass headers
            if line.lower().startswith("scenes:") or in_content:
                in_content = True
                if not line.lower().startswith("scenes:"):
                    content_lines.append(line)
            elif not any(
                header in line.lower()
                for header in ["title:", "overview:", "duration:", "style:", "input:"]
            ):
                content_lines.append(line)

        logger.info(f"📝 Found {len(content_lines)} content lines")

        if not content_lines:
            # If no content lines found, try to extract any meaningful text
            logger.info("🔍 No content lines found, trying to extract any meaningful text")
            meaningful_lines = []
            for line in script.split("\n"):
                line = line.strip()
                if line and len(line) > 10:  # Lines with substantial content
                    # Remove common prefixes
                    cleaned = re.sub(r"^Scene\s*\d+\s*[:\-.]?\s*", "", line, flags=re.IGNORECASE)
                    cleaned = re.sub(r"^Narration:\s*", "", cleaned, flags=re.IGNORECASE)
                    if cleaned.strip() and len(cleaned.strip()) > 5:
                        meaningful_lines.append(cleaned.strip())

            if meaningful_lines:
                logger.info(f"📝 Found {len(meaningful_lines)} meaningful lines")
                return [{"scene_number": 1, "narration": " ".join(meaningful_lines)}]
            return []

        # Look for narration lines in the content
        narrations = []
        scene_num = 1

        for line in content_lines:
            if self._is_narration_line(line):
                narration_text = self._extract_narration_text(line)
                if narration_text:
                    narrations.append({"scene_number": scene_num, "narration": narration_text})
                    scene_num += 1

        # If no narrations found, treat all content as narration
        if not narrations and content_lines:
            logger.info("🔍 No specific narration lines found, treating all content as narration")
            combined_text = " ".join(content_lines)
            # Clean up the combined text
            combined_text = re.sub(r"\s+", " ", combined_text).strip()
            if combined_text:
                narrations.append({"scene_number": 1, "narration": combined_text})

        return narrations

    def extract_narration_from_script(self, script: str) -> List[Dict[str, str]]:
        """
        Extracts ONLY the narration text from lines that start with "Narration:"
        This ensures we only get text that needs text-to-speech conversion.

        ENHANCED: Now uses multiple parsing strategies with professional regex patterns.
        """
        if not script or not script.strip():
            logger.warning("Empty script provided for narration extraction")
            return []

        logger.info(f"🔍 Starting narration extraction from script of length: {len(script)}")
        logger.debug(f"📝 Script preview: {script[:200]}...")

        # Clean and normalize the script
        script = self._normalize_script(script)

        # Try multiple parsing strategies
        narrations = self._parse_with_multiple_strategies(script)

        # Post-process and validate
        narrations = self._post_process_narrations(narrations)

        logger.info(f"✅ Extracted {len(narrations)} narration(s) from script")
        for narration in narrations:
            logger.debug(f"  Scene {narration['scene_number']}: {narration['narration'][:100]}...")

        return narrations

    def _post_process_narrations(self, narrations: List[Dict]) -> List[Dict]:
        """Post-process narrations to ensure consistency and quality"""
        processed_narrations = []

        for i, narration in enumerate(narrations, 1):
            # Ensure sequential numbering
            narration["scene_number"] = i

            # Clean up narration text
            if "narration" in narration:
                # Clean whitespace and normalize
                narration["narration"] = re.sub(r"\s+", " ", narration["narration"].strip())

                # Remove any parsing artifacts and markdown
                narration["narration"] = re.sub(r"^[-:\s*]+|[-:\s*]+$", "", narration["narration"])
                narration["narration"] = re.sub(r"^\*+|\*+$", "", narration["narration"]).strip()

            processed_narrations.append(narration)

        return processed_narrations

    def extract_narrations_by_scene(self, script: str) -> List[Dict[str, str]]:
        """
        Extracts narrations organized by actual scene sections in the script.
        ENHANCED: Now uses professional regex patterns and multiple parsing strategies.
        """
        if not script or not script.strip():
            logger.warning("Empty script provided for scene-wise narration extraction")
            return []

        # Use the enhanced extraction method
        return self.extract_narration_from_script(script)

    def generate_audio_for_script(
        self,
        script: str,
        voice: str = None,
        stability: float = None,
        similarity_boost: float = None,
        speed: float = None,
        style: float = None,
        include_timestamps: bool = False,
    ) -> Dict[str, Any]:
        """
        Generate one combined MP3 for all narration in the script, using ElevenLabs HTTP API.
        Returns a dict with:
          - "success": bool
          - "audio_url": data:audio/mpeg;base64,… (so you can embed in an <audio> tag)
          - "local_path": path/to/saved.mp3
          - plus any errors or metadata
        """
        voice_id = voice or self.default_voice_id
        stability = stability if stability is not None else self.default_stability
        similarity_boost = (
            similarity_boost if similarity_boost is not None else self.default_similarity_boost
        )
        speed = speed if speed is not None else self.default_speed
        style = style if style is not None else self.default_style

        if not voice_id:
            return {
                "success": False,
                "error": "No voice_id provided. Please specify a valid ElevenLabs voice ID.",
                "audio_url": None,
                "timestamps": None,
            }

        # Use enhanced narration extraction to get only the actual narration text
        narrations = self.extract_narrations_by_scene(script)
        if not narrations:
            return {
                "success": False,
                "error": "No narration found in script",
                "audio_url": None,
                "timestamps": None,
            }

        # Combine all narrations into one text
        narration_texts = [n["narration"].strip() for n in narrations if n["narration"].strip()]
        combined_narration = " ".join(narration_texts)
        if not combined_narration:
            return {
                "success": False,
                "error": "No narration found in script",
                "audio_url": None,
                "timestamps": None,
            }

        logger.info(f"🎤 Generating audio for narration with voice_id: {voice_id}")
        logger.debug(f"📝 Final narration text: {combined_narration}")

        payload = {
            "text": combined_narration,
            "voice_settings": {
                "stability": stability,
                "similarity_boost": similarity_boost,
                "speed": speed,
                "style": style,
            },
        }

        try:
            resp = requests.post(
                f"{self.base_url}/text-to-speech/{voice_id}",
                headers=self._headers(),
                json=payload,
                timeout=120,
            )
            if resp.status_code != 200:
                return {
                    "success": False,
                    "error": f"HTTP {resp.status_code}: {resp.text}",
                    "audio_url": None,
                    "timestamps": None,
                }
            audio_bytes = resp.content
            # Defensive check: ensure audio is not empty
            if not audio_bytes or len(audio_bytes) < 1000:  # 1KB minimum for valid MP3
                logger.error(
                    f"❌ TTS API returned empty or invalid audio. Length: {len(audio_bytes)} bytes. Payload: {payload}. Response headers: {resp.headers}. Response text: {resp.text[:200]}"
                )
                return {
                    "success": False,
                    "error": "TTS API returned empty or invalid audio.",
                    "audio_url": None,
                    "timestamps": None,
                }
            # Upload to S3 (write audio_bytes to temp file first)
            script_id = None
            match = re.search(r"Scene 1:.*", script)
            if hasattr(self, "current_script_id") and self.current_script_id:
                script_id = self.current_script_id
            elif match:
                # fallback: try to parse from context if possible
                script_id = None  # fallback if not available
            else:
                script_id = None
            s3_url = self._upload_audio_bytes_to_s3(audio_bytes, script_id or "unknown")
            logger.info(f"🎤 Uploaded TTS audio to S3: {s3_url}")
            return {
                "success": True,
                "s3_url": s3_url,
                "timestamps": None,
                "voice_used": voice_id,
                "narration_text": combined_narration,
                "scene_count": 1,
                "settings": {
                    "stability": stability,
                    "similarity_boost": similarity_boost,
                    "speed": speed,
                    "style": style,
                },
            }
        except Exception as e:
            logger.error(f"❌ Error generating audio: {str(e)}")
            return {"success": False, "error": str(e), "audio_url": None, "timestamps": None}

    def _upload_audio_bytes_to_s3(self, audio_bytes: bytes, identifier: str) -> str:
        """
        Upload audio bytes to S3 and return the S3 URL.
        """
        import uuid

        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as tmp_file:
            tmp_file.write(audio_bytes)
            temp_path = tmp_file.name
        # Use UUID to ensure unique filenames even when generated at the same time
        unique_id = str(uuid.uuid4())[:8]
        s3_key = (
            f"Vidflux-Assets/text-to-speech-assets/{identifier}-{int(time.time())}-{unique_id}.mp3"
        )
        s3_url = self.s3_client.upload_file(bucket=self.s3_bucket, key=s3_key, file_path=temp_path)
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return s3_url

    def generate_audio_for_scenes(
        self,
        scenes=None,
        script: str = None,
        voice: str = None,
        stability: float = None,
        similarity_boost: float = None,
        speed: float = None,
        style: float = None,
        include_timestamps: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        Generate separate MP3s for each scene's narration.
        Returns a list of dicts: one entry per scene, each with "audio_url", "local_path", etc.

        ENHANCED: Now uses professional regex patterns and improved parsing.
        """
        voice_id = voice or self.default_voice_id
        stability = stability if stability is not None else self.default_stability
        similarity_boost = (
            similarity_boost if similarity_boost is not None else self.default_similarity_boost
        )
        speed = speed if speed is not None else self.default_speed
        style = style if style is not None else self.default_style

        # Legacy path: if "scenes" is provided, use that
        if scenes is not None and script is None:
            return self._generate_audio_from_scene_objects(
                scenes, voice_id, stability, similarity_boost, speed, style, include_timestamps
            )

        if script is None:
            return [
                {
                    "scene_number": 1,
                    "success": False,
                    "error": "No script or scenes provided",
                    "audio_url": None,
                    "timestamps": None,
                    "narration_text": "",
                }
            ]

        # Use enhanced scene-aware extraction
        narrations = self.extract_narrations_by_scene(script)
        if not narrations:
            return [
                {
                    "scene_number": 1,
                    "success": False,
                    "error": "No narrations found in script",
                    "audio_url": None,
                    "timestamps": None,
                    "narration_text": "",
                }
            ]

        results = []
        for n_data in narrations:
            scene_num = n_data["scene_number"]
            narration_text = n_data["narration"]
            logger.info(f"🎤 Processing scene {scene_num} narration: {narration_text[:30]}...")

            if not narration_text.strip():
                results.append(
                    {
                        "scene_number": scene_num,
                        "success": False,
                        "error": "Empty narration text",
                        "audio_url": None,
                        "timestamps": None,
                        "narration_text": narration_text,
                    }
                )
                continue

            payload = {
                "text": narration_text,
                "voice_settings": {
                    "stability": stability,
                    "similarity_boost": similarity_boost,
                    "speed": speed,
                    "style": style,
                },
            }

            try:
                resp = requests.post(
                    f"{self.base_url}/text-to-speech/{voice_id}",
                    headers=self._headers(),
                    json=payload,
                    timeout=120,
                )
                if resp.status_code != 200:
                    results.append(
                        {
                            "scene_number": scene_num,
                            "success": False,
                            "error": f"HTTP {resp.status_code}: {resp.text}",
                            "audio_url": None,
                            "timestamps": None,
                            "narration_text": narration_text,
                        }
                    )
                else:
                    audio_bytes = resp.content
                    # Defensive check: ensure audio is not empty
                    if not audio_bytes or len(audio_bytes) < 1000:  # 1KB minimum for valid MP3
                        logger.error(
                            f"❌ TTS API returned empty or invalid audio for scene {scene_num}. Length: {len(audio_bytes)} bytes. Payload: {payload}. Response headers: {resp.headers}. Response text: {resp.text[:200]}"
                        )
                        results.append(
                            {
                                "scene_number": scene_num,
                                "success": False,
                                "error": "TTS API returned empty or invalid audio.",
                                "audio_url": None,
                                "timestamps": None,
                                "narration_text": narration_text,
                            }
                        )
                        continue
                    # Upload to S3
                    script_id = None
                    match = re.search(r"Scene 1:.*", script)
                    if hasattr(self, "current_script_id") and self.current_script_id:
                        script_id = self.current_script_id
                    elif match:
                        # fallback: try to parse from context if possible
                        script_id = None  # fallback if not available
                    else:
                        script_id = None
                    s3_url = self._upload_audio_bytes_to_s3(audio_bytes, script_id or "unknown")
                    logger.info(f"🎤 Uploaded TTS audio to S3: {s3_url}")

                    results.append(
                        {
                            "scene_number": scene_num,
                            "success": True,
                            "s3_url": s3_url,
                            "timestamps": None,
                            "narration_text": narration_text,
                            "voice_used": voice_id,
                            "settings": {
                                "stability": stability,
                                "similarity_boost": similarity_boost,
                                "speed": speed,
                                "style": style,
                            },
                        }
                    )

            except Exception as e:
                logger.error(f"❌ Error generating audio for scene {scene_num}: {str(e)}")
                results.append(
                    {
                        "scene_number": scene_num,
                        "success": False,
                        "error": str(e),
                        "audio_url": None,
                        "timestamps": None,
                        "narration_text": narration_text,
                    }
                )

            time.sleep(0.5)  # avoid rate limit

        return results

    def _generate_audio_from_scene_objects(
        self,
        scenes: List[Any],
        voice: str = None,
        stability: float = None,
        similarity_boost: float = None,
        speed: float = None,
        style: float = None,
        include_timestamps: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        Legacy method if you pass in a list of scene‐objects rather than a script string.
        """
        voice_id = voice or self.default_voice_id
        stability = stability if stability is not None else self.default_stability
        similarity_boost = (
            similarity_boost if similarity_boost is not None else self.default_similarity_boost
        )
        speed = speed if speed is not None else self.default_speed
        style = style if style is not None else self.default_style

        if not scenes:
            return []

        results = []
        for i, scene in enumerate(scenes, 1):
            # Try to pull narration from scene object/dict
            if hasattr(scene, "narration"):
                scene_narration = scene.narration
            elif hasattr(scene, "description"):
                scene_narration = scene.description
            elif isinstance(scene, dict):
                scene_narration = scene.get("narration", scene.get("description", str(scene)))
            else:
                scene_narration = str(scene)

            if not scene_narration.strip():
                results.append(
                    {
                        "scene_number": i,
                        "success": False,
                        "error": "Empty scene narration",
                        "audio_url": None,
                        "timestamps": None,
                        "narration_text": scene_narration,
                    }
                )
                continue

            payload = {
                "text": scene_narration,
                "voice_settings": {
                    "stability": stability,
                    "similarity_boost": similarity_boost,
                    "speed": speed,
                    "style": style,
                },
            }

            try:
                resp = requests.post(
                    f"{self.base_url}/text-to-speech/{voice_id}",
                    headers=self._headers(),
                    json=payload,
                    timeout=120,
                )
                if resp.status_code != 200:
                    results.append(
                        {
                            "scene_number": i,
                            "success": False,
                            "error": f"HTTP {resp.status_code}: {resp.text}",
                            "audio_url": None,
                            "timestamps": None,
                            "narration_text": scene_narration,
                        }
                    )
                else:
                    audio_bytes = resp.content
                    # Defensive check: ensure audio is not empty
                    if not audio_bytes or len(audio_bytes) < 1000:  # 1KB minimum for valid MP3
                        logger.error(
                            f"❌ TTS API returned empty or invalid audio for scene {i}. Length: {len(audio_bytes)} bytes. Payload: {payload}. Response headers: {resp.headers}. Response text: {resp.text[:200]}"
                        )
                        results.append(
                            {
                                "scene_number": i,
                                "success": False,
                                "error": "TTS API returned empty or invalid audio.",
                                "audio_url": None,
                                "timestamps": None,
                                "narration_text": scene_narration,
                            }
                        )
                        continue
                    # Upload to S3
                    script_id = None
                    match = re.search(r"Scene 1:.*", script)
                    if hasattr(self, "current_script_id") and self.current_script_id:
                        script_id = self.current_script_id
                    elif match:
                        # fallback: try to parse from context if possible
                        script_id = None  # fallback if not available
                    else:
                        script_id = None
                    s3_url = self._upload_audio_bytes_to_s3(audio_bytes, script_id or "unknown")
                    logger.info(f"🎤 Uploaded TTS audio to S3: {s3_url}")

                    results.append(
                        {
                            "scene_number": i,
                            "success": True,
                            "s3_url": s3_url,
                            "timestamps": None,
                            "narration_text": scene_narration,
                            "voice_used": voice_id,
                            "settings": {
                                "stability": stability,
                                "similarity_boost": similarity_boost,
                                "speed": speed,
                                "style": style,
                            },
                        }
                    )

            except Exception as e:
                logger.error(f"❌ Error generating audio for scene {i}: {str(e)}")
                results.append(
                    {
                        "scene_number": i,
                        "success": False,
                        "error": str(e),
                        "audio_url": None,
                        "timestamps": None,
                        "narration_text": scene_narration,
                    }
                )

            time.sleep(0.5)  # avoid rate limit

        return results

    def save_audio_locally(
        self, audio_results: Any, output_dir: str = "output/audio"
    ) -> List[Dict[str, Any]]:
        """
        If you pass in a dict or list of dicts where audio_url is an actual URL (or data URL),
        this will decode and write the MP3 bytes to disk under output_dir.
        """
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        if isinstance(audio_results, dict):
            audio_results = [audio_results]

        for result in audio_results:
            if not result.get("success", False) or not result.get("audio_url"):
                continue

            # If it's already a data URL, decode and save:
            audio_url = result["audio_url"]
            try:
                if audio_url.startswith("data:audio"):
                    header, b64 = audio_url.split(",", 1)
                    audio_bytes = base64.b64decode(b64)
                else:
                    resp = requests.get(audio_url, timeout=30)
                    resp.raise_for_status()
                    audio_bytes = resp.content

                scene_num = result.get("scene_number", "combined")
                if isinstance(scene_num, int):
                    filename = f"scene_{scene_num:02d}_narration.mp3"
                else:
                    filename = f"{scene_num}_narration.mp3"

                local_path = os.path.join(output_dir, filename)
                with open(local_path, "wb") as f:
                    f.write(audio_bytes)
                result["local_path"] = local_path
                logger.info(f"💾 Saved narration audio for scene {scene_num}: {local_path}")

            except Exception as e:
                logger.error(f"❌ Error saving audio for scene {scene_num}: {str(e)}")
                result["save_error"] = str(e)

        return audio_results

    def format_audio_results_for_display(self, results: Any) -> str:
        """
        Build a short text summary of which scenes succeeded/failed.
        """
        if isinstance(results, dict):
            results = [results]
        if not results:
            return "No audio results to display."

        success_count = sum(1 for r in results if r.get("success", False))
        total_count = len(results)

        output_lines = [
            f"🎤 ElevenLabs Narration Audio Generation Complete!",
            f"✅ Successfully generated: {success_count}/{total_count} narration audio files",
            "",
        ]

        for result in results:
            scene_num = result.get("scene_number", "Unknown")
            if result.get("success", False):
                voice = result.get("voice_used", "Unknown")
                audio_url = result.get("audio_url", "")
                local_path = result.get("local_path", "")
                narration_text = result.get("narration_text", "")

                output_lines.extend(
                    [
                        f"✅ Scene {scene_num} Narration:",
                        f"   🎭 Voice ID: {voice}",
                        (
                            f"   📝 Text: {narration_text[:60]}..."
                            if len(narration_text) > 60
                            else f"   📝 Text: {narration_text}"
                        ),
                        (
                            f"   🔗 Data URL: {audio_url[:60]}..."
                            if len(audio_url) > 60
                            else f"   🔗 Data URL: {audio_url}"
                        ),
                        f"   💾 Saved: {local_path}" if local_path else "   💾 Not saved locally",
                        "",
                    ]
                )
            else:
                error = result.get("error", "Unknown error")
                narration_text = result.get("narration_text", "")
                output_lines.extend(
                    [
                        f"❌ Scene {scene_num} Narration: Failed",
                        (
                            f"   📝 Text: {narration_text[:60]}..."
                            if len(narration_text) > 60
                            else f"   📝 Text: {narration_text}"
                        ),
                        f"   Error: {error}",
                        "",
                    ]
                )

        return "\n".join(output_lines)

    def get_voice_preview_text(self) -> str:
        """
        Sample text for a quick voice preview.
        """
        return "Hello! This is a preview of how this voice sounds. I will be narrating your video with clear and engaging speech."

    def generate_voice_preview(self, voice: str) -> Dict[str, Any]:
        """
        Generate a brief preview for the given voice_id.
        """
        preview_text = self.get_voice_preview_text()
        return self.generate_audio_for_script(
            script=preview_text,
            voice=voice,
            stability=self.default_stability,
            similarity_boost=self.default_similarity_boost,
            speed=self.default_speed,
            style=self.default_style,
        )


# Global instance
tts_generator = ElevenLabsAudioGenerator()
