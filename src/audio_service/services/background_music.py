# Global imports
import re
import os
import uuid
import time
import shutil
import logging
import tempfile
import requests
from enum import Enum
from pathlib import Path
import google.generativeai as genai
from typing import List, Dict, Any, Optional, Tuple

# Local imports
from src.shared.config.settings import settings
from src.shared.utils.s3_client import S3Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MusicType(Enum):
    INSTRUMENTAL = "instrumental"
    LYRICAL = "lyrical"


class MusicStyle(Enum):
    ROCK = "rock"
    CLASSICAL = "classical"
    AMBIENT = "ambient"
    ELECTRONIC = "electronic"
    JAZZ = "jazz"
    POP = "pop"
    CINEMATIC = "cinematic"
    FOLK = "folk"
    HIP_HOP = "hip-hop"
    COUNTRY = "country"


class BackgroundMusicGenerator:
    def __init__(self):
        self.output_dir = os.getenv("BACKGROUND_MUSIC_OUTPUT_DIR", "output/background_music")
        os.makedirs(self.output_dir, exist_ok=True)
        self.hf_token = (
            os.getenv("HUGGINGFACE_TOKEN")
            or os.getenv("HF_TOKEN")
            or os.getenv("HUGGING_FACE_TOKEN")
        )
        self.max_retries = 3
        self.retry_delay = 2
        # LLM service setup
        try:
            from src.shared.services.llm_service import llm_service

            self.llm_service = llm_service
            if llm_service.is_available():
                logger.info("✅ LLM service initialized for music analysis")
            else:
                logger.warning("⚠️ No LLM providers available for music analysis")
                self.llm_service = None
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize LLM service: {e}")
            self.llm_service = None
        # Initialize Gradio client for Stable Audio Open Zero
        try:
            from gradio_client import Client

            self.client = Client("artificialguybr/Stable-Audio-Open-Zero", hf_token=self.hf_token)
            logger.info("✅ Stable Audio Open Zero client initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Stable Audio client: {e}")
            self.client = None
        if not self.hf_token:
            logger.warning(
                "⚠️ No Hugging Face token found. Set HUGGINGFACE_TOKEN environment variable."
            )
        self.s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
        self.s3_region = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
        self.s3_client = S3Client(self.s3_region)
        logger.info(f"🎵 Background Music Generator initialized. Output dir: {self.output_dir}")

    def _generate_music_prompt_with_gemini(self, script: str, video_style: str) -> Dict[str, str]:
        try:
            if not self.llm_service:
                logger.warning("LLM service not available, using defaults")
                return {
                    "type": "instrumental",
                    "style": "cinematic",
                    "reason": "LLM service not initialized",
                }
            gemini_prompt = f"""
            Analyze this video script and recommend appropriate background music:
            Script: {script}
            Video Style: {video_style}
            Based on the content, tone, and style, recommend:
            1. Music Type: Choose ONLY from: instrumental, lyrical
            2. Music Style: Choose ONLY from: rock, classical, ambient, electronic, jazz, pop, cinematic, folk, hip-hop, country
            Respond in this exact format:
            Type: [instrumental/lyrical]
            Style: [one of the styles above]
            Reason: [brief explanation]
            """
            result = self.llm_service.generate_content(gemini_prompt)
            if result["success"]:
                response_text = result["content"].strip()
            else:
                logger.warning(f"LLM service failed: {result.get('error')}")
                return {
                    "type": "instrumental",
                    "style": "cinematic",
                    "reason": "LLM service failed",
                }
            lines = response_text.split("\n")
            music_type = "instrumental"
            music_style = "cinematic"
            for line in lines:
                if line.startswith("Type:"):
                    music_type = line.split(":", 1)[1].strip().lower()
                elif line.startswith("Style:"):
                    music_style = line.split(":", 1)[1].strip().lower()
            return {"type": music_type, "style": music_style, "reason": response_text}
        except Exception as e:
            logger.warning(f"Gemini music analysis failed: {e}, using defaults")
            return {
                "type": "instrumental",
                "style": "cinematic",
                "reason": "Using default values due to analysis failure",
            }

    def generate_script_background_music(
        self, script: str, video_style: str = "cinematic", duration: int = 30
    ) -> Dict[str, Any]:
        """
        Generate background music using Stable Audio Open Zero via Gradio, with Gemini prompt analysis and quota management
        """
        try:
            if not self.client:
                return {"success": False, "error": "Stable Audio client not initialized"}
            # Use Gemini to determine music type/style
            gemini_analysis = self._generate_music_prompt_with_gemini(script, video_style)
            logger.info(f"Gemini analysis result: {gemini_analysis}")
            music_type = gemini_analysis["type"]
            music_style = gemini_analysis["style"]
            prompt = self._build_prompt(script, video_style, music_type, music_style)
            logger.info(f"Music prompt for Stable Audio: {prompt}")
            logger.info(
                f"🎵 Generating background music using Stable Audio Open Zero for {video_style} style, duration: {duration}s, type: {music_type}, style: {music_style}"
            )
            # Quota management and retries
            for attempt in range(self.max_retries):
                try:
                    audio_result = self.client.predict(
                        prompt=prompt,
                        seconds_total=duration,
                        steps=100 if attempt == 0 else 50,  # Reduce steps on retry
                        cfg_scale=7 if attempt == 0 else 5,  # Reduce cfg on retry
                        api_name="/predict",
                    )
                    upload_result = self._download_and_upload_audio_to_s3(audio_result)
                    if (
                        upload_result
                        and upload_result.get("s3_url")
                        and upload_result.get("music_id")
                    ):
                        logger.info(
                            f"✅ Background music uploaded to S3: {upload_result['s3_url']}"
                        )
                        return {
                            "success": True,
                            "s3_url": upload_result["s3_url"],
                            "music_id": upload_result["music_id"],
                            "prompt": prompt,
                            "duration": duration,
                            "music_type": music_type,
                            "music_style": music_style,
                            "gemini_reason": gemini_analysis.get("reason"),
                        }
                    else:
                        logger.error("❌ Failed to upload generated audio to S3")
                        return {"success": False, "error": "Failed to upload generated audio to S3"}
                except Exception as e:
                    error_msg = str(e)
                    if "exceeded your GPU quota" in error_msg:
                        logger.warning(
                            f"🚫 GPU quota exceeded on attempt {attempt+1}, retrying with reduced params..."
                        )
                        time.sleep(self.retry_delay)
                        continue
                    logger.error(f"❌ Background music generation failed: {error_msg}")
                    return {
                        "success": False,
                        "error": f"Background music generation failed: {error_msg}",
                    }
            return {"success": False, "error": "All generation attempts failed or quota exceeded"}
        except Exception as e:
            logger.error(f"❌ Background music generation failed: {str(e)}")
            return {"success": False, "error": f"Background music generation failed: {str(e)}"}

    def _build_prompt(
        self,
        script: str,
        video_style: str,
        music_type: str = "instrumental",
        music_style: str = "cinematic",
    ) -> str:
        # Clean prompt with only music-related information
        base = f"{music_style} {music_type} background music"

        if music_type == "instrumental":
            base += ", no vocals"
        elif music_type == "lyrical":
            base += ", with vocals"

        # Add style context without script content
        if video_style:
            base += f", {video_style} style"

        return base

    def _download_and_upload_audio_to_s3(self, audio_result) -> Optional[dict]:
        """
        Download the generated audio file (if needed) and upload to S3. Return the S3 URL and music_id.
        """
        try:
            if not audio_result:
                logger.error("❌ No audio result provided")
                return None
            # Determine audio URL or local path
            if isinstance(audio_result, str):
                audio_path = audio_result
            elif isinstance(audio_result, dict) and "url" in audio_result:
                audio_path = audio_result["url"]
            elif isinstance(audio_result, list) and len(audio_result) > 0:
                audio_path = audio_result[0]
            else:
                logger.error(f"❌ Unexpected audio result format: {type(audio_result)}")
                return None
            logger.info(f"📥 Processing audio from: {audio_path}")
            # Download audio to temp file if it's a URL
            temp_path = None
            if audio_path.startswith("http://") or audio_path.startswith("https://"):
                response = requests.get(audio_path, timeout=120, stream=True)
                response.raise_for_status()
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            tmp_file.write(chunk)
                    temp_path = tmp_file.name
                file_to_upload = temp_path
            elif os.path.isfile(audio_path):
                file_to_upload = audio_path
            else:
                logger.error(f"❌ No valid audio source found: {audio_path}")
                return None
            # Check file existence and size before upload (like image upload)
            if not os.path.exists(file_to_upload):
                logger.error(f"❌ File to upload does not exist: {file_to_upload}")
                return None
            file_size = os.path.getsize(file_to_upload)
            if file_size == 0:
                logger.error(f"❌ File to upload is empty: {file_to_upload}")
                return None
            logger.info(
                f"Preparing to upload audio file to S3: bucket={self.s3_bucket}, key=..., path={file_to_upload}, size={file_size} bytes"
            )
            # Generate UUID ONCE
            music_id = str(uuid.uuid4())
            s3_key = f"Vidflux-Assets/background-music-assets/music_{music_id}.wav"
            self.s3_client.upload_file(bucket=self.s3_bucket, key=s3_key, file_path=file_to_upload)
            from datetime import timedelta

            s3_url = self.s3_client.get_presigned_url(self.s3_bucket, s3_key, timedelta(hours=1))
            logger.info(f"✅ Successfully uploaded audio to S3: {s3_url}")
            # Remove temp file after upload (like image upload)
            if temp_path and os.path.exists(temp_path):
                os.remove(temp_path)
            return {"s3_url": s3_url, "music_id": music_id}
        except Exception as e:
            logger.error(f"❌ Error uploading audio to S3: {str(e)}")
            return None

    def _download_and_save_audio(self, audio_result) -> Optional[str]:
        """
        Download the generated audio file and save it locally.
        Returns the local file path.
        """
        try:
            if not audio_result:
                logger.error("❌ No audio result provided")
                return None

            # Determine audio URL or local path
            if isinstance(audio_result, str):
                audio_path = audio_result
            elif isinstance(audio_result, dict) and "url" in audio_result:
                audio_path = audio_result["url"]
            elif isinstance(audio_result, list) and len(audio_result) > 0:
                audio_path = audio_result[0]
            else:
                logger.error(f"❌ Unexpected audio result format: {type(audio_result)}")
                return None

            logger.info(f"📥 Processing audio from: {audio_path}")

            # If it's already a local file, just return the path
            if os.path.isfile(audio_path):
                return audio_path

            # If it's a URL, download it to a local file
            if audio_path.startswith("http://") or audio_path.startswith("https://"):
                response = requests.get(audio_path, timeout=120, stream=True)
                response.raise_for_status()

                # Create a unique filename in the output directory
                music_id = str(uuid.uuid4())
                local_filename = f"music_{music_id}.wav"
                local_path = os.path.join(self.output_dir, local_filename)

                with open(local_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                logger.info(f"✅ Audio downloaded to: {local_path}")
                return local_path
            else:
                logger.error(f"❌ Invalid audio path: {audio_path}")
                return None

        except Exception as e:
            logger.error(f"❌ Error downloading and saving audio: {str(e)}")
            return None

    def generate_music_for_style(
        self, style: str, duration: int = 30, tempo: str = "medium"
    ) -> Dict[str, Any]:
        """
        Generate music for a specific style using Stable Audio Open Zero

        Args:
            style: Music style (rock, classical, ambient, etc.)
            duration: Duration in seconds
            tempo: Tempo (slow, medium, fast)

        Returns:
            Dict with success status and audio file path
        """
        try:
            logger.info(f"🎵 Generating {style} music, duration: {duration}s, tempo: {tempo}")

            # Build prompt for the specific style
            prompt = f"{style} music, {tempo} tempo, background music"

            if not self.client:
                return {"success": False, "error": "Stable Audio client not initialized"}

            # Generate audio using Stable Audio Open Zero
            audio_result = self.client.predict(
                prompt=prompt, seconds_total=duration, steps=100, cfg_scale=7, api_name="/predict"
            )

            if audio_result:
                # Download and save the audio file
                audio_path = self._download_and_save_audio(audio_result)

                if audio_path and os.path.exists(audio_path):
                    return {
                        "success": True,
                        "audio_path": audio_path,
                        "local_path": audio_path,
                        "style": style,
                        "duration": duration,
                        "tempo": tempo,
                    }
                else:
                    return {"success": False, "error": "Failed to save generated audio file"}
            else:
                return {"success": False, "error": "No audio generated by Stable Audio Open Zero"}

        except Exception as e:
            logger.error(f"❌ Music generation failed: {str(e)}")
            return {"success": False, "error": f"Music generation failed: {str(e)}"}

    def generate_background_music_with_custom_prompt(
        self, script_id: str, custom_prompt: str, duration: int = 30
    ) -> Dict[str, Any]:
        """
        Generate background music using a custom prompt for the PUT endpoint

        Args:
            script_id: The script ID (for logging purposes)
            custom_prompt: The user-provided custom prompt for music generation
            duration: Duration in seconds

        Returns:
            Dict with success status and audio file path
        """
        try:
            if not self.client:
                return {"success": False, "error": "Stable Audio client not initialized"}

            logger.info(f"🎵 Generating background music with custom prompt for script {script_id}")
            logger.info(f"Custom prompt: {custom_prompt}")

            # Use the custom prompt directly for music generation
            for attempt in range(self.max_retries):
                try:
                    audio_result = self.client.predict(
                        prompt=custom_prompt,
                        seconds_total=duration,
                        steps=100 if attempt == 0 else 50,  # Reduce steps on retry
                        cfg_scale=7 if attempt == 0 else 5,  # Reduce cfg on retry
                        api_name="/predict",
                    )

                    # Save the audio file locally and return the path
                    if audio_result:
                        audio_path = self._download_and_save_audio(audio_result)

                        if audio_path and os.path.exists(audio_path):
                            logger.info(
                                f"✅ Background music generated successfully for script {script_id}"
                            )
                            return {
                                "success": True,
                                "audio_file_path": audio_path,
                                "prompt": custom_prompt,
                                "duration": duration,
                                "script_id": script_id,
                            }
                        else:
                            logger.error("❌ Failed to save generated audio file")
                            return {
                                "success": False,
                                "error": "Failed to save generated audio file",
                            }
                    else:
                        logger.error("❌ No audio generated by Stable Audio")
                        return {"success": False, "error": "No audio generated by Stable Audio"}

                except Exception as e:
                    error_msg = str(e)
                    if "exceeded your GPU quota" in error_msg:
                        logger.warning(
                            f"🚫 GPU quota exceeded on attempt {attempt+1}, retrying with reduced params..."
                        )
                        time.sleep(self.retry_delay)
                        continue
                    logger.error(f"❌ Background music generation failed: {error_msg}")
                    return {
                        "success": False,
                        "error": f"Background music generation failed: {error_msg}",
                    }

            return {"success": False, "error": "All generation attempts failed or quota exceeded"}

        except Exception as e:
            logger.error(f"❌ Custom prompt background music generation failed: {str(e)}")
            return {
                "success": False,
                "error": f"Custom prompt background music generation failed: {str(e)}",
            }

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to Stable Audio Open Zero
        """
        try:
            if not self.client:
                return {"success": False, "error": "Stable Audio client not initialized"}

            # Test with a simple prompt
            test_result = self.client.predict(
                prompt="gentle ambient music",
                seconds_total=5,  # Very short test
                steps=10,  # Minimal steps for faster test
                cfg_scale=5,  # Lower CFG
                api_name="/predict",
            )

            if test_result:
                return {
                    "success": True,
                    "message": "✅ Stable Audio Open Zero connection successful",
                }
            else:
                return {"success": False, "error": "Test returned empty result"}

        except Exception as e:
            return {"success": False, "error": f"Connection test failed: {str(e)}"}
