# Global imports
import re
import os
import time
import base64
import requests
from pathlib import Path
from typing import List, Dict, Any, Tuple

# Local imports
from src.shared.config.settings import settings


class ElevenLabsAudioGenerator:
    def __init__(self):
        self.api_key = os.getenv("ELEVENLABS_API_KEY") or getattr(
            settings, "elevenlabs_api_key", None
        )
        self.base_url = "https://api.elevenlabs.io/v1"
        self.output_dir = os.getenv("ELEVENLABS_OUTPUT_DIR", "output/audio")
        os.makedirs(self.output_dir, exist_ok=True)

        if not self.api_key:
            raise EnvironmentError("ELEVENLABS_API_KEY not found in environment variables")

    def generate_audio_for_script(
        self, script: str, voice_id: str = "21m00Tcm4TlvDq8ikWAM"
    ) -> Dict[str, Any]:
        """
        Generate TTS audio from script content using ElevenLabs API

        Args:
            script: The script content to convert to speech
            voice_id: ElevenLabs voice ID (default: <PERSON> voice)

        Returns:
            Dict with success status and audio file path
        """
        try:
            # Clean and prepare the script
            cleaned_script = self._clean_script(script)
            if not cleaned_script:
                return {"success": False, "error": "No valid script content provided"}

            # Generate audio using ElevenLabs API
            audio_path = self._generate_tts_audio(cleaned_script, voice_id)

            if audio_path and os.path.exists(audio_path):
                return {
                    "success": True,
                    "audio_path": audio_path,
                    "local_path": audio_path,
                    "script_length": len(cleaned_script),
                }
            else:
                return {"success": False, "error": "Failed to generate TTS audio"}

        except Exception as e:
            return {"success": False, "error": f"TTS generation failed: {str(e)}"}

    def _clean_script(self, script: str) -> str:
        """Clean and prepare script for TTS"""
        if not script:
            return ""

        # Remove extra whitespace and normalize
        cleaned = re.sub(r"\s+", " ", script.strip())

        # Remove markdown formatting
        cleaned = re.sub(r"[*_`#]", "", cleaned)

        # Split into sentences and take first few for demo
        sentences = re.split(r"[.!?]+", cleaned)
        sentences = [s.strip() for s in sentences if s.strip()]

        # Limit to first 3 sentences for demo (to avoid long audio)
        demo_script = ". ".join(sentences[:3])
        if demo_script:
            demo_script += "."

        return demo_script

    def _generate_tts_audio(self, text: str, voice_id: str) -> str:
        """Generate TTS audio using ElevenLabs API"""
        try:
            url = f"{self.base_url}/text-to-speech/{voice_id}"

            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.api_key,
            }

            data = {
                "text": text,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": {"stability": 0.5, "similarity_boost": 0.5},
            }

            response = requests.post(url, json=data, headers=headers)

            if response.status_code == 200:
                # Save audio file
                timestamp = int(time.time())
                filename = f"voiceover_{timestamp}.mp3"
                filepath = os.path.join(self.output_dir, filename)

                with open(filepath, "wb") as f:
                    f.write(response.content)

                return filepath
            else:
                print(f"ElevenLabs API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"Error generating TTS audio: {str(e)}")
            return None

    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices from ElevenLabs"""
        try:
            url = f"{self.base_url}/voices"
            headers = {"xi-api-key": self.api_key}

            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                return response.json().get("voices", [])
            else:
                return []

        except Exception as e:
            print(f"Error fetching voices: {str(e)}")
            return []

    def generate_audio_for_scenes(
        self, scenes: List[dict], voice_id: str = "21m00Tcm4TlvDq8ikWAM"
    ) -> List[Dict[str, Any]]:
        """
        Generate TTS audio for each scene using the narration field from scene dicts.
        Args:
            scenes: List of scene dicts, each with a 'narration' field
            voice_id: ElevenLabs voice ID
        Returns:
            List of dicts with success status and audio file path for each scene
        """
        results = []
        for scene in scenes:
            narration = scene.get("narration", "").strip()
            scene_number = scene.get("scene_number")
            if not narration:
                results.append(
                    {
                        "scene_number": scene_number,
                        "success": False,
                        "error": "No narration found for scene",
                    }
                )
                continue
            audio_path = self._generate_tts_audio(narration, voice_id)
            if audio_path and os.path.exists(audio_path):
                results.append(
                    {
                        "scene_number": scene_number,
                        "success": True,
                        "audio_path": audio_path,
                        "local_path": audio_path,
                        "narration": narration,
                    }
                )
            else:
                results.append(
                    {
                        "scene_number": scene_number,
                        "success": False,
                        "error": "Failed to generate TTS audio for scene",
                        "narration": narration,
                    }
                )
        return results
