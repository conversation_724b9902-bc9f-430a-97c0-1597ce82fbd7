# Global imports
from typing import Optional, List
from pydantic import BaseModel


class GenerateImagesRequest(BaseModel):
    scene_id: str
    aspect_ratio: Optional[str] = "16:9"
    include_brand: Optional[bool] = True


class RegenerateImagesRequest(BaseModel):
    scene_ids: List[str]
    aspect_ratio: Optional[str] = "16:9"
    include_brand: Optional[bool] = True


class SelectImageRequest(BaseModel):
    generation_id: str
    image_id: str


class GenerateImagesForScriptRequest(BaseModel):
    script_id: str
    aspect_ratio: Optional[str] = "16:9"
    include_brand: Optional[bool] = True


class GenerateImagesForScenesRequest(BaseModel):
    scene_ids: List[str]
    aspect_ratio: Optional[str] = "16:9"
    include_brand: Optional[bool] = True


class UpdatePromptAndRegenerateRequest(BaseModel):
    updated_prompt: str
    aspect_ratio: Optional[str] = "16:9"
    include_brand: Optional[bool] = True
