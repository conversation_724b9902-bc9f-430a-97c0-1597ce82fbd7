# Global imports
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional, Dict, Any


class ImageGenerationResponse(BaseModel):
    generation_id: str
    scene_id: str
    status: str
    progress: int
    message: Optional[str] = None


class ImageGenerationStatusResponse(BaseModel):
    generation_id: str
    status: str
    progress: int
    images: Optional[List[str]] = None
    error: Optional[str] = None


class ImageSelectionResponse(BaseModel):
    image_id: str
    selected: bool
    message: Optional[str] = None


class ImageGenerationListResponse(BaseModel):
    scene_id: str
    scene_title: str
    images: List[Dict[str, Any]]
    generation_status: Optional[str] = None
    generation_progress: int = 0
    selected_image_id: Optional[str] = None


class ScriptImageGenerationResponse(BaseModel):
    script_id: str
    scene_generations: List[ImageGenerationResponse]
