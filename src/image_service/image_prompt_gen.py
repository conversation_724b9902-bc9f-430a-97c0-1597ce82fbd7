"""
Image Prompt Generator
Handles generation of image prompts from scene data
"""

# Global imports
import re
import base64
from pathlib import Path
from loguru import logger
from typing import List, Dict, Optional, Any

# Local imports
from src.shared.config.settings import settings
from src.shared.services.llm_service import llm_service


class ImagePromptGenerator:
    """Professional Image Prompt Generator with character consistency and single-image focus"""

    def __init__(self):
        """Initialize the professional image prompt generator with unified LLM service"""
        # Check if LLM service is available
        if not llm_service.is_available():
            raise ValueError("No LLM providers available")

        self.llm_service = llm_service

        self.brand_info = {}
        self.brand_visual_analysis = ""
        self.character_profile = ""  # Store character consistency data
        self.location_groups = {}  # Store location group backgrounds for consistency

    def set_brand_info(self, brand_info: Dict[str, Any]):
        """
        Set brand information for prompt generation

        Args:
            brand_info: Dictionary containing brand details from ScriptGenerator
        """
        self.brand_info = brand_info
        logger.info(f"Brand info set for image prompt generation: {brand_info.get('brand_name')}")

        # Analyze brand image if path provided
        if brand_info.get("brand_image_path"):
            self.brand_visual_analysis = self._analyze_brand_visual_style(
                brand_info["brand_image_path"]
            )

    def _extract_character_profile(self, scenes: List[Dict]) -> str:
        """
        Extract character profile from scenes for consistency across all images

        Args:
            scenes: List of scene dictionaries

        Returns:
            Character profile description for consistent generation
        """
        import time

        def make_api_call_with_retry(prompt: str, max_retries: int = 3) -> str:
            """Make API call with retry logic and exponential backoff"""
            for attempt in range(max_retries):
                try:
                    logger.info(
                        f"🎭 Attempting character profile extraction (attempt {attempt + 1}/{max_retries})"
                    )
                    result = self.llm_service.generate_content(prompt)
                    if result["success"]:
                        return result["content"].strip()
                    else:
                        raise Exception(f"LLM service failed: {result.get('error')}")
                except Exception as e:
                    if "504" in str(e) or "timeout" in str(e).lower():
                        if attempt < max_retries - 1:
                            wait_time = (2**attempt) * 2  # Exponential backoff: 2, 4, 8 seconds
                            logger.warning(
                                f"⚠️ API timeout, retrying in {wait_time} seconds... (attempt {attempt + 1})"
                            )
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.error(
                                f"❌ All retry attempts failed for character profile extraction: {e}"
                            )
                            raise
                    else:
                        logger.error(f"❌ Non-timeout error in character profile extraction: {e}")
                        raise
            return ""

        try:
            logger.info("🎭 Extracting character profile for consistency")

            # Combine all scene descriptions to find character details
            scene_texts = []
            for scene in scenes:
                scene_text = f"Scene {scene.get('number')}: {scene.get('description', '')} {scene.get('visual', '')}"
                scene_texts.append(scene_text)

            combined_text = "\n".join(scene_texts)

            character_extraction_prompt = f"""
            You are a PROFESSIONAL CHARACTER CONSISTENCY EXPERT for video production.
            
            Analyze these video scenes and extract a HYPER-DETAILED character profile that will ensure the EXACT SAME PERSON with IDENTICAL clothing and appearance appears in all generated images.
            
            SCENE DESCRIPTIONS:
            {combined_text}
            
            EXTRACT ULTRA-DETAILED CHARACTER PROFILE:
            Create an extremely detailed description of the main character(s) that includes:
            
            **EXACT PHYSICAL CHARACTERISTICS:**
            - Precise age (e.g., "32-year-old" not "mid-30s")
            - Specific gender and ethnicity (be very precise)
            - EXACT hair color with specific shade (e.g., "dark auburn brown hair with slight copper highlights")
            - EXACT hair style and length (e.g., "shoulder-length, slightly wavy, parted on the left side")
            - Precise eye color (e.g., "hazel-green eyes with golden flecks")
            - Specific skin tone and complexion
            - Exact body type, build, and height
            - Distinctive facial features (cheekbones, nose shape, jawline, etc.)
            - Any unique marks, scars, or distinguishing features
            
            **EXACT CLOTHING & STYLING (CRITICAL - MUST REMAIN IDENTICAL):**
            - EXACT outfit description (e.g., "navy blue wool blazer over crisp white cotton button-down shirt")
            - Specific clothing colors, materials, and style details
            - EXACT pants/bottom wear (e.g., "dark indigo straight-leg jeans, well-fitted")
            - Specific footwear (color, style, material)
            - EXACT accessories (e.g., "silver watch with black leather strap on left wrist")
            - Any jewelry details (rings, necklaces, earrings with exact descriptions)
            - Specific styling choices (tucked/untucked, rolled sleeves, etc.)
            
            **GROOMING & STYLING DETAILS:**
            - Exact makeup style (if applicable)
            - Facial hair description (if applicable) - exact style, color, length
            - Nail polish or styling details
            - Any glasses or eyewear with exact descriptions
            
            **POSTURE & MANNERISMS:**
            - Typical posture and stance
            - Characteristic hand positions or gestures
            - Facial expressions and demeanor
            - Body language patterns
            
            CRITICAL: This character description will be used to generate multiple images. Every detail must be EXACTLY the same across all images - especially clothing, hair color, and styling.
            
            Return ONLY the ultra-detailed character profile that will ensure PERFECT consistency across all scenes.
            """

            self.character_profile = make_api_call_with_retry(character_extraction_prompt)

            if self.character_profile:
                logger.success(
                    f"✅ Character profile extracted ({len(self.character_profile)} chars)"
                )
                logger.debug(f"Character profile: {self.character_profile[:200]}...")
                return self.character_profile
            else:
                logger.warning("⚠️ Character profile extraction failed, using fallback")
                return (
                    "Professional adult character with authentic appearance and consistent clothing"
                )

        except Exception as e:
            logger.error(f"❌ Error extracting character profile: {e}")
            return "Professional adult character with authentic appearance and consistent clothing"

    def _extract_location_groups(self, scenes: List[Dict]) -> Dict[str, str]:
        """
        Extract location groups from scenes to ensure background consistency

        Args:
            scenes: List of scene dictionaries

        Returns:
            Dictionary mapping location groups to consistent background descriptions
        """
        try:
            logger.info("🏢 Extracting location groups for background consistency")

            # Group scenes by location
            location_groups = {}
            for scene in scenes:
                # Extract location group from scene if specified
                visual_desc = scene.get("visual", "")

                # Look for location group indicators in the visual description
                location_group = self._determine_location_group(scene, visual_desc)

                if location_group not in location_groups:
                    location_groups[location_group] = []
                location_groups[location_group].append(scene)

            # Generate consistent background descriptions for each location group
            consistent_backgrounds = {}
            for group, group_scenes in location_groups.items():
                if group_scenes:
                    # Use the first scene to generate the master background description
                    master_scene = group_scenes[0]
                    background_desc = self._generate_consistent_background(master_scene, group)
                    consistent_backgrounds[group] = background_desc
                    logger.info(
                        f"📍 Location Group {group}: {len(group_scenes)} scenes with consistent background"
                    )

            self.location_groups = consistent_backgrounds
            logger.success(f"✅ Location groups extracted: {len(consistent_backgrounds)} groups")
            return consistent_backgrounds

        except Exception as e:
            logger.error(f"❌ Error extracting location groups: {e}")
            return {}

    def _determine_location_group(self, scene: Dict, visual_desc: str) -> str:
        """
        Determine which location group a scene belongs to

        Args:
            scene: Scene dictionary
            visual_desc: Visual description text

        Returns:
            Location group identifier
        """
        scene_num = scene.get("number", 1)

        # Check if location group is explicitly mentioned in visual description
        if "Location 1" in visual_desc or "location 1" in visual_desc:
            return "Location_1"
        elif "Location 2" in visual_desc or "location 2" in visual_desc:
            return "Location_2"
        elif "Location 3" in visual_desc or "location 3" in visual_desc:
            return "Location_3"
        elif "Location 4" in visual_desc or "location 4" in visual_desc:
            return "Location_4"

        # Fallback: Group by scene numbers (professional standard grouping)
        if scene_num <= 2:
            return "Location_1"
        elif scene_num <= 4:
            return "Location_2"
        elif scene_num <= 6:
            return "Location_3"
        elif scene_num <= 8:
            return "Location_4"
        elif scene_num <= 10:
            return "Location_5"
        else:
            return "Location_6"

    def _generate_consistent_background(self, master_scene: Dict, location_group: str) -> str:
        """
        Generate a consistent background description for a location group

        Args:
            master_scene: The first scene in the location group
            location_group: Location group identifier

        Returns:
            Consistent background description for the location group
        """
        import time

        def make_api_call_with_retry(prompt: str, max_retries: int = 3) -> str:
            """Make API call with retry logic and exponential backoff"""
            for attempt in range(max_retries):
                try:
                    logger.info(
                        f"🎬 Attempting background generation (attempt {attempt + 1}/{max_retries})"
                    )
                    result = self.llm_service.generate_content(prompt)
                    if result["success"]:
                        return result["content"].strip()
                    else:
                        raise Exception(f"LLM service failed: {result.get('error')}")
                except Exception as e:
                    if "504" in str(e) or "timeout" in str(e).lower():
                        if attempt < max_retries - 1:
                            wait_time = (2**attempt) * 2  # Exponential backoff: 2, 4, 8 seconds
                            logger.warning(
                                f"⚠️ API timeout, retrying in {wait_time} seconds... (attempt {attempt + 1})"
                            )
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.error(
                                f"❌ All retry attempts failed for background generation: {e}"
                            )
                            raise
                    else:
                        logger.error(f"❌ Non-timeout error in background generation: {e}")
                        raise
            return ""

        try:
            logger.info(f"🎬 Generating consistent background for {location_group}")

            visual_desc = master_scene.get("visual", "")
            scene_desc = master_scene.get("description", "")

            background_prompt = f"""
            You are a PROFESSIONAL LOCATION CONSISTENCY EXPERT for video production.
            
            Analyze this scene and extract a HYPER-DETAILED, consistent background/location description that will be used for multiple scenes in the same location group to ensure IDENTICAL backgrounds.
            
            MASTER SCENE DATA:
            Scene Description: {scene_desc}
            Visual Description: {visual_desc}
            Location Group: {location_group}
            
            EXTRACT ULTRA-DETAILED CONSISTENT BACKGROUND ELEMENTS:
            
            **EXACT LOCATION SETTING:**
            - Precise location type with specific details (e.g., "modern corporate office with glass walls" not just "office")
            - Exact architectural features (ceiling height, window styles, door types, etc.)
            - Specific layout and spatial arrangement
            - Interior/exterior designation with precise environment description
            - Specific style period and design aesthetic
            
            **PRECISE LIGHTING & ATMOSPHERE:**
            - Exact lighting setup (e.g., "warm LED ceiling lights at 3000K color temperature plus natural daylight from floor-to-ceiling windows on the east wall")
            - Specific time of day and lighting quality
            - Exact shadow patterns and light direction
            - Precise color temperature and brightness levels
            - Specific atmospheric conditions and mood
            
            **DETAILED BACKGROUND ELEMENTS:**
            - EXACT furniture pieces with specific descriptions (e.g., "dark walnut conference table with black metal legs, 8 black leather swivel chairs")
            - Precise wall colors, textures, and materials (e.g., "light gray painted walls with white trim, exposed brick accent wall")
            - Specific decorative elements and their exact placement
            - Exact props and objects with precise descriptions and positions
            - Specific flooring material and color (e.g., "polished concrete floor in charcoal gray")
            
            **TECHNICAL CAMERA & VISUAL DETAILS:**
            - Optimal camera angles and positions for consistency
            - Specific depth of field characteristics for this location
            - Exact framing references and compositional elements
            - Consistent perspective and viewpoint guidelines
            - Specific visual style and color grading notes
            
            **ENVIRONMENTAL SPECIFICS:**
            - Exact ambient elements (sounds, air quality, temperature feel)
            - Specific background activity or lack thereof
            - Precise spatial dimensions and scale references
            - Exact horizon lines and vanishing points
            - Specific reflection and shadow behaviors
            
            **BRAND INTEGRATION CONTEXT:**
            {"- Precise placement opportunities for " + self.brand_info.get('product_name', 'products') + " with exact surface and positioning options" if self.brand_info.get('brand_name') else "- Specific product placement opportunities with exact positioning"}
            
            CRITICAL: This background description must be SO DETAILED that it can be replicated EXACTLY across multiple images. Every element must be precisely described to ensure perfect visual consistency within the location group.
            
            Return ONLY the ultra-detailed background description that will ensure PERFECT consistency for all scenes in this location group.
            """

            background_desc = make_api_call_with_retry(background_prompt)

            if background_desc:
                logger.success(f"✅ Consistent background generated for {location_group}")
                logger.debug(f"Background: {background_desc[:100]}...")
                return background_desc
            else:
                logger.warning(
                    f"⚠️ Background generation failed for {location_group}, using fallback"
                )
                return f"Professional {location_group.replace('_', ' ').lower()} setting with consistent lighting and atmosphere"

        except Exception as e:
            logger.error(f"❌ Error generating consistent background: {e}")
            return f"Professional {location_group.replace('_', ' ').lower()} setting with consistent lighting and atmosphere"

    def _analyze_brand_visual_style(self, image_path: str) -> str:
        """
        Analyze brand image to understand visual style for integration

        Args:
            image_path: Path to brand image

        Returns:
            Visual style analysis for prompt generation
        """
        try:
            logger.debug(f"Analyzing brand visual style from: {image_path}")

            if not Path(image_path).exists():
                logger.warning(f"Brand image not found: {image_path}")
                return ""

            # Import PIL for proper image handling
            from PIL import Image

            # Load image using PIL
            image = Image.open(image_path)

            analysis_prompt = """
            You are a PROFESSIONAL BRAND VISUAL ANALYST.
            
            Analyze this brand/product image for seamless integration into video scenes:
            
            **PRODUCT SPECIFICATIONS:**
            1. Exact shape, dimensions, proportions
            2. Precise colors (be specific - exact shade descriptions)
            3. Materials, textures, surface properties (matte, glossy, metallic)
            4. Text/logos visible and their exact placement
            5. Unique design elements or patterns
            
            **INTEGRATION REQUIREMENTS:**
            1. Natural placement contexts (how it's typically held, positioned)
            2. Scale relative to human hands/common objects
            3. Lighting requirements to maintain authenticity
            4. Angles that best showcase the product
            
            **BRAND IDENTITY ELEMENTS:**
            1. Key brand colors and color codes
            2. Visual style (modern, classic, luxury, playful, etc.)
            3. Brand personality reflected in design
            4. Critical visual elements that MUST be preserved
            
            Provide precise, technical details for realistic product integration.
            """

            result = self.llm_service.generate_content([analysis_prompt, image])
            logger.debug("Brand visual analysis completed")
            if result["success"]:
                return result["content"]
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                return "Brand visual analysis failed"

        except Exception as e:
            logger.error(f"Error analyzing brand visual style: {e}")
            return ""

    def generate_scene_image_prompt(self, scene: Dict, include_brand: bool = True) -> str:
        """
        Generate professional, single-image prompt for a scene with character consistency

        Args:
            scene: Scene dictionary containing description, visual, narration, duration
            include_brand: Whether to include brand in this specific scene

        Returns:
            Professional image generation prompt optimized for single, high-quality output
        """
        try:
            logger.info(f"🎨 Generating professional prompt for scene {scene.get('number')}")

            # Check if this scene mentions the brand/product
            scene_has_brand = False
            if self.brand_info.get("product_name"):
                scene_text = f"{scene.get('visual', '')} {scene.get('description', '')}"
                if self.brand_info["product_name"].lower() in scene_text.lower():
                    scene_has_brand = True

            # Generate appropriate prompt based on brand presence
            if scene_has_brand and include_brand:
                prompt = self._build_professional_branded_prompt(scene)
            elif include_brand:
                prompt = self._build_professional_branded_prompt(scene)
            else:
                prompt = self._build_professional_standard_prompt(scene)

            result = self.llm_service.generate_content(prompt)
            if result["success"]:
                generated_prompt = result["content"].strip()
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                generated_prompt = scene.get("description", "Professional scene")

            logger.success(f"✅ Professional prompt generated for scene {scene.get('number')}")
            logger.debug(f"Generated prompt preview: {generated_prompt[:150]}...")

            return generated_prompt

        except Exception as e:
            error_msg = f"Error generating professional image prompt for scene {scene.get('number', 'unknown')}: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def _should_include_brand(self, scene: Dict) -> bool:
        """
        Professional brand inclusion analysis - strategic placement approach

        Args:
            scene: Scene dictionary

        Returns:
            Boolean indicating if brand should be included
        """
        if not self.brand_info.get("brand_name"):
            logger.debug("No brand info available")
            return False

        scene_num = scene.get("number", 0)
        scene_text = (
            f"{scene.get('visual', '')} {scene.get('description', '')} {scene.get('narration', '')}"
        )
        product_name = self.brand_info.get("product_name", "")
        brand_name = self.brand_info.get("brand_name", "")

        logger.info(f"🔍 Professional brand analysis for scene {scene_num}")

        # Direct mention check - highest priority
        if product_name and product_name.lower() in scene_text.lower():
            logger.info(f"✅ Scene {scene_num} has DIRECT PRODUCT mention: {product_name}")
            return True

        if brand_name and brand_name.lower() in scene_text.lower():
            logger.info(f"✅ Scene {scene_num} has DIRECT BRAND mention: {brand_name}")
            return True

        # Strategic placement for key scenes (professional approach)
        if scene_num <= 3:  # Strong presence in opening scenes
            logger.info(f"✅ Strategic brand inclusion in opening scene {scene_num}")
            return True

        # Professional placement opportunity keywords
        premium_placement_keywords = [
            "table",
            "desk",
            "hand",
            "holding",
            "using",
            "drinking",
            "eating",
            "enjoying",
            "sharing",
            "displaying",
            "showing",
            "counter",
            "surface",
            "room",
            "home",
            "kitchen",
            "office",
            "elegant",
            "beautiful",
            "crafted",
            "lifestyle",
            "professional",
            "premium",
            "quality",
            "authentic",
            "natural",
            "sophisticated",
        ]

        found_keywords = [kw for kw in premium_placement_keywords if kw in scene_text.lower()]

        if found_keywords:
            logger.info(f"🎯 Scene {scene_num} has premium placement keywords: {found_keywords}")
            # Professional assessment of placement opportunity
            should_include = self._professional_placement_analysis(scene)
            logger.info(f"🤖 Professional analysis for scene {scene_num}: {should_include}")
            return should_include

        # Professional distribution pattern - every 3rd scene for balanced presence
        if scene_num % 3 == 0:
            logger.info(f"✅ Strategic brand distribution in scene {scene_num}")
            return True

        logger.info(f"❌ Scene {scene_num} will not include brand")
        return False

    def _professional_placement_analysis(self, scene: Dict) -> bool:
        """
        Professional analysis of brand placement opportunity using AI assessment

        Args:
            scene: Scene dictionary

        Returns:
            Boolean indicating if this is a premium placement opportunity
        """
        try:
            analysis_prompt = f"""
            You are a PROFESSIONAL BRAND PLACEMENT STRATEGIST.
            
            Assess this scene for natural, authentic product integration:
            
            SCENE: {scene.get('description')}
            VISUAL: {scene.get('visual')}
            BRAND: {self.brand_info.get('brand_name')}
            PRODUCT: {self.brand_info.get('product_name')}
            
            PROFESSIONAL CRITERIA:
            - Does the scene allow for natural product placement?
            - Would the product enhance rather than interrupt the narrative?
            - Is there a logical reason for the product to be present?
            - Can the placement feel authentic and unforced?
            - Does it align with professional advertising standards?
            
            Answer ONLY "yes" or "no" based on professional brand placement best practices.
            """

            result = self.llm_service.generate_content(analysis_prompt)
            if result["success"]:
                return "yes" in result["content"].lower()
            else:
                return False

        except:
            return False

    def _build_professional_branded_prompt(self, scene: Dict) -> str:
        """
        Build professional prompt for FLUX Kontext with strategic brand integration
        """
        try:
            logger.info(f"🏷️ Building professional branded prompt for scene {scene.get('number')}")

            # Generate professional Kontext instruction
            kontext_instruction = self._generate_professional_kontext_instruction(scene)

            # Return with clear professional marker
            result = f"KONTEXT_INSTRUCTION: {kontext_instruction}"
            logger.info(f"✅ Professional branded prompt created")
            logger.debug(f"Branded prompt: {result[:150]}...")
            return result

        except Exception as e:
            logger.error(f"❌ Error building professional branded prompt: {e}")
            # Fallback to standard professional prompt
            return self._build_professional_standard_prompt(scene)

    def _build_professional_standard_prompt(self, scene: Dict) -> str:
        """
        Build professional standard image prompt with location consistency
        """
        logger.info(f"🎨 Building professional standard prompt for scene {scene.get('number')}")

        # Determine location group and get consistent background
        scene_location_group = self._determine_location_group(scene, scene.get("visual", ""))
        consistent_background = self.location_groups.get(scene_location_group, "")

        return f"""
        You are a PROFESSIONAL PROMPT ENGINEER specializing in cinematic image generation with PERFECT character and location consistency.
        
        Create a single, detailed prompt for generating ONE high-quality image with EXACT character consistency and consistent location background:
        
        SCENE DATA:
        • Scene: {scene['description']}
        • Visual Detail: {scene['visual']}
        • Narration Context: {scene['narration']}
        • Duration: {scene.get('duration', '≈3-5s')}
        • Location Group: {scene_location_group}
        
        CRITICAL CHARACTER CONSISTENCY (MUST BE EXACT):
        {self.character_profile if self.character_profile else "Maintain consistent character appearance throughout"}
        
        CRITICAL LOCATION CONSISTENCY (MUST BE IDENTICAL):
        {consistent_background if consistent_background else "Maintain consistent background and setting"}
        
        PROFESSIONAL PROMPT REQUIREMENTS:
        
        **CRITICAL: Generate ONLY ONE IMAGE**
        - NO collages, multiple panels, or split screens
        - NO before/after comparisons
        - NO multiple timeframes in one image
        - SINGLE moment in time, ONE clear composition
        
        **ABSOLUTE CHARACTER CONSISTENCY PRIORITY:**
        - Use the EXACT character description provided above - EVERY DETAIL MUST MATCH
        - CRITICAL: EXACT same clothing as described (same colors, same style, same fit)
        - CRITICAL: EXACT same hair color and hairstyle as described
        - CRITICAL: Same facial features, eye color, skin tone, and build
        - CRITICAL: Same accessories, jewelry, and styling details
        - ONLY vary the character's pose, expression, and action - NOTHING ELSE
        - If character is wearing a "navy blue blazer," it must be EXACTLY that in every image
        - If character has "auburn brown hair," it must be EXACTLY that color in every image
        - Outfit should match the location(example beach wear)
        
        **ABSOLUTE LOCATION CONSISTENCY PRIORITY:**
        - Use the EXACT background/location description provided above - EVERY DETAIL MUST MATCH
        - CRITICAL: IDENTICAL lighting, setting, and environment
        - CRITICAL: Same architectural elements, furniture, and props
        - CRITICAL: Same wall colors, textures, and decorative elements
        - CRITICAL: Same lighting setup and atmospheric conditions
        - Only vary camera angle slightly and character positioning - BACKGROUND STAYS IDENTICAL
        
        **TECHNICAL SPECIFICATIONS:**
        1. **Subject & Action**: Precise description using culturally accurate terminology
        2. **Character Consistency**: Use EVERY SINGLE detail from character description above
        3. **Background Consistency**: Use EVERY SINGLE detail from location description above
        4. **Composition**: Professional framing (rule of thirds, leading lines, depth)
        5. **Camera Work**: Specify shot type (close-up, medium, wide), lens choice (35mm, 85mm, etc.)
        6. **Lighting**: Maintain EXACT lighting from location group description
        7. **Quality Tags**: 4K, hyperrealistic, cinematic, highly detailed, professional photography
        8. **Style Direction**: Cinematographic style, color grading preferences
        9. **Atmosphere**: Maintain EXACT mood from location description
        
        **PROFESSIONAL STANDARDS:**
        - Authentic diversity and representation
        - No stereotypes or tokenism
        - Culturally respectful and accurate
        - Commercial-grade production quality
        - Advertisement-ready visual appeal
        - PERFECT character and location continuity
        
        **CRITICAL NEGATIVE ELEMENTS TO AVOID:**
        - Different clothing than specified in character profile
        - Different hair color or style than specified
        - Different background elements than specified in location description
        - Text overlays or watermarks
        - Multiple images or panels
        - Low quality or amateur appearance
        - Cultural stereotypes
        - Inappropriate content
        - ANY inconsistencies with character or background descriptions

        **ABSOLUTELY NO TEXT OF ANY SORT NO SUBTITLES NO TEXT OVERLAY(NO WORDS AT ALL)**
        
        ABSOLUTE REQUIREMENT: The character must appear EXACTLY as described (same clothes, same hair, same everything) and the background must be EXACTLY as described (same lighting, same furniture, same everything).
        
        Return ONLY the finished, professional image generation prompt with PERFECT consistency.
        No bullet points, labels, or explanations.
        The prompt should be a single, flowing paragraph optimized for AI image generation with EXACT character and background consistency.
        """

    def _generate_professional_kontext_instruction(self, scene: Dict) -> str:
        """
        Generate professional Kontext instruction for strategic product integration

        Args:
            scene: Scene dictionary

        Returns:
            Professional Kontext instruction for authentic product placement
        """
        try:
            product_name = self.brand_info.get("product_name", "product")
            brand_name = self.brand_info.get("brand_name", "brand")

            logger.info(f"🎯 Creating professional Kontext instruction for {product_name}")

            kontext_prompt = f"""
            You are a PROFESSIONAL PRODUCT PLACEMENT SPECIALIST with expertise in seamless brand integration.
            
            Create a precise FLUX Kontext instruction for natural, authentic product placement:
            
            **PRODUCT DETAILS:**
            Product: {product_name} from {brand_name}
            Visual Analysis: {self.brand_visual_analysis}
            
            **SCENE CONTEXT:**
            Description: {scene.get('description', '')}
            Visual Setting: {scene.get('visual', '')}
            Character Profile: {self.character_profile}
            
            **PROFESSIONAL PLACEMENT REQUIREMENTS:**
            
            1. **Authentic Integration**: Product must appear naturally in the scene
            2. **Character Interaction**: Specify how the character naturally interacts with/uses the product
            3. **Visual Preservation**: Maintain all brand colors, logos, and design elements
            4. **Realistic Physics**: Proper scale, lighting, shadows, and perspective
            5. **Professional Quality**: Commercial-grade product presentation
            6. **Brand Authenticity**: Product appears exactly as it would in real life
            
            **TECHNICAL SPECIFICATIONS:**
            - Exact placement position (be specific about location in frame)
            - Natural lighting that enhances product appearance
            - Proper scale relative to character and environment
            - Authentic product orientation and positioning
            - Professional product styling
            
            **EXAMPLES OF PROFESSIONAL PLACEMENT:**
            - "Naturally position the ceramic artisan coffee mug in the character's left hand, maintaining the rich earth-tone glazing and handcrafted texture visible on the mug's surface"
            - "Place the premium leather handbag on the marble café table to the character's right, preserving the bag's sophisticated black finish and gold hardware details"
            - "Integrate the smartphone into the scene by having the character hold it naturally while looking at the screen, ensuring the device's premium design and brand logo remain clearly visible"
            
            Create a specific, professional instruction that ensures authentic, high-quality product integration.
            Return ONLY the direct placement instruction.
            """

            result = self.llm_service.generate_content(kontext_prompt)
            if result["success"]:
                instruction = result["content"].strip()
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                instruction = f"Include {brand_name} product naturally in the scene"

            # Clean up formatting
            instruction = instruction.replace('"', "").replace("'", "").strip()

            logger.success(f"✅ Professional Kontext instruction created")
            logger.debug(f"Kontext instruction: {instruction[:100]}...")
            return instruction

        except Exception as e:
            logger.error(f"❌ Error generating professional Kontext instruction: {e}")
            return f"Professionally integrate the {self.brand_info.get('product_name', 'product')} into the scene naturally, maintaining authentic brand appearance and commercial-quality presentation"

    def generate_all_scene_prompts(self, scenes: List[Dict]) -> List[Dict]:
        """
        Generate professional image prompts for all scenes with strategic brand integration

        Args:
            scenes: List of scene dictionaries

        Returns:
            List of dictionaries with scene info and professional image prompts
        """
        logger.info(f"🎬 PROFESSIONAL IMAGE PROMPT GENERATION STARTED")
        logger.info(f"📊 Processing {len(scenes)} scenes")

        # Extract character profile for consistency
        if not self.character_profile:
            self.character_profile = self._extract_character_profile(scenes)

        # Extract location groups for background consistency
        if not self.location_groups:
            self.location_groups = self._extract_location_groups(scenes)

        # Log brand information
        if self.brand_info.get("brand_name"):
            logger.info(f"🏢 Brand: {self.brand_info['brand_name']}")
            logger.info(f"📦 Product: {self.brand_info.get('product_name')}")
            logger.info(f"🖼️ Brand image analysis: {len(self.brand_visual_analysis)} characters")
        else:
            logger.info("🎭 No brand integration - generating standard professional prompts")

        # Log location groups
        logger.info(f"🏢 Location groups: {len(self.location_groups)} consistent backgrounds")
        for group, desc in self.location_groups.items():
            logger.info(f"📍 {group}: {desc[:50]}...")

        scene_prompts = []
        brand_scene_count = 0

        for scene in scenes:
            # Professional brand inclusion analysis
            include_brand = self._should_include_brand(scene)

            if include_brand:
                brand_scene_count += 1

            # Determine location group for this scene
            scene_location_group = self._determine_location_group(scene, scene.get("visual", ""))

            logger.info(
                f"🎭 Scene {scene.get('number')}: Professional brand integration = {include_brand}"
            )
            logger.info(f"🏢 Scene {scene.get('number')}: Location group = {scene_location_group}")

            # Generate professional image prompt
            image_prompt = self.generate_scene_image_prompt(scene, include_brand)

            logger.debug(
                f"📝 Professional prompt for scene {scene.get('number')}: {image_prompt[:100]}..."
            )

            scene_prompts.append(
                {
                    "scene_number": scene["number"],
                    "scene_description": scene["description"],
                    "original_visual": scene["visual"],
                    "image_prompt": image_prompt,
                    "duration": scene["duration"],
                    "includes_brand": include_brand,
                    "brand_info": self.brand_info if include_brand else None,
                    "character_profile": self.character_profile,
                    "location_group": scene_location_group,
                    "location_background": self.location_groups.get(scene_location_group, ""),
                }
            )

        # Professional summary logging
        logger.success(f"✅ PROFESSIONAL PROMPT GENERATION COMPLETE")
        logger.info(f"🎯 Generated {len(scene_prompts)} professional image prompts")
        logger.info(
            f"🏷️ Strategic brand integration: {brand_scene_count}/{len(scene_prompts)} scenes"
        )
        logger.info(f"🎭 Character consistency: Applied to all {len(scene_prompts)} scenes")
        logger.info(f"🏢 Location consistency: {len(self.location_groups)} location groups")

        # Log character consistency details
        if self.character_profile:
            logger.info(f"👤 Character profile length: {len(self.character_profile)} characters")
            logger.info(
                f"🧥 Character profile includes clothing details: {'clothing' in self.character_profile.lower() or 'outfit' in self.character_profile.lower()}"
            )
            logger.info(
                f"💇 Character profile includes hair details: {'hair' in self.character_profile.lower()}"
            )

        if brand_scene_count == 0 and self.brand_info.get("brand_name"):
            logger.warning("⚠️ PROFESSIONAL ALERT: NO SCENES include brand integration!")
        elif brand_scene_count > 0:
            percentage = (brand_scene_count / len(scene_prompts)) * 100
            logger.info(f"📈 Brand coverage: {percentage:.1f}% (Professional standard: 30-70%)")

        # Log location group distribution
        location_distribution = {}
        for prompt in scene_prompts:
            loc_group = prompt.get("location_group", "Unknown")
            location_distribution[loc_group] = location_distribution.get(loc_group, 0) + 1

        for loc_group, count in location_distribution.items():
            logger.info(f"📍 {loc_group}: {count} scenes with consistent background")

        return scene_prompts

    def format_prompts_for_display(self, scene_prompts: List[Dict]) -> str:
        """
        Format professional scene prompts for UI display

        Args:
            scene_prompts: List of scene prompt dictionaries

        Returns:
            Professionally formatted string for display
        """
        formatted_output = []

        # Add header with professional summary
        header = f"🎬 PROFESSIONAL IMAGE PROMPTS GENERATED\n"
        header += f"📊 Total Scenes: {len(scene_prompts)}\n"
        brand_count = sum(1 for p in scene_prompts if p.get("includes_brand"))
        header += f"🏷️ Brand Integration: {brand_count}/{len(scene_prompts)} scenes\n"
        header += f"🎭 Character Consistency: Applied to all scenes\n"

        # Add location group summary
        location_groups = set(p.get("location_group", "Unknown") for p in scene_prompts)
        header += f"🏢 Location Groups: {len(location_groups)} consistent backgrounds\n"

        header += "=" * 100 + "\n\n"
        formatted_output.append(header)

        for prompt_data in scene_prompts:
            section = f"**🎬 Scene {prompt_data['scene_number']} - Professional Image Prompt**\n"
            section += f"*📝 Original Scene:* {prompt_data['scene_description']}\n"
            section += f"*⏱️ Duration:* {prompt_data['duration']}\n"
            section += f"*🏢 Location Group:* {prompt_data.get('location_group', 'Unknown')}\n"

            if prompt_data.get("includes_brand"):
                section += f"*🏷️ Brand Integration:* {self.brand_info.get('product_name')} professionally integrated\n"
            else:
                section += f"*🎭 Focus:* Character and narrative (no brand)\n"

            section += f"*👥 Character:* Consistent profile applied\n"
            section += f"*🏢 Background:* Consistent with location group\n"
            section += f"\n**🎨 Professional AI Image Prompt:**\n{prompt_data['image_prompt']}\n"
            section += "─" * 100 + "\n"
            formatted_output.append(section)

        return "\n".join(formatted_output)

    def enhance_prompt_with_style(self, base_prompt: str, style_preferences: Dict) -> str:
        """
        Enhance a base prompt with professional style preferences

        Args:
            base_prompt: The base image prompt
            style_preferences: Dictionary with style options

        Returns:
            Enhanced prompt with professional style modifications
        """
        try:
            logger.info("🎨 Enhancing prompt with professional style preferences")

            brand_context = ""
            if self.brand_info.get("brand_name"):
                brand_context = f"\nMaintain {self.brand_info['product_name']} brand authenticity with exact colors and design."

            enhancement_prompt = f"""
            You are a PROFESSIONAL PROMPT ENHANCEMENT SPECIALIST.
            
            Enhance this image generation prompt with professional style elements:
            
            **BASE PROMPT:** {base_prompt}
            
            **PROFESSIONAL STYLE PREFERENCES:**
            - Art Style: {style_preferences.get('art_style', 'photorealistic')}
            - Color Palette: {style_preferences.get('color_palette', 'professional')}
            - Mood: {style_preferences.get('mood', 'premium')}
            - Quality Standard: {style_preferences.get('quality', 'commercial-grade')}
            {brand_context}
            
            **ENHANCEMENT REQUIREMENTS:**
            - Maintain SINGLE IMAGE focus (no collages or panels)
            - Add professional cinematography elements
            - Include commercial-quality specifications
            - Preserve character consistency elements
            - Enhance visual appeal for professional use
            
            Return ONLY the enhanced, professional-grade prompt.
            """

            result = self.llm_service.generate_content(enhancement_prompt)
            if result["success"]:
                enhanced = result["content"].strip()
                logger.success("✅ Prompt professionally enhanced")
                return enhanced
            else:
                logger.error(f"AI service failed: {result.get('error')}")
                return base_prompt  # Return original prompt if enhancement fails

        except Exception as e:
            logger.error(f"❌ Error enhancing prompt professionally: {str(e)}")
            return base_prompt

    def clear_brand_info(self):
        """Clear brand information, reset character profile, and clear location groups"""
        logger.info("🧹 Clearing brand info, character profile, and location groups")
        self.brand_info = {}
        self.brand_visual_analysis = ""
        self.character_profile = ""
        self.location_groups = {}
        logger.info("✅ Professional image prompt generator reset")
