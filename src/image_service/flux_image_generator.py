"""
FLUX Image Generator
Handles image generation using FLUX API via fal-client
"""

# Global imports
import os
import time
import requests
import fal_client
from pathlib import Path
from loguru import logger
from dotenv import load_dotenv
from typing import Any, Dict, List, Optional, Tuple

# Local imports
from src.shared.config.settings import settings


class FluxImageGenerator:
    """Handles image generation using both FLUX Schnell and FLUX Kontext for brand integration"""

    def __init__(self):
        """Initialize the Flux generator with support for both models"""
        # Load environment variables from .env file if available
        load_dotenv()
        fal_key = os.getenv("FAL_KEY")
        if not fal_key:
            raise EnvironmentError("❌ FAL_KEY not found in environment variables.")
        os.environ["FAL_KEY"] = fal_key

        # API endpoints - Updated to use FLUX Schnell
        self.flux_pro_endpoint = "fal-ai/flux/schnell"  # Updated to new FLUX.1 endpoint
        self.kontext_endpoint = "fal-ai/flux-pro/kontext/max/multi"  # Keep kontext as is

        # Brand information storage
        self.brand_info = {}
        self.brand_image_url = None

        logger.info("FluxImageGenerator initialized with FLUX Schnell and FLUX Kontext support")

    def set_brand_info(self, brand_info: Dict[str, Any]):
        """
        Set brand information for image generation

        Args:
            brand_info: Dictionary containing brand details
        """
        self.brand_info = brand_info

        # Upload brand image if path provided
        if brand_info.get("brand_image_path"):
            self.brand_image_url = self._upload_brand_image(brand_info["brand_image_path"])
            logger.info(f"Brand image uploaded: {self.brand_image_url}")

    def _upload_brand_image(self, image_path: str) -> Optional[str]:
        """
        Upload brand image to FAL storage for use in Kontext

        Args:
            image_path: Local path to brand image

        Returns:
            URL of uploaded image or None if failed
        """
        try:
            logger.debug(f"Uploading brand image: {image_path}")
            url = fal_client.upload_file(image_path)
            logger.success(f"Brand image uploaded successfully: {url}")
            return url
        except Exception as e:
            logger.error(f"Failed to upload brand image: {e}")
            return None

    def generate_single_image(self, prompt: str, scene_data: Dict = None, **kwargs) -> Dict:
        """
        Generate a single image using either FLUX Schnell or Kontext based on brand presence

        Args:
            prompt: The image generation prompt
            scene_data: Optional scene data containing brand integration info
            **kwargs: Additional parameters

        Returns:
            Dictionary containing image info and metadata
        """
        # Check if this scene should use Kontext for brand integration
        use_kontext = (
            scene_data
            and scene_data.get("includes_brand", False)
            and self.brand_image_url
            and ("kontext_instruction" in prompt.lower() or scene_data.get("includes_brand"))
        )

        logger.info(
            f"Scene {scene_data.get('scene_number') if scene_data else 'N/A'}: use_kontext={use_kontext}, "
            f"brand_url_exists={bool(self.brand_image_url)}, "
            f"includes_brand={scene_data.get('includes_brand') if scene_data else False}"
        )

        if use_kontext:
            return self._generate_with_kontext(prompt, scene_data, **kwargs)
        else:
            return self._generate_with_flux_schnell(prompt, **kwargs)

    def _generate_with_kontext(self, prompt: str, scene_data: Dict, **kwargs) -> Dict:
        """
        Generate image using FLUX Kontext for brand integration - DIRECT APPROACH

        Args:
            prompt: Kontext instruction prompt
            scene_data: Scene data with brand info
            **kwargs: Additional parameters

        Returns:
            Generation result dictionary
        """
        try:
            logger.info(
                f"🔥 GENERATING WITH FLUX KONTEXT for scene {scene_data.get('scene_number')}"
            )

            # Clean the prompt - remove any markers
            if "kontext_instruction:" in prompt.lower():
                parts = prompt.split("kontext_instruction:", 1)
                kontext_instruction = parts[1].strip() if len(parts) > 1 else prompt
            else:
                kontext_instruction = prompt

            logger.info(f"Kontext instruction: {kontext_instruction[:100]}...")
            logger.info(f"Brand image URL: {self.brand_image_url}")

            # Create a simple base scene image URL for the first image in multi-image Kontext
            # We'll use a simple placeholder scene
            base_scene_description = self._extract_base_scene_prompt(scene_data)

            # Generate base scene first
            logger.info("Step 1: Generating base scene with FLUX Schnell...")
            base_result = self._generate_with_flux_schnell(base_scene_description, **kwargs)

            if not base_result.get("success") or not base_result.get("images"):
                logger.error("❌ Failed to generate base scene for Kontext")
                return base_result

            base_scene_url = base_result["images"][0]["url"]
            logger.info(f"✅ Base scene generated: {base_scene_url}")

            # Step 2: Use FLUX Kontext Multi to integrate brand
            logger.info("Step 2: Integrating brand with FLUX Kontext Multi...")

            kontext_params = {
                "prompt": kontext_instruction,
                "image_urls": [
                    base_scene_url,
                    self.brand_image_url,
                ],  # Base scene first, brand second
                "guidance_scale": kwargs.get("guidance_scale", 3.5),
                "num_images": 1,
                "safety_tolerance": kwargs.get("safety_tolerance", "2"),
                "aspect_ratio": self._map_aspect_ratio(kwargs.get("aspect_ratio", "16:9")),
            }

            # Handle seed if provided
            if "seed" in kwargs:
                kontext_params["seed"] = int(kwargs["seed"])

            logger.info(f"🚀 Calling FLUX Kontext endpoint: {self.kontext_endpoint}")
            logger.info(f"Parameters: {kontext_params}")

            def on_queue_update(update):
                if isinstance(update, fal_client.InProgress):
                    for log in update.logs:
                        logger.debug(f"Kontext Progress: {log['message']}")

            result = fal_client.subscribe(
                self.kontext_endpoint,
                arguments=kontext_params,
                with_logs=True,
                on_queue_update=on_queue_update,
            )

            # Process result
            processed_result = {
                "success": True,
                "images": result.get("images", []),
                "prompt": result.get("prompt", kontext_instruction),
                "seed": result.get("seed"),
                "timings": result.get("timings", {"inference": "N/A"}),
                "has_nsfw_concepts": result.get("has_nsfw_concepts", [False]),
                "generation_method": "flux_kontext",
                "brand_integrated": True,
            }

            logger.success(
                f"🎉 Successfully generated FLUX Kontext image for scene {scene_data.get('scene_number')}"
            )
            logger.info(f"Generated {len(processed_result.get('images', []))} images")

            return processed_result

        except Exception as e:
            logger.error(f"💥 Kontext generation failed: {str(e)}")
            logger.exception("Full Kontext error traceback:")
            # Fallback to standard generation
            logger.info("🔄 Falling back to standard FLUX Schnell generation")
            return self._generate_with_flux_schnell(
                self._extract_base_scene_prompt(scene_data), **kwargs
            )

    def _generate_with_flux_schnell(self, prompt: str, **kwargs) -> Dict:
        """
        Generate image using FLUX Schnell (updated from FLUX Pro)

        Args:
            prompt: Image generation prompt
            **kwargs: Additional parameters

        Returns:
            Generation result dictionary
        """
        try:
            # Default parameters for FLUX Schnell
            default_params = {
                "prompt": prompt,
                "image_size": "landscape_4_3",  # Changed from aspect_ratio
                "num_images": 1,
                "enable_safety_checker": True,
                "output_format": "png",  # Default to PNG for Schnell
                "num_inference_steps": 4,  # Schnell-specific parameter
                "acceleration": "regular",  # Schnell-specific parameter
            }

            # Handle aspect_ratio parameter by mapping to image_size
            if "aspect_ratio" in kwargs:
                aspect_ratio = kwargs.pop("aspect_ratio")
                default_params["image_size"] = self._map_aspect_ratio_to_image_size(aspect_ratio)

            # Map image_size parameter directly
            if "image_size" in kwargs:
                valid_sizes = [
                    "square_hd",
                    "square",
                    "portrait_4_3",
                    "portrait_16_9",
                    "landscape_4_3",
                    "landscape_16_9",
                ]
                if kwargs["image_size"] in valid_sizes:
                    default_params["image_size"] = kwargs.pop("image_size")

            # Accept num_images if provided
            if "num_images" in kwargs:
                default_params["num_images"] = min(int(kwargs.pop("num_images")), 4)

            # Accept seed for reproducibility
            if "seed" in kwargs:
                default_params["seed"] = int(kwargs.pop("seed"))

            # Handle num_inference_steps
            if "num_inference_steps" in kwargs:
                steps = int(kwargs.pop("num_inference_steps"))
                if 1 <= steps <= 50:  # Reasonable range for Schnell
                    default_params["num_inference_steps"] = steps

            # Handle output_format
            if "output_format" in kwargs:
                format_val = kwargs.pop("output_format")
                if format_val in ["jpeg", "png"]:
                    default_params["output_format"] = format_val

            # Handle enable_safety_checker
            if "enable_safety_checker" in kwargs:
                default_params["enable_safety_checker"] = bool(kwargs.pop("enable_safety_checker"))

            # Handle acceleration
            if "acceleration" in kwargs:
                acceleration = kwargs.pop("acceleration")
                if acceleration in ["none", "regular", "high"]:
                    default_params["acceleration"] = acceleration

            # Handle sync_mode
            if "sync_mode" in kwargs:
                default_params["sync_mode"] = bool(kwargs.pop("sync_mode"))

            # Remove parameters not supported by Schnell
            unsupported_params = [
                "safety_tolerance",
                "raw",
                "negative_prompt",
                "guidance_scale",
            ]
            for param in unsupported_params:
                if param in kwargs:
                    if param == "safety_tolerance":
                        logger.warning(
                            f"{param} not supported in FLUX Schnell, using enable_safety_checker instead"
                        )
                    else:
                        logger.warning(f"{param} not supported in FLUX Schnell, ignoring")
                    kwargs.pop(param)

            def on_queue_update(update):
                if isinstance(update, fal_client.InProgress):
                    for log in update.logs:
                        logger.debug(f"FLUX Schnell Progress: {log['message']}")

            result = fal_client.subscribe(
                self.flux_pro_endpoint,  # This now points to schnell endpoint
                arguments=default_params,
                with_logs=True,
                on_queue_update=on_queue_update,
            )

            return {
                "success": True,
                "images": result.get("images", []),
                "prompt": result.get("prompt", default_params["prompt"]),
                "seed": result.get("seed"),
                "timings": result.get("timings", {"inference": "N/A"}),
                "has_nsfw_concepts": result.get(
                    "has_nsfw_concepts", [False] * len(result.get("images", []))
                ),
                "generation_method": "flux_schnell",  # Updated method name
            }

        except Exception as e:
            return {"success": False, "error": str(e), "prompt": prompt, "images": []}

    def _map_aspect_ratio_to_image_size(self, aspect_ratio: str) -> str:
        """
        Map aspect ratio to FLUX Schnell image_size format

        Args:
            aspect_ratio: Aspect ratio string (e.g., "16:9")

        Returns:
            Image size string for Schnell
        """
        # Mapping from aspect ratios to Schnell image_size values
        aspect_map = {
            "1:1": "square",
            "4:3": "landscape_4_3",
            "16:9": "landscape_16_9",
            "3:4": "portrait_4_3",
            "9:16": "portrait_16_9",
            "21:9": "landscape_16_9",  # Fallback to closest
            "9:21": "portrait_16_9",  # Fallback to closest
        }
        return aspect_map.get(aspect_ratio, "landscape_4_3")

    def _extract_base_scene_prompt(self, scene_data: Dict) -> str:
        """
        Extract base scene prompt without brand elements

        Args:
            scene_data: Scene data dictionary

        Returns:
            Base scene prompt
        """
        # Get the original visual description without brand mentions
        visual = scene_data.get("original_visual", "")

        # Remove brand/product mentions
        if self.brand_info.get("product_name"):
            visual = visual.replace(self.brand_info["product_name"], "")
        if self.brand_info.get("brand_name"):
            visual = visual.replace(self.brand_info["brand_name"], "")

        # Create a clean scene prompt
        return f"{visual}, cinematic composition, high quality, photorealistic, 4K"

    def _map_aspect_ratio(self, ratio: str) -> str:
        """Map aspect ratio to Kontext format"""
        # Kontext uses same format as input, just ensure it's valid
        valid_ratios = [
            "21:9",
            "16:9",
            "4:3",
            "3:2",
            "1:1",
            "2:3",
            "3:4",
            "9:16",
            "9:21",
        ]
        return ratio if ratio in valid_ratios else "16:9"

    def generate_scene_images(self, scene_prompts: List[Dict]) -> List[Dict]:
        """
        Generate images for all scenes with smart brand integration

        Args:
            scene_prompts: List of scene prompt dictionaries

        Returns:
            List of generation results
        """
        results = []

        for i, scene_data in enumerate(scene_prompts):
            logger.info(f"🎬 Generating image for Scene {scene_data.get('scene_number', i+1)}...")

            image_prompt = scene_data.get("image_prompt", "")
            if not image_prompt:
                results.append(
                    {
                        "scene_number": scene_data.get("scene_number", i + 1),
                        "success": False,
                        "error": "No image prompt available",
                        "images": [],
                    }
                )
                continue

            # More flexible Kontext detection
            is_kontext = "kontext_instruction:" in image_prompt.lower() or scene_data.get(
                "includes_brand", False
            )

            # Additional check for brand availability
            can_use_kontext = is_kontext and bool(self.brand_image_url)

            logger.info(
                f"🔍 Scene {scene_data.get('scene_number')}: "
                f"is_kontext={is_kontext}, "
                f"can_use_kontext={can_use_kontext}, "
                f"brand_url_exists={bool(self.brand_image_url)}, "
                f"includes_brand={scene_data.get('includes_brand')}"
            )

            if can_use_kontext:
                logger.info(f"🎯 Using FLUX KONTEXT for scene {scene_data.get('scene_number')}")

                # Extract the actual instruction
                if "kontext_instruction:" in image_prompt.lower():
                    parts = image_prompt.split("kontext_instruction:", 1)
                    kontext_instruction = parts[1].strip() if len(parts) > 1 else image_prompt
                else:
                    # If includes_brand is True but no explicit instruction, create one
                    product_name = self.brand_info.get("product_name", "product")
                    kontext_instruction = f"Naturally place the {product_name} in this scene: {scene_data.get('scene_description', '')} maintaining its authentic appearance and colors"

                logger.info(f"📝 Kontext instruction: {kontext_instruction[:100]}...")

                result = self.generate_single_image(
                    prompt=kontext_instruction,
                    scene_data=scene_data,
                    aspect_ratio=scene_data.get("aspect_ratio", "16:9"),
                )
            else:
                # Standard generation
                logger.info(
                    f"🖼️ Using standard FLUX Schnell for scene {scene_data.get('scene_number')}"
                )
                result = self.generate_single_image(
                    prompt=image_prompt,
                    aspect_ratio=scene_data.get("aspect_ratio", "16:9"),
                )

            # Add scene metadata to result
            result.update(
                {
                    "scene_number": scene_data.get("scene_number", i + 1),
                    "scene_description": scene_data.get("scene_description", ""),
                    "original_visual": scene_data.get("original_visual", ""),
                    "duration": scene_data.get("duration", ""),
                    "includes_brand": scene_data.get("includes_brand", False),
                }
            )

            results.append(result)

            # Small delay between requests
            if i < len(scene_prompts) - 1:
                time.sleep(1)

        return results

    def test_kontext_with_debug(self, test_instruction: str = None) -> str:
        """
        Test FLUX Kontext generation with detailed debugging

        Args:
            test_instruction: Optional custom instruction to test

        Returns:
            Detailed test result string
        """
        if not self.brand_image_url:
            return "❌ No brand image uploaded. Upload brand image first in Step 1."

        try:
            logger.info("🧪 Starting FLUX Kontext debug test...")

            # Step 1: Test basic FLUX Schnell generation
            logger.info("Step 1: Testing basic FLUX Schnell generation...")
            base_prompt = (
                "A modern living room with a wooden coffee table, warm lighting, minimalist decor"
            )
            base_result = self._generate_with_flux_schnell(base_prompt, aspect_ratio="16:9")

            if not base_result.get("success"):
                return f"❌ Base FLUX Schnell test failed: {base_result.get('error')}"

            base_image_url = base_result["images"][0]["url"]
            logger.info(f"✅ Base scene generated: {base_image_url}")

            # Step 2: Test FLUX Kontext
            logger.info("Step 2: Testing FLUX Kontext integration...")

            # Use provided instruction or create default
            kontext_instruction = (
                test_instruction
                or f"Place the {self.brand_info.get('product_name', 'product')} elegantly on the wooden coffee table in the center, maintaining its authentic colors and design"
            )

            kontext_params = {
                "prompt": kontext_instruction,
                "image_urls": [base_image_url, self.brand_image_url],
                "guidance_scale": 3.5,
                "num_images": 1,
                "aspect_ratio": "16:9",
            }

            logger.info(f"🎯 Kontext endpoint: {self.kontext_endpoint}")
            logger.info(f"📝 Instruction: {kontext_instruction}")
            logger.info(f"🖼️ Base image: {base_image_url}")
            logger.info(f"🏷️ Brand image: {self.brand_image_url}")

            result = fal_client.subscribe(
                self.kontext_endpoint, arguments=kontext_params, with_logs=True
            )

            if result and result.get("images"):
                kontext_image_url = result["images"][0]["url"]
                return f"""✅ FLUX Kontext test successful!

🎯 Endpoint: {self.kontext_endpoint}
📝 Instruction: {kontext_instruction}
🖼️ Base scene: {base_image_url}
🏷️ Brand image: {self.brand_image_url}
🎨 Result image: {kontext_image_url}

✅ Brand integration working correctly!"""
            else:
                return f"❌ Kontext generation failed - no images returned. Result: {result}"

        except Exception as e:
            error_msg = f"❌ Kontext test failed: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full test error:")
            return error_msg

    def generate_batch_images(self, prompts: List[str], batch_size: int = 3) -> List[Dict]:
        """Generate images in batches"""
        results = []
        for i in range(0, len(prompts), batch_size):
            batch = prompts[i : i + batch_size]
            batch_results = []
            for j, p in enumerate(batch):
                print(f"Generating image {i+j+1}/{len(prompts)}...")
                res = self.generate_single_image(prompt=p)
                batch_results.append(res)
                if j < len(batch) - 1:
                    time.sleep(1)
            results.extend(batch_results)
            if i + batch_size < len(prompts):
                print(f"Completed batch {i//batch_size + 1}, waiting before next batch...")
                time.sleep(3)
        return results

    def save_images_locally(self, image_results: List[Dict], output_dir: str = None) -> List[Dict]:
        """Save generated images locally"""
        if output_dir is None:
            current_file = os.path.abspath(__file__)
            root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
            output_dir = os.path.join(root, "generated_images")

        os.makedirs(output_dir, exist_ok=True)
        updated = []

        for r in image_results:
            if not r.get("success", False):
                updated.append(r)
                continue

            local_paths = []
            for idx, img in enumerate(r.get("images", [])):
                url = img.get("url")
                if not url:
                    continue

                # Use original content type for filename extension
                content_type = img.get("content_type", "image/png")  # Default to PNG for Schnell
                ext = "png" if "png" in content_type else "jpg"

                # Add scene number and brand indicator to filename
                scene_num = r.get("scene_number", idx + 1)
                brand_suffix = "_branded" if r.get("includes_brand") else ""
                fname = f"scene_{scene_num}_image_{idx+1}{brand_suffix}.{ext}"

                path = os.path.join(output_dir, fname)
                try:
                    resp = requests.get(url, timeout=30)
                    resp.raise_for_status()
                    with open(path, "wb") as f:
                        f.write(resp.content)
                    local_paths.append(path)
                    logger.success(f"Saved image: {path}")
                except Exception as err:
                    logger.error(f"Error saving image: {err}")

            r["local_paths"] = local_paths
            updated.append(r)

        return updated

    def get_generation_summary(self, results: List[Dict]) -> Dict:
        """Get summary of generation results"""
        total = len(results)
        success = sum(1 for r in results if r.get("success"))
        failed = total - success
        images = sum(len(r.get("images", [])) for r in results if r.get("success"))

        # Count brand integrations
        brand_scenes = sum(1 for r in results if r.get("includes_brand"))
        kontext_used = sum(1 for r in results if r.get("generation_method") == "flux_kontext")
        flux_schnell_used = sum(1 for r in results if r.get("generation_method") == "flux_schnell")

        errors = [r.get("error") for r in results if not r.get("success")]

        return {
            "total_scenes": total,
            "successful_generations": success,
            "failed_generations": failed,
            "total_images_generated": images,
            "brand_integrated_scenes": brand_scenes,
            "kontext_generations": kontext_used,
            "flux_schnell_generations": flux_schnell_used,  # Updated name
            "success_rate": f"{(success/total*100):.1f}%" if total else "0%",
            "errors": errors,
        }

    def format_results_for_display(self, results: List[Dict]) -> str:
        """Format generation results for display"""
        if not results:
            return "No image generation results available."

        out = ["=" * 80, "🎨 IMAGE GENERATION SUMMARY", "=" * 80]
        summary = self.get_generation_summary(results)

        out += [
            f"Total Scenes: {summary['total_scenes']}",
            f"Successful: {summary['successful_generations']}",
            f"Failed: {summary['failed_generations']}",
            f"Success Rate: {summary['success_rate']}",
            f"Total Images: {summary['total_images_generated']}",
            f"Brand Integrated Scenes: {summary['brand_integrated_scenes']}",
            f"FLUX Kontext Used: {summary['kontext_generations']} times",
            f"FLUX Schnell Used: {summary['flux_schnell_generations']} times\n",  # Updated name
        ]

        for r in results:
            scene = r.get("scene_number", "Unknown")
            out.append(f"🎬 SCENE {scene}")
            out.append("-" * 40)

            if r.get("success"):
                imgs = r.get("images", [])
                out += [
                    "✅ Generation: SUCCESS",
                    f"📷 Images Generated: {len(imgs)}",
                    f"🔧 Method: {r.get('generation_method', 'unknown')}",
                ]

                if r.get("includes_brand"):
                    out.append(f"🏷️ Brand Integration: {self.brand_info.get('product_name', 'Yes')}")

                for i, img in enumerate(imgs):
                    out.append(f"   Image {i+1}: {img.get('url', 'No URL')}")

                if r.get("local_paths"):
                    out.append(f"💾 Local Files: {', '.join(r['local_paths'])}")

                out.append(f"🎲 Seed: {r.get('seed', 'Unknown')}")

                # Show NSFW detection results
                nsfw_concepts = r.get("has_nsfw_concepts", [])
                if nsfw_concepts and any(nsfw_concepts):
                    out.append("⚠️  NSFW content detected")
            else:
                out += [
                    "❌ Generation: FAILED",
                    f"🚫 Error: {r.get('error', 'Unknown error')}",
                ]

            out.append(f"📝 Scene: {r.get('scene_description', 'No description')}\n")

        return "\n".join(out)

    def test_api_connection(self) -> Tuple[bool, str]:
        """Test API connection with both FLUX Schnell and Kontext"""
        try:
            # Test FLUX Schnell
            test_flux_schnell = self.generate_single_image(
                "A simple test image of a red apple on a white background",
                image_size="square",
                num_images=1,
            )

            if not test_flux_schnell.get("success"):
                return (
                    False,
                    f"❌ FLUX Schnell test failed: {test_flux_schnell.get('error', 'Unknown error')}",
                )

            # Test Kontext if brand image is available
            if self.brand_image_url:
                # Upload a test image for Kontext
                test_kontext_params = {
                    "prompt": "Place the object in the center of a white background",
                    "image_urls": [
                        test_flux_schnell["images"][0]["url"],
                        self.brand_image_url,
                    ],
                    "guidance_scale": 3.5,
                    "num_images": 1,
                }

                try:
                    result = fal_client.subscribe(
                        self.kontext_endpoint, arguments=test_kontext_params
                    )
                    return (
                        True,
                        "✅ Both FLUX Schnell and FLUX Kontext APIs are working!",
                    )
                except:
                    return (
                        True,
                        "✅ FLUX Schnell working! ⚠️ FLUX Kontext test skipped (requires brand image)",
                    )
            else:
                return (
                    True,
                    "✅ FLUX Schnell API connection successful! (Kontext requires brand image)",
                )

        except Exception as e:
            return False, f"❌ API connection failed: {e}"

    def clear_brand_info(self):
        """Clear brand information"""
        self.brand_info = {}
        self.brand_image_url = None
        logger.info("Brand info cleared from image generator")

    # Alias methods for backward compatibility
    def _generate_with_flux_pro(self, prompt: str, **kwargs) -> Dict:
        """Alias for backward compatibility - now uses Schnell"""
        return self._generate_with_flux_schnell(prompt, **kwargs)
