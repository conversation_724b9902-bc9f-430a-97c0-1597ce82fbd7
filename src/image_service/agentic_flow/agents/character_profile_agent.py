"""
Character Profile Agent - Extracts character profiles for consistency
"""

from typing import Dict, List, Any
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_google_genai import ChatGoogleGenerativeAI
from loguru import logger

from src.shared.config.settings import settings
from ..state import AgenticImageState
from ..prompts import get_character_profile_prompt


class CharacterProfileAgent:
    """Agent responsible for extracting character profiles for consistency"""

    def __init__(self):
        self.llm_service = ChatGoogleGenerativeAI(
            model=settings.gemini_model_name,
            google_api_key=settings.gemini_api_key,
            temperature=0.3,
        )

        # Get prompt from centralized prompt manager
        prompt_template = get_character_profile_prompt()
        self.prompt = ChatPromptTemplate.from_template(prompt_template)

        self.parser = StrOutputParser()
        self.chain = self.prompt | self.llm_service | self.parser

    def extract_character_profile(self, state: AgenticImageState) -> str:
        """Extract character profile from scenes"""
        try:
            scenes_text = "\n".join(
                [
                    f"Scene {scene.get('number', i+1)}: {scene.get('description', '')}"
                    for i, scene in enumerate(state["scenes"])
                ]
            )

            profile = self.chain.invoke({"scenes_text": scenes_text})
            return profile.strip()

        except Exception as e:
            logger.error(f"Error in CharacterProfileAgent: {e}")
            return "Professional adult character with authentic, approachable appearance"
