"""
Flow Decider Agent - Decides image types and parameters for scenes
"""

from typing import Dict, List, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from loguru import logger

from src.shared.config.settings import settings
from ..state import (
    AgenticImageState,
    ImageType,
    StylePreset,
    SceneImagePlan,
    get_type_balance_score,
)
from ..prompts import get_flow_decider_prompt


class FlowDeciderAgent:
    """Agent responsible for deciding image types and parameters for scenes"""

    def __init__(self):
        self.llm_service = ChatGoogleGenerativeAI(
            model=settings.gemini_model_name,
            google_api_key=settings.gemini_api_key,
            temperature=0.7,
        )

        # Get prompt from centralized prompt manager
        prompt_template = get_flow_decider_prompt()
        self.prompt = ChatPromptTemplate.from_template(prompt_template)

        self.parser = JsonOutputParser()
        self.chain = self.prompt | self.llm_service | self.parser

    def decide_scene_plan(self, state: AgenticImageState, scene_index: int) -> SceneImagePlan:
        """Decide the plan for a specific scene"""
        try:
            scene = state["scenes"][scene_index]

            # Calculate balance scores for each type
            balance_scores = {
                image_type.value: get_type_balance_score(state, image_type)
                for image_type in ImageType
            }

            # Prepare brand context
            brand_context = "No brand information provided"
            if state["brand_info"]:
                brand = state["brand_info"]
                brand_context = f"Brand: {brand.name}\nDescription: {brand.description}\nStyle: {brand.visual_style}"

            # Prepare type distribution
            total_processed = sum(state["current_type_distribution"].values())
            type_dist_str = "\n".join(
                [
                    f"- {k.title()}: {v}/{total_processed} ({v/max(total_processed, 1)*100:.1f}%)"
                    for k, v in state["current_type_distribution"].items()
                ]
            )

            balance_scores_str = "\n".join(
                [f"- {k.title()}: {v:.2f}" for k, v in balance_scores.items()]
            )

            result = self.chain.invoke(
                {
                    "scene_number": scene.get("number", scene_index + 1),
                    "scene_description": scene.get("description", ""),
                    "visual_description": scene.get("visual", ""),
                    "duration": scene.get("duration", ""),
                    "brand_context": brand_context,
                    "type_distribution": type_dist_str,
                    "balance_scores": balance_scores_str,
                }
            )

            return SceneImagePlan(
                scene_number=scene.get("number", scene_index + 1),
                image_type=ImageType(result["image_type"]),
                aspect_ratio=result["aspect_ratio"],
                style_preset=StylePreset(result["style_preset"]),
                include_brand=result["include_brand"],
                weight_score=result["weight_score"],
                rationale=result["rationale"],
            )

        except Exception as e:
            logger.error(f"Error in FlowDeciderAgent: {e}")
            # Return default plan
            return SceneImagePlan(
                scene_number=scene.get("number", scene_index + 1),
                image_type=ImageType.CHARACTER,
                aspect_ratio="16:9",
                style_preset=StylePreset.CINEMATIC,
                include_brand=False,
                weight_score=0.5,
                rationale=f"Default plan due to error: {str(e)}",
            )
