"""
Image Prompt Agent - Generates specific image prompts
"""

from typing import Dict, List, Any
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from loguru import logger

from src.shared.config.settings import settings
from ..state import AgenticImageState, SceneImagePlan
from ..prompts import get_image_prompt_template


class ImagePromptAgent:
    """Agent responsible for generating specific image prompts"""

    def __init__(self):
        self.llm_service = ChatGoogleGenerativeAI(
            model=settings.gemini_model_name,
            google_api_key=settings.gemini_api_key,
            temperature=0.8,
        )

        # Get prompt from centralized prompt manager
        prompt_template = get_image_prompt_template()
        self.prompt = ChatPromptTemplate.from_template(prompt_template)

        self.parser = StrOutputParser()
        self.chain = self.prompt | self.llm_service | self.parser

    def generate_prompt(
        self, state: AgenticImageState, scene_index: int, plan: SceneImagePlan
    ) -> str:
        """Generate image prompt for a scene based on the plan"""
        try:
            scene = state["scenes"][scene_index]

            # Prepare brand info
            brand_info = "No brand integration required"
            if plan.include_brand and state["brand_info"]:
                brand = state["brand_info"]
                brand_info = f"Brand: {brand.name}\nDescription: {brand.description}\nColors: {', '.join(brand.colors)}"

            prompt = self.chain.invoke(
                {
                    "scene_number": plan.scene_number,
                    "scene_description": scene.get("description", ""),
                    "visual_description": scene.get("visual", ""),
                    "image_type": plan.image_type.value,
                    "style_preset": plan.style_preset.value,
                    "aspect_ratio": plan.aspect_ratio,
                    "include_brand": plan.include_brand,
                    "character_profile": state["character_profile"],
                    "brand_info": brand_info,
                }
            )

            return prompt.strip()

        except Exception as e:
            logger.error(f"Error in ImagePromptAgent: {e}")
            return (
                f"Professional {plan.image_type.value} scene: {scene.get('description', 'scene')}"
            )
