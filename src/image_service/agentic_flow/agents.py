"""
LangChain agents for the agentic image flow
"""

from typing import Dict, List, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerativeAI
from loguru import logger
import json

from src.shared.config.settings import settings
from .state import AgenticImageState, ImageType, StylePreset, SceneImagePlan, get_type_balance_score


class FlowDeciderAgent:
    """Agent responsible for deciding image types and parameters for scenes"""

    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model=settings.gemini_model_name,
            google_api_key=settings.gemini_api_key,
            temperature=0.7,
        )

        self.prompt = ChatPromptTemplate.from_template(
            """
You are an expert image flow designer for video content. Your job is to decide the optimal image type and parameters for each scene to create a balanced, engaging visual narrative.

SCENE INFORMATION:
Scene {scene_number}: {scene_description}
Visual Description: {visual_description}
Duration: {duration}

BRAND CONTEXT:
{brand_context}

CURRENT TYPE DISTRIBUTION:
{type_distribution}

TARGET WEIGHTS:
- Object: 30% (product/brand focus)
- Character: 40% (human-centered scenes)
- Object + Character: 25% (balanced scenes)
- Montage: 5% (artistic/transition scenes)

BALANCE SCORES:
{balance_scores}

GUIDELINES:
1. First 2 scenes should establish character/context
2. Middle scenes can vary based on content
3. Last scenes should focus on product/brand
4. Consider scene content and narrative flow
5. Maintain overall balance across all scenes

Choose the best image type and parameters for this scene:

RESPOND WITH VALID JSON:
{{
    "image_type": "object|character|object_character|montage",
    "aspect_ratio": "16:9|9:16|1:1",
    "style_preset": "cinematic|commercial|lifestyle|product_focus|artistic",
    "include_brand": true|false,
    "weight_score": 0.0-1.0,
    "rationale": "Brief explanation of choice"
}}
"""
        )

        self.parser = JsonOutputParser()
        self.chain = self.prompt | self.llm | self.parser

    def decide_scene_plan(self, state: AgenticImageState, scene_index: int) -> SceneImagePlan:
        """Decide the plan for a specific scene"""
        try:
            scene = state["scenes"][scene_index]

            # Calculate balance scores for each type
            balance_scores = {
                image_type.value: get_type_balance_score(state, image_type)
                for image_type in ImageType
            }

            # Prepare brand context
            brand_context = "No brand information provided"
            if state["brand_info"]:
                brand = state["brand_info"]
                brand_context = f"Brand: {brand.name}\nDescription: {brand.description}\nStyle: {brand.visual_style}"

            # Prepare type distribution
            total_processed = sum(state["current_type_distribution"].values())
            type_dist_str = "\n".join(
                [
                    f"- {k.title()}: {v}/{total_processed} ({v/max(total_processed, 1)*100:.1f}%)"
                    for k, v in state["current_type_distribution"].items()
                ]
            )

            balance_scores_str = "\n".join(
                [f"- {k.title()}: {v:.2f}" for k, v in balance_scores.items()]
            )

            result = self.chain.invoke(
                {
                    "scene_number": scene.get("number", scene_index + 1),
                    "scene_description": scene.get("description", ""),
                    "visual_description": scene.get("visual", ""),
                    "duration": scene.get("duration", ""),
                    "brand_context": brand_context,
                    "type_distribution": type_dist_str,
                    "balance_scores": balance_scores_str,
                }
            )

            return SceneImagePlan(
                scene_number=scene.get("number", scene_index + 1),
                image_type=ImageType(result["image_type"]),
                aspect_ratio=result["aspect_ratio"],
                style_preset=StylePreset(result["style_preset"]),
                include_brand=result["include_brand"],
                weight_score=result["weight_score"],
                rationale=result["rationale"],
            )

        except Exception as e:
            logger.error(f"Error in FlowDeciderAgent: {e}")
            # Return default plan
            return SceneImagePlan(
                scene_number=scene.get("number", scene_index + 1),
                image_type=ImageType.CHARACTER,
                aspect_ratio="16:9",
                style_preset=StylePreset.CINEMATIC,
                include_brand=False,
                weight_score=0.5,
                rationale=f"Default plan due to error: {str(e)}",
            )


class CharacterProfileAgent:
    """Agent responsible for extracting character profiles for consistency"""

    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model=settings.gemini_model_name,
            google_api_key=settings.gemini_api_key,
            temperature=0.3,
        )

        self.prompt = ChatPromptTemplate.from_template(
            """
You are an expert character designer. Analyze the following scenes and create a consistent character profile that will ensure the same person appears across all character-focused images.

SCENES:
{scenes_text}

Extract and define:
1. Physical appearance (age, gender, ethnicity, build)
2. Clothing style and colors
3. Key distinguishing features
4. Personality traits that affect appearance
5. Professional context (if applicable)

Create a detailed character profile that will ensure visual consistency across all scenes.

RESPOND WITH A DETAILED CHARACTER DESCRIPTION:
"""
        )

        self.parser = StrOutputParser()
        self.chain = self.prompt | self.llm | self.parser

    def extract_character_profile(self, state: AgenticImageState) -> str:
        """Extract character profile from scenes"""
        try:
            scenes_text = "\n".join(
                [
                    f"Scene {scene.get('number', i+1)}: {scene.get('description', '')}"
                    for i, scene in enumerate(state["scenes"])
                ]
            )

            profile = self.chain.invoke({"scenes_text": scenes_text})
            return profile.strip()

        except Exception as e:
            logger.error(f"Error in CharacterProfileAgent: {e}")
            return "Professional adult character with authentic, approachable appearance"


class ImagePromptAgent:
    """Agent responsible for generating specific image prompts"""

    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model=settings.gemini_model_name,
            google_api_key=settings.gemini_api_key,
            temperature=0.8,
        )

        self.prompt = ChatPromptTemplate.from_template(
            """
You are an expert image prompt engineer. Create a detailed, professional image generation prompt based on the scene and plan.

SCENE INFORMATION:
Scene {scene_number}: {scene_description}
Visual Description: {visual_description}

IMAGE PLAN:
Type: {image_type}
Style: {style_preset}
Aspect Ratio: {aspect_ratio}
Include Brand: {include_brand}

CHARACTER PROFILE:
{character_profile}

BRAND INFORMATION:
{brand_info}

GUIDELINES:
1. Create vivid, specific descriptions
2. Include lighting, composition, and mood
3. Ensure character consistency if applicable
4. Integrate brand naturally if required
5. Match the specified style and type
6. Optimize for {aspect_ratio} composition

Generate a professional image prompt (150-200 words) that will produce high-quality, commercially viable images.

RESPOND WITH THE IMAGE PROMPT:
"""
        )

        self.parser = StrOutputParser()
        self.chain = self.prompt | self.llm | self.parser

    def generate_prompt(
        self, state: AgenticImageState, scene_index: int, plan: SceneImagePlan
    ) -> str:
        """Generate image prompt for a scene based on the plan"""
        try:
            scene = state["scenes"][scene_index]

            # Prepare brand info
            brand_info = "No brand integration required"
            if plan.include_brand and state["brand_info"]:
                brand = state["brand_info"]
                brand_info = f"Brand: {brand.name}\nDescription: {brand.description}\nColors: {', '.join(brand.colors)}"

            prompt = self.chain.invoke(
                {
                    "scene_number": plan.scene_number,
                    "scene_description": scene.get("description", ""),
                    "visual_description": scene.get("visual", ""),
                    "image_type": plan.image_type.value,
                    "style_preset": plan.style_preset.value,
                    "aspect_ratio": plan.aspect_ratio,
                    "include_brand": plan.include_brand,
                    "character_profile": state["character_profile"],
                    "brand_info": brand_info,
                }
            )

            return prompt.strip()

        except Exception as e:
            logger.error(f"Error in ImagePromptAgent: {e}")
            return (
                f"Professional {plan.image_type.value} scene: {scene.get('description', 'scene')}"
            )
