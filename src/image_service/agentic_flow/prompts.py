"""
Prompts for the agentic image flow system
Centralized prompt management with versioning support
"""

from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class PromptVersion:
    """Represents a versioned prompt"""

    version: str
    prompt: str
    description: str


class PromptManager:
    """Manages prompts with versioning and fallback support"""

    def __init__(self):
        self.prompts = self._initialize_prompts()
        self.current_version = "v1.0"

    def _initialize_prompts(self) -> Dict[str, Dict[str, PromptVersion]]:
        """Initialize all prompts with versions"""
        return {
            "flow_decider": {
                "v1.0": PromptVersion(
                    version="v1.0",
                    description="Flow decider prompt for image type and parameter selection",
                    prompt="""You are an expert image flow designer for video content. Your job is to decide the optimal image type and parameters for each scene to create a balanced, engaging visual narrative.

SCENE INFORMATION:
Scene {scene_number}: {scene_description}
Visual Description: {visual_description}
Duration: {duration}

BRAND CONTEXT:
{brand_context}

CURRENT TYPE DISTRIBUTION:
{type_distribution}

TARGET WEIGHTS:
- Object: 30% (product/brand focus)
- Character: 40% (human-centered scenes)
- Object + Character: 25% (balanced scenes)
- Montage: 5% (artistic/transition scenes)

BALANCE SCORES:
{balance_scores}

GUIDELINES:
1. First 2 scenes should establish character/context
2. Middle scenes can vary based on content
3. Last scenes should focus on product/brand
4. Consider scene content and narrative flow
5. Maintain overall balance across all scenes

Choose the best image type and parameters for this scene:

RESPOND WITH VALID JSON:
{{
    "image_type": "object|character|object_character|montage",
    "aspect_ratio": "16:9|9:16|1:1",
    "style_preset": "cinematic|commercial|lifestyle|product_focus|artistic",
    "include_brand": true|false,
    "weight_score": 0.0-1.0,
    "rationale": "Brief explanation of choice"
}}""",
                )
            },
            "character_profile": {
                "v1.0": PromptVersion(
                    version="v1.0",
                    description="Character profile extraction for consistency",
                    prompt="""You are an expert character designer. Analyze the following scenes and create a consistent character profile that will ensure the same person appears across all character-focused images.

SCENES:
{scenes_text}

Extract and define:
1. Physical appearance (age, gender, ethnicity, build)
2. Clothing style and colors
3. Key distinguishing features
4. Personality traits that affect appearance
5. Professional context (if applicable)

Create a detailed character profile that will ensure visual consistency across all scenes.

RESPOND WITH A DETAILED CHARACTER DESCRIPTION:""",
                )
            },
            "image_prompt": {
                "v1.0": PromptVersion(
                    version="v1.0",
                    description="Image prompt generation for specific scenes",
                    prompt="""You are an expert image prompt engineer. Create a detailed, professional image generation prompt based on the scene and plan.

SCENE INFORMATION:
Scene {scene_number}: {scene_description}
Visual Description: {visual_description}

IMAGE PLAN:
Type: {image_type}
Style: {style_preset}
Aspect Ratio: {aspect_ratio}
Include Brand: {include_brand}

CHARACTER PROFILE:
{character_profile}

BRAND INFORMATION:
{brand_info}

GUIDELINES:
1. Create vivid, specific descriptions
2. Include lighting, composition, and mood
3. Ensure character consistency if applicable
4. Integrate brand naturally if required
5. Match the specified style and type
6. Optimize for {aspect_ratio} composition

Generate a professional image prompt (150-200 words) that will produce high-quality, commercially viable images.

RESPOND WITH THE IMAGE PROMPT:""",
                )
            },
        }

    def get_prompt(self, prompt_type: str, version: str = None) -> str:
        """
        Get a prompt by type and version

        Args:
            prompt_type: Type of prompt (flow_decider, character_profile, image_prompt)
            version: Specific version (defaults to current_version)

        Returns:
            The prompt string
        """
        if version is None:
            version = self.current_version

        if prompt_type not in self.prompts:
            raise ValueError(f"Unknown prompt type: {prompt_type}")

        if version not in self.prompts[prompt_type]:
            # Fallback to current version
            version = self.current_version
            if version not in self.prompts[prompt_type]:
                # Fallback to first available version
                version = list(self.prompts[prompt_type].keys())[0]

        return self.prompts[prompt_type][version].prompt

    def get_available_versions(self, prompt_type: str) -> list[str]:
        """Get available versions for a prompt type"""
        if prompt_type not in self.prompts:
            return []
        return list(self.prompts[prompt_type].keys())

    def add_prompt_version(self, prompt_type: str, version: str, prompt: str, description: str):
        """Add a new version of a prompt"""
        if prompt_type not in self.prompts:
            self.prompts[prompt_type] = {}

        self.prompts[prompt_type][version] = PromptVersion(
            version=version, prompt=prompt, description=description
        )

    def set_current_version(self, version: str):
        """Set the current default version"""
        self.current_version = version


# Global prompt manager instance
prompt_manager = PromptManager()


# Convenience functions for backward compatibility
def get_flow_decider_prompt(version: str = None) -> str:
    """Get the flow decider prompt"""
    return prompt_manager.get_prompt("flow_decider", version)


def get_character_profile_prompt(version: str = None) -> str:
    """Get the character profile prompt"""
    return prompt_manager.get_prompt("character_profile", version)


def get_image_prompt_template(version: str = None) -> str:
    """Get the image prompt template"""
    return prompt_manager.get_prompt("image_prompt", version)
