"""
State management for the agentic image flow using LangGraph
"""

from typing import Dict, List, Any, Optional, TypedDict, Annotated
from dataclasses import dataclass
from enum import Enum
import operator


class ImageType(Enum):
    """Types of images that can be generated"""

    OBJECT = "object"
    CHARACTER = "character"
    OBJECT_CHARACTER = "object_character"
    MONTAGE = "montage"


class StylePreset(Enum):
    """Available style presets"""

    CINEMATIC = "cinematic"
    COMMERCIAL = "commercial"
    LIFESTYLE = "lifestyle"
    PRODUCT_FOCUS = "product_focus"
    ARTISTIC = "artistic"


@dataclass
class SceneImagePlan:
    """Plan for a single scene's image generation"""

    scene_number: int
    image_type: ImageType
    aspect_ratio: str
    style_preset: StylePreset
    include_brand: bool
    weight_score: float
    rationale: str


@dataclass
class BrandInfo:
    """Brand information for image generation"""

    name: str
    description: str
    visual_style: str
    colors: List[str]
    logo_url: Optional[str] = None
    product_image_url: Optional[str] = None


@dataclass
class ImageParameters:
    """Image generation parameters"""

    aspect_ratio: str = "16:9"
    style: str = "cinematic"
    quality: str = "high"
    num_images: int = 1


class AgenticImageState(TypedDict):
    """State for the agentic image flow"""

    # Input data
    scenes: List[Dict[str, Any]]
    brand_info: Optional[BrandInfo]
    global_style: Dict[str, Any]

    # Processing state
    scene_plans: Annotated[List[SceneImagePlan], operator.add]
    character_profile: str
    location_groups: Dict[str, List[int]]

    # Generated outputs
    image_prompts: Annotated[List[Dict[str, Any]], operator.add]

    # Flow control
    current_scene_index: int
    total_scenes: int
    processing_complete: bool

    # Error handling
    errors: Annotated[List[str], operator.add]

    # Weights and balancing
    type_weights: Dict[str, float]
    current_type_distribution: Dict[str, int]


def create_initial_state(
    scenes: List[Dict[str, Any]],
    brand_info: Optional[Dict[str, Any]] = None,
    global_style: Optional[Dict[str, Any]] = None,
) -> AgenticImageState:
    """Create initial state for the agentic image flow"""

    brand = None
    if brand_info:
        brand = BrandInfo(
            name=brand_info.get("name", ""),
            description=brand_info.get("description", ""),
            visual_style=brand_info.get("visual_style", ""),
            colors=brand_info.get("colors", []),
            logo_url=brand_info.get("logo_url"),
            product_image_url=brand_info.get("product_image_url"),
        )

    return AgenticImageState(
        scenes=scenes,
        brand_info=brand,
        global_style=global_style or {},
        scene_plans=[],
        character_profile="",
        location_groups={},
        image_prompts=[],
        current_scene_index=0,
        total_scenes=len(scenes),
        processing_complete=False,
        errors=[],
        type_weights={"object": 0.3, "character": 0.4, "object_character": 0.25, "montage": 0.05},
        current_type_distribution={
            "object": 0,
            "character": 0,
            "object_character": 0,
            "montage": 0,
        },
    )


def update_type_distribution(state: AgenticImageState, image_type: ImageType) -> None:
    """Update the current type distribution"""
    type_str = image_type.value
    state["current_type_distribution"][type_str] += 1


def get_type_balance_score(state: AgenticImageState, proposed_type: ImageType) -> float:
    """Calculate balance score for a proposed image type"""
    total_processed = sum(state["current_type_distribution"].values())
    if total_processed == 0:
        return 1.0

    type_str = proposed_type.value
    current_ratio = state["current_type_distribution"][type_str] / total_processed
    target_ratio = state["type_weights"][type_str]

    # Return higher score if we're under the target ratio
    if current_ratio < target_ratio:
        return 1.0 + (target_ratio - current_ratio)
    else:
        return max(0.1, 1.0 - (current_ratio - target_ratio))
