"""
Image Prompt Generation Service
Wraps the existing image_prompt_gen.py functionality for the microservice
"""

# Global imports
import os
import sys
from pathlib import Path
from loguru import logger
from typing import Dict, List, Optional, Any

# Local imports
# Import from the local image_service directory
try:
    from ..image_prompt_gen import ImagePromptGenerator
except ImportError:
    logger.error("❌ Could not import ImagePromptGenerator from image_prompt_gen.py")
    raise


class PromptGenerationService:
    """Service wrapper for image prompt generation"""

    def __init__(self):
        """Initialize the prompt generation service"""
        try:
            self.prompt_generator = ImagePromptGenerator()
            logger.info("✅ PromptGenerationService initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize PromptGenerationService: {e}")
            raise

    def set_brand_info(self, brand_info: Dict[str, Any]):
        """
        Set brand information for prompt generation

        Args:
            brand_info: Dictionary containing brand details
        """
        try:
            self.prompt_generator.set_brand_info(brand_info)
            logger.info(
                f"✅ Brand info set for prompt generation: {brand_info.get('brand_name', 'Unknown')}"
            )
        except Exception as e:
            logger.error(f"❌ Failed to set brand info: {e}")

    def generate_scene_prompts(self, scenes: List[Dict]) -> List[Dict]:
        """
        Generate image prompts for multiple scenes

        Args:
            scenes: List of scene dictionaries

        Returns:
            List of scene prompt dictionaries
        """
        try:
            logger.info(f"🎨 Generating prompts for {len(scenes)} scenes")

            scene_prompts = self.prompt_generator.generate_all_scene_prompts(scenes)

            logger.success(f"✅ Generated prompts for {len(scene_prompts)} scenes")

            return scene_prompts

        except Exception as e:
            logger.error(f"❌ Error generating scene prompts: {e}")
            return []

    def generate_single_scene_prompt(self, scene: Dict, include_brand: bool = True) -> str:
        """
        Generate a single image prompt for a scene

        Args:
            scene: Scene dictionary
            include_brand: Whether to include brand integration

        Returns:
            Generated image prompt string
        """
        try:
            logger.info(f"🎨 Generating prompt for scene {scene.get('number', 'unknown')}")

            prompt = self.prompt_generator.generate_scene_image_prompt(scene, include_brand)

            logger.success(f"✅ Generated prompt for scene {scene.get('number', 'unknown')}")

            return prompt

        except Exception as e:
            logger.error(f"❌ Error generating single scene prompt: {e}")
            return f"Error generating prompt: {str(e)}"

    def extract_character_profile(self, scenes: List[Dict]) -> str:
        """
        Extract character profile from scenes for consistency

        Args:
            scenes: List of scene dictionaries

        Returns:
            Character profile description
        """
        try:
            logger.info("🎭 Extracting character profile")

            profile = self.prompt_generator._extract_character_profile(scenes)

            logger.success("✅ Character profile extracted")

            return profile

        except Exception as e:
            logger.error(f"❌ Error extracting character profile: {e}")
            return "Professional adult character with authentic appearance"

    def extract_location_groups(self, scenes: List[Dict]) -> Dict[str, str]:
        """
        Extract location groups from scenes for background consistency

        Args:
            scenes: List of scene dictionaries

        Returns:
            Dictionary mapping location groups to background descriptions
        """
        try:
            logger.info("🏢 Extracting location groups")

            location_groups = self.prompt_generator._extract_location_groups(scenes)

            logger.success(f"✅ Extracted {len(location_groups)} location groups")

            return location_groups

        except Exception as e:
            logger.error(f"❌ Error extracting location groups: {e}")
            return {}

    def enhance_prompt_with_style(self, base_prompt: str, style_preferences: Dict) -> str:
        """
        Enhance a base prompt with style preferences

        Args:
            base_prompt: The base image prompt
            style_preferences: Dictionary with style options

        Returns:
            Enhanced prompt string
        """
        try:
            logger.info("🎨 Enhancing prompt with style preferences")

            enhanced_prompt = self.prompt_generator.enhance_prompt_with_style(
                base_prompt, style_preferences
            )

            logger.success("✅ Prompt enhanced with style preferences")

            return enhanced_prompt

        except Exception as e:
            logger.error(f"❌ Error enhancing prompt: {e}")
            return base_prompt

    def clear_brand_info(self):
        """Clear brand information"""
        try:
            self.prompt_generator.clear_brand_info()
            logger.info("✅ Brand info cleared from prompt generator")
        except Exception as e:
            logger.error(f"❌ Failed to clear brand info: {e}")

    def format_prompts_for_display(self, scene_prompts: List[Dict]) -> str:
        """
        Format scene prompts for display

        Args:
            scene_prompts: List of scene prompt dictionaries

        Returns:
            Formatted string for display
        """
        try:
            return self.prompt_generator.format_prompts_for_display(scene_prompts)
        except Exception as e:
            logger.error(f"❌ Error formatting prompts: {e}")
            return "Error formatting prompts for display"
