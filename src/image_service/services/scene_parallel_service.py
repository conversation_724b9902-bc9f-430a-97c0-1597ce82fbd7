"""
Scene-level parallel image generation service

This service processes each scene independently in parallel:
- Scene 1: Planning → Generation → Upload → Status Update
- Scene 2: Planning → Generation → Upload → Status Update  
- Scene 3: Planning → Generation → Upload → Status Update
(All scenes run concurrently)
"""

import asyncio
from typing import List, Dict, Any, Optional, Callable
from loguru import logger

from src.image_service.services.agentic_image_service import AgenticImageService
from src.image_service.services.image_generator import ImageGenerationService


class SceneParallelImageService:
    """Service for processing scenes in true parallel mode"""
    
    def __init__(self, max_concurrent_scenes: int = 3):
        self.max_concurrent_scenes = max_concurrent_scenes
        self.agentic_service = AgenticImageService()
        self.image_generator = ImageGenerationService()
        
    def set_brand_info(self, brand_info: Dict[str, Any]):
        """Set brand information for image generation"""
        self.agentic_service.set_brand_info(brand_info)
        
    def set_global_style(self, style: Dict[str, Any]):
        """Set global style preferences"""
        self.agentic_service.set_global_style(style)

    async def process_scenes_parallel(
        self,
        scenes_data: List[Dict[str, Any]],
        aspect_ratio: str = "16:9",
        include_brand: bool = True,
        scene_completion_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None,
        progress_callback: Optional[Callable[[str, int, int, str], None]] = None,
    ) -> Dict[str, Any]:
        """
        Process multiple scenes in true parallel mode
        
        Each scene runs its complete pipeline independently:
        1. Individual agentic planning
        2. Image generation  
        3. Return results for immediate processing
        
        Args:
            scenes_data: List of scene dictionaries
            aspect_ratio: Aspect ratio for images
            include_brand: Whether to include brand integration
            scene_completion_callback: Called when each scene completes (scene_id, result)
            progress_callback: Called for overall progress updates
            
        Returns:
            Dictionary with aggregated results
        """
        total_scenes = len(scenes_data)
        logger.info(f"🚀 Starting TRUE parallel scene processing for {total_scenes} scenes")
        
        # Create semaphore to limit concurrent scenes
        semaphore = asyncio.Semaphore(self.max_concurrent_scenes)
        
        async def process_single_scene_complete(scene_data: Dict[str, Any], scene_index: int) -> Dict[str, Any]:
            """Process a single scene from planning to generation"""
            async with semaphore:
                scene_id = scene_data["id"]
                scene_number = scene_data["number"]
                
                try:
                    logger.info(f"🎯 Starting complete processing for scene {scene_number} ({scene_id})")
                    
                    # Step 1: Individual Agentic Planning
                    logger.info(f"🧠 Planning scene {scene_number}...")
                    scene_prompts = await self.agentic_service.plan_and_generate_prompts([scene_data])
                    
                    if not scene_prompts or len(scene_prompts) == 0:
                        raise Exception(f"Agentic planning failed for scene {scene_number}")
                    
                    scene_prompt = scene_prompts[0]  # Get the first (and only) prompt
                    logger.success(f"✅ Planning completed for scene {scene_number}")
                    
                    # Step 2: Image Generation
                    logger.info(f"🎨 Generating image for scene {scene_number}...")
                    generation_results = await self.image_generator.generate_images_parallel(
                        scene_prompts=[scene_prompt],
                        max_concurrent=1,  # Only one image per scene
                        progress_callback=None
                    )
                    
                    if not generation_results or len(generation_results) == 0:
                        raise Exception(f"Image generation failed for scene {scene_number}")
                    
                    generation_result = generation_results[0]  # Get the first (and only) result
                    
                    if not generation_result.get("success") or not generation_result.get("images"):
                        raise Exception(f"Image generation unsuccessful for scene {scene_number}: {generation_result.get('error', 'Unknown error')}")
                    
                    logger.success(f"✅ Image generated for scene {scene_number}")
                    
                    # Step 3: Prepare result
                    result = {
                        "success": True,
                        "scene_id": scene_id,
                        "scene_number": scene_number,
                        "images": generation_result["images"],
                        "prompt": scene_prompt.get("image_prompt", ""),
                        "metadata": {
                            "image_type": scene_prompt.get("type"),
                            "aspect_ratio": scene_prompt.get("aspect_ratio"),
                            "style_preset": scene_prompt.get("style_preset"),
                            "includes_brand": scene_prompt.get("includes_brand"),
                            "rationale": scene_prompt.get("rationale"),
                            "weight_score": scene_prompt.get("weight_score"),
                        }
                    }
                    
                    # Notify completion immediately
                    if scene_completion_callback:
                        scene_completion_callback(scene_id, result)
                    
                    logger.success(f"🎉 Scene {scene_number} completed successfully!")
                    return result
                    
                except Exception as e:
                    logger.error(f"❌ Complete processing failed for scene {scene_number}: {e}")
                    error_result = {
                        "success": False,
                        "scene_id": scene_id,
                        "scene_number": scene_number,
                        "error": str(e),
                        "prompt": "",
                        "metadata": {}
                    }
                    
                    # Notify completion even for errors
                    if scene_completion_callback:
                        scene_completion_callback(scene_id, error_result)
                    
                    return error_result
        
        try:
            # Execute all scenes in parallel but maintain order for frontend
            logger.info(f"🚀 Processing {total_scenes} scenes in TRUE parallel mode (max_concurrent={self.max_concurrent_scenes})...")

            if progress_callback:
                progress_callback("processing", 0, total_scenes, "Starting parallel scene processing")

            # Sort scenes by scene_number to ensure proper order
            sorted_scenes_data = sorted(scenes_data, key=lambda x: x.get("number", 0))

            scene_tasks = [
                process_single_scene_complete(scene_data, i)
                for i, scene_data in enumerate(sorted_scenes_data)
            ]

            # Wait for all scenes to complete
            scene_results = await asyncio.gather(*scene_tasks, return_exceptions=True)
            
            # Process results
            successful_results = []
            failed_results = []
            
            for result in scene_results:
                if isinstance(result, Exception):
                    failed_results.append({
                        "scene_id": "unknown",
                        "error": str(result),
                        "scene_number": 0
                    })
                elif result.get("success"):
                    successful_results.append(result)
                else:
                    failed_results.append(result)
            
            # Final progress update
            if progress_callback:
                progress_callback("completed", total_scenes, total_scenes, "All scenes processed")
            
            # Prepare final results
            final_results = {
                "success": len(successful_results) > 0,
                "total_scenes": total_scenes,
                "successful_generations": len(successful_results),
                "failed_generations": len(failed_results),
                "success_rate": f"{(len(successful_results) / total_scenes * 100):.1f}%",
                "successful_results": successful_results,
                "failed_results": failed_results,
                "processing_mode": "scene_parallel"
            }
            
            logger.success(
                f"🎉 Scene parallel processing completed: {len(successful_results)}/{total_scenes} successful "
                f"({final_results['success_rate']})"
            )
            
            return final_results
            
        except Exception as e:
            logger.error(f"❌ Scene parallel processing failed: {e}")
            if progress_callback:
                progress_callback("error", 0, total_scenes, f"Processing failed: {str(e)}")
            
            return {
                "success": False,
                "error": str(e),
                "total_scenes": total_scenes,
                "successful_generations": 0,
                "failed_generations": total_scenes,
                "success_rate": "0%",
                "successful_results": [],
                "failed_results": [],
                "processing_mode": "scene_parallel"
            }
