"""
Image Generation Service
Wraps the existing flux_image_generator.py functionality for the microservice
"""

# Global imports
import asyncio
from concurrent.futures import ThreadPoolExecutor
from loguru import logger
from typing import Dict, List, Optional, Any, Callable

# Local imports
# Import from the local image_service directory
try:
    from ..flux_image_generator import FluxImageGenerator
except ImportError:
    logger.error("❌ Could not import FluxImageGenerator from flux_image_generator.py")
    raise


class ImageGenerationService:
    """Service wrapper for FLUX image generation"""

    def __init__(self):
        """Initialize the image generation service"""
        try:
            self.flux_generator = FluxImageGenerator()
            logger.info("✅ ImageGenerationService initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize ImageGenerationService: {e}")
            raise

    def set_brand_info(self, brand_info: Dict[str, Any]):
        """
        Set brand information for image generation

        Args:
            brand_info: Dictionary containing brand details
        """
        try:
            self.flux_generator.set_brand_info(brand_info)
            logger.info(f"✅ Brand info set: {brand_info.get('brand_name', 'Unknown')}")
        except Exception as e:
            logger.error(f"❌ Failed to set brand info: {e}")

    def generate_single_image(
        self, prompt: str, scene_data: Optional[Dict] = None, **kwargs
    ) -> Dict:
        """
        Generate a single image using FLUX

        Args:
            prompt: The image generation prompt
            scene_data: Optional scene data for brand integration
            **kwargs: Additional parameters for generation

        Returns:
            Dictionary containing generation result
        """
        try:
            logger.info(f"🎨 Generating single image with prompt: {prompt[:100]}...")

            result = self.flux_generator.generate_single_image(
                prompt=prompt, scene_data=scene_data, **kwargs
            )

            if result.get("success"):
                logger.success(f"✅ Image generated successfully")
                logger.debug(f"Generated {len(result.get('images', []))} images")
            else:
                logger.error(f"❌ Image generation failed: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"❌ Error in generate_single_image: {e}")
            return {"success": False, "error": str(e), "images": [], "prompt": prompt}

    def generate_multiple_images(
        self, prompts: List[str], scene_data: Optional[Dict] = None, **kwargs
    ) -> List[Dict]:
        """
        Generate multiple images using FLUX

        Args:
            prompts: List of image generation prompts
            scene_data: Optional scene data for brand integration
            **kwargs: Additional parameters for generation

        Returns:
            List of generation results
        """
        try:
            logger.info(f"🎨 Generating {len(prompts)} images")

            results = []
            for i, prompt in enumerate(prompts):
                logger.info(f"🎨 Generating image {i+1}/{len(prompts)}")

                result = self.generate_single_image(prompt=prompt, scene_data=scene_data, **kwargs)

                results.append(result)

                # Add small delay between generations
                if i < len(prompts) - 1:
                    import time

                    time.sleep(1)

            success_count = sum(1 for r in results if r.get("success"))
            logger.success(f"✅ Generated {success_count}/{len(prompts)} images successfully")

            return results

        except Exception as e:
            logger.error(f"❌ Error in generate_multiple_images: {e}")
            return [{"success": False, "error": str(e), "images": []} for _ in prompts]

    def test_api_connection(self) -> tuple[bool, str]:
        """
        Test the FLUX API connection

        Returns:
            Tuple of (success: bool, message: str)
        """
        try:
            return self.flux_generator.test_api_connection()
        except Exception as e:
            logger.error(f"❌ API connection test failed: {e}")
            return False, f"API connection test failed: {e}"

    def clear_brand_info(self):
        """Clear brand information"""
        try:
            self.flux_generator.clear_brand_info()
            logger.info("✅ Brand info cleared")
        except Exception as e:
            logger.error(f"❌ Failed to clear brand info: {e}")

    def get_generation_summary(self, results: List[Dict]) -> Dict:
        """
        Get summary of generation results

        Args:
            results: List of generation results

        Returns:
            Summary dictionary
        """
        try:
            return self.flux_generator.get_generation_summary(results)
        except Exception as e:
            logger.error(f"❌ Failed to get generation summary: {e}")
            return {
                "total_scenes": len(results),
                "successful_generations": 0,
                "failed_generations": len(results),
                "total_images_generated": 0,
                "success_rate": "0%",
                "errors": [str(e)],
            }

    async def generate_images_parallel(
        self,
        scene_prompts: List[Dict[str, Any]],
        max_concurrent: int = 3,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Generate images for multiple scenes in parallel using asyncio

        Args:
            scene_prompts: List of scene prompt dictionaries with scene data
            max_concurrent: Maximum number of concurrent image generations
            progress_callback: Optional callback for progress updates (current, total, scene_id)

        Returns:
            List of generation results
        """
        if not scene_prompts:
            return []

        logger.info(f"🚀 Starting parallel image generation for {len(scene_prompts)} scenes (max_concurrent={max_concurrent})")

        # DEBUG: Check if scene_prompts count is reasonable
        if len(scene_prompts) > 50:
            logger.error(f"🚨 CRITICAL: scene_prompts has {len(scene_prompts)} items - this seems wrong!")
            logger.error(f"🚨 First few items: {scene_prompts[:3] if scene_prompts else 'None'}")
            logger.error(f"🚨 Last few items: {scene_prompts[-3:] if scene_prompts else 'None'}")
            # Limit to reasonable number to prevent infinite loop
            scene_prompts = scene_prompts[:6]
            logger.warning(f"🚨 LIMITED scene_prompts to first 6 items to prevent infinite loop")

        # Create semaphore to limit concurrent generations
        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_single_async(scene_prompt: Dict[str, Any], index: int) -> Dict[str, Any]:
            """Generate a single image asynchronously"""
            async with semaphore:
                scene_id = scene_prompt.get("scene_id", f"scene_{index}")

                try:
                    if progress_callback:
                        progress_callback(index, len(scene_prompts), scene_id)

                    logger.info(f"🎨 Generating image for scene {scene_id} ({index + 1}/{len(scene_prompts)})")

                    # Run the synchronous image generation in a thread pool
                    loop = asyncio.get_event_loop()
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        result = await loop.run_in_executor(
                            executor,
                            self._generate_single_sync,
                            scene_prompt
                        )

                    # Add scene metadata to result
                    result["scene_id"] = scene_id
                    result["scene_number"] = scene_prompt.get("scene_number", index + 1)

                    if result.get("success"):
                        logger.success(f"✅ Image generated for scene {scene_id}")
                    else:
                        logger.error(f"❌ Image generation failed for scene {scene_id}: {result.get('error')}")

                    return result

                except Exception as e:
                    logger.error(f"❌ Exception during image generation for scene {scene_id}: {e}")
                    return {
                        "success": False,
                        "error": str(e),
                        "scene_id": scene_id,
                        "scene_number": scene_prompt.get("scene_number", index + 1),
                        "images": []
                    }

        # Create tasks for all scenes
        tasks = [
            generate_single_async(scene_prompt, i)
            for i, scene_prompt in enumerate(scene_prompts)
        ]

        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions that occurred
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Task exception for scene {i}: {result}")
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "scene_id": scene_prompts[i].get("scene_id", f"scene_{i}"),
                    "scene_number": scene_prompts[i].get("scene_number", i + 1),
                    "images": []
                })
            else:
                processed_results.append(result)

        # Log summary
        successful = sum(1 for r in processed_results if r.get("success"))
        logger.success(f"🎉 Parallel generation completed: {successful}/{len(scene_prompts)} successful")

        return processed_results

    def _generate_single_sync(self, scene_prompt: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synchronous wrapper for single image generation
        Used by the async parallel generation method
        """
        try:
            return self.generate_single_image(
                prompt=scene_prompt.get("image_prompt", ""),
                scene_data=scene_prompt,
                aspect_ratio=scene_prompt.get("aspect_ratio", "16:9"),
                num_images=1
            )
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "images": []
            }
