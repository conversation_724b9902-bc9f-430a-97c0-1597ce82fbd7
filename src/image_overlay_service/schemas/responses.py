from pydantic import BaseModel, Field
from typing import Optional


class OverlayResponse(BaseModel):
    status: str = Field(..., description="Status of the overlay operation (success or error)")
    message: Optional[str] = Field(None, description="Optional message or error details")
    s3_key: Optional[str] = Field(None, description="S3 object key for the output video")
    presigned_url: Optional[str] = Field(
        None, description="Presigned S3 URL for downloading the output video"
    )
