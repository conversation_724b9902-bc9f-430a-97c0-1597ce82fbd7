from pydantic import BaseModel, Field
from typing import Optional


class SimpleOverlayRequest(BaseModel):
    script_id: str = Field(..., description="Script ID to fetch the final video")
    logo_base64: str = Field(..., description="Base64-encoded logo image")
    position: str = Field(
        "top-right",
        description="Logo position (e.g., top-right, top-left, bottom-right, bottom-left, center)",
    )
    opacity: float = Field(0.8, description="Logo opacity (0.0 to 1.0)")
    margin: int = Field(20, description="Margin from the edge in pixels")


class OptionARequest(BaseModel):
    script_id: str = Field(..., description="Script ID to fetch the final video")
    logo_base64: str = Field(..., description="Base64-encoded logo image")
    brand_name: str = Field(..., description="Brand name for outro text")
    brand_tagline: Optional[str] = Field("", description="Brand tagline for outro text")
    blur_duration: float = Field(3.0, description="Duration (seconds) of the blurred outro section")


class OptionBRequest(BaseModel):
    script_id: str = Field(..., description="Script ID to fetch the final video")
    logo_base64: str = Field(..., description="Base64-encoded logo image")
    brand_name: str = Field(..., description="Brand name for outro text")
    brand_tagline: Optional[str] = Field("", description="Brand tagline for outro text")
