# Service logic for image overlay (brand outro, logo overlay, etc.)
# Moved from user-provided code

# Global imports
import os
import uuid
import json
import shutil
import tempfile
import numpy as np
from pathlib import Path
from loguru import logger
from moviepy.video import fx as vfx
import google.generativeai as genai
from typing import Any, Dict, List, Optional, Tuple
from PIL import Image, ImageDraw, ImageFont
from moviepy import (
    Color<PERSON>lip,
    CompositeVideoClip,
    ImageClip,
    VideoFileClip,
    # Local imports
    concatenate_videoclips,
)

# Use shared S3 client
from src.shared.utils.s3_client import S3Client

# --- Begin EnhancedBrandImageOverlayProcessor class and helpers ---

# (Full class definition pasted from user code)


class EnhancedBrandImageOverlayProcessor:
    """Enhanced processor for brand image overlays with outro features"""

    def __init__(self):
        # Initialize S3 client
        self.s3_client = S3Client(aws_region="us-east-2")
        self.s3_bucket = "assets-vidflux"
        self.text_overlay_prefix = "Vidflux-Assets/text_overlay_assets/"
        self.image_overlay_prefix = "Vidflux-Assets/image-overlay/"

        self.temp_dir = Path("temp_brand_overlays")
        self.temp_dir.mkdir(exist_ok=True)

        # Output directories
        self.overlay_output_dir = Path("output/image_overlay_videos")
        self.brand_outro_output_dir = Path("output/brand_outro_videos")
        self.overlay_output_dir.mkdir(parents=True, exist_ok=True)
        self.brand_outro_output_dir.mkdir(parents=True, exist_ok=True)

        # Video source directories (priority order)
        self.video_source_dirs = [
            "output/final_videos",
            "output/text_videos",
            "output/custom_text_videos",
            "output/mixed_videos",
            "output/stitched_videos",
            "output/videos",
        ]

        # Default overlay settings
        base_w, base_h = (120, 80)
        self.default_logo_size = (int(base_w * 3.8), int(base_h * 3.8))
        self.default_position = "top-right"
        self.default_margin = 20
        self.default_opacity = 0.8

        # Brand outro settings
        self.outro_duration = 2.0  # seconds
        self.blur_intensity = 25  # Increased from 15 to 25 for better blending
        self.brand_text_size_ratio = 0.06  # 6% of video height
        self.logo_size_ratio = 0.2  # 20% of video height

        # NEW: Semi-transparent overlay settings for Option A
        self.overlay_opacity = 0.7  # 30% transparent (70% opaque)

        # Initialize Gemini AI for smart background selection
        self.setup_gemini_ai()

        logger.info("Enhanced Brand Image Overlay Processor initialized with S3 integration")

    def setup_gemini_ai(self):
        self.llm_service = None  # Always define the attribute, even if setup fails
        try:
            # Use shared LLM service
            from src.shared.services.llm_service import llm_service

            if llm_service.is_available():
                self.llm_service = llm_service
                self.gemini_available = True
                available_providers = llm_service.get_available_providers()
                logger.info(f"✅ LLM service initialized with providers: {available_providers}")
            else:
                self.gemini_available = False
                logger.warning("⚠️ No LLM providers available - using fallback colors")
        except Exception as e:
            self.gemini_available = False
            logger.warning(f"⚠️ LLM service setup failed: {e} - using fallback colors")

    def add_simple_logo_overlay(
        self,
        video_path: str,
        logo_path: str,
        position: str = "top-right",
        opacity: float = 0.8,
        margin: int = 20,
    ) -> Dict[str, Any]:
        """Simple logo overlay (existing functionality)"""
        try:
            logger.info(f"Adding simple logo overlay to: {video_path}")

            # Resize logo
            resized_logo_path = self.resize_logo_image(logo_path, self.default_logo_size)

            # Load video
            video_clip = VideoFileClip(video_path)
            video_size = (video_clip.w, video_clip.h)

            # Load and prepare logo
            with Image.open(resized_logo_path) as logo_img:
                actual_logo_size = logo_img.size

            # Calculate position
            logo_position = self.calculate_overlay_position(
                video_size, actual_logo_size, position, margin
            )

            # Create logo clip
            logo_clip = (
                ImageClip(resized_logo_path, transparent=True)
                .with_duration(video_clip.duration)
                .with_position(logo_position)
                .with_opacity(opacity)
            )

            # Composite video
            final_video = CompositeVideoClip([video_clip, logo_clip])

            # Generate output path
            input_name = Path(video_path).stem
            output_path = (
                self.overlay_output_dir / f"{input_name}_simple_overlay_{uuid.uuid4().hex[:8]}.mp4"
            )

            # Write video
            final_video.write_videofile(
                str(output_path),
                codec="libx264",
                audio_codec="aac",
                temp_audiofile="temp-audio.m4a",
                remove_temp=True,
                logger=None,
            )

            # Cleanup
            video_clip.close()
            logo_clip.close()
            final_video.close()

            return {
                "status": "success",
                "input_video": video_path,
                "output_video": str(output_path),
                "option": "Simple Overlay",
                "position": position,
                "opacity": opacity,
            }

        except Exception as e:
            logger.error(f"Simple overlay failed: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "input_video": video_path,
                "option": "Simple Overlay",
            }

    def resize_logo_image(self, image_path: str, target_size: Tuple[int, int]) -> str:
        """Resize logo image (existing functionality)"""
        try:
            with Image.open(image_path) as img:
                if img.mode != "RGBA":
                    img = img.convert("RGBA")

                # Calculate aspect ratio preserving resize
                original_width, original_height = img.size
                target_width, target_height = target_size

                scale_w = target_width / original_width
                scale_h = target_height / original_height
                scale = min(scale_w, scale_h)

                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                output_path = self.temp_dir / f"resized_logo_{uuid.uuid4().hex[:8]}.png"
                resized_img.save(output_path, "PNG")

                return str(output_path)

        except Exception as e:
            logger.error(f"Failed to resize logo: {str(e)}")
            raise

    def calculate_overlay_position(
        self,
        video_size: Tuple[int, int],
        logo_size: Tuple[int, int],
        position: str = "top-right",
        margin: int = 20,
    ) -> Tuple[int, int]:
        """Calculate overlay position (existing functionality)"""
        video_width, video_height = video_size
        logo_width, logo_height = logo_size

        position_map = {
            "top-right": (video_width - logo_width - margin, margin),
            "top-left": (margin, margin),
            "bottom-right": (
                video_width - logo_width - margin,
                video_height - logo_height - margin,
            ),
            "bottom-left": (margin, video_height - logo_height - margin),
            "center": (
                (video_width - logo_width) // 2,
                (video_height - logo_height) // 2,
            ),
        }

        return position_map.get(position, position_map["top-right"])

    def create_semi_transparent_overlay(
        self, video_size: Tuple[int, int], overlay_color: str, duration: float
    ) -> CompositeVideoClip:
        """
        Creates a semi-transparent overlay clip.
        """
        overlay_color_rgb = self.hex_to_rgb(overlay_color)
        overlay_clip = ColorClip(size=video_size, color=overlay_color_rgb, duration=duration)
        overlay_clip = overlay_clip.with_opacity(self.overlay_opacity)
        return overlay_clip

    def hex_to_rgb(self, color: str) -> tuple:
        # Handle named colors
        if color.lower() == "black":
            return (0, 0, 0)
        if color.lower() == "white":
            return (255, 255, 255)

        # Handle hex colors
        if color.startswith("#"):
            color = color.lstrip("#")
        # If it's a short hex (e.g. 'fff'), expand it
        if len(color) == 3:
            color = "".join([c * 2 for c in color])
        return tuple(int(color[i : i + 2], 16) for i in (0, 2, 4))

    def resize_logo_for_outro(self, logo_path: str, video_size: Tuple[int, int]) -> str:
        """Resizes the logo for the outro to a fixed size."""
        with Image.open(logo_path) as img:
            if img.mode != "RGBA":
                img = img.convert("RGBA")

            # Calculate aspect ratio preserving resize
            original_width, original_height = img.size
            target_width = int(video_size[0] * self.logo_size_ratio)
            target_height = int(video_size[1] * self.logo_size_ratio)

            scale_w = target_width / original_width
            scale_h = target_height / original_height
            scale = min(scale_w, scale_h)

            new_width = int(original_width * scale)
            new_height = int(original_height * scale)

            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            output_path = self.temp_dir / f"resized_logo_outro_{uuid.uuid4().hex[:8]}.png"
            resized_img.save(output_path, "PNG")
            return str(output_path)

    def create_brand_text_overlay(
        self,
        video_size: Tuple[int, int],
        brand_name: str,
        brand_tagline: str,
        style_data: Dict[str, Any],
        logo_height: Optional[int] = None,
        duration: Optional[float] = None,
    ) -> Optional[ImageClip]:
        """Create professional brand text overlay with smart positioning and color - FIXED: Custom duration support"""

        # FIXED: Handle optional brand name
        if not brand_name or not brand_name.strip():
            if not brand_tagline or not brand_tagline.strip():
                return None  # No text to display
            # Use only tagline if no brand name
            brand_name = ""

        width, height = video_size

        # Smart text color based on background/overlay
        ai_text_color = style_data.get("text_color", "#FFFFFF")
        fallback_text_color = self.get_smart_text_color(style_data)

        # Convert hex color to RGB tuple if needed
        if isinstance(ai_text_color, str) and ai_text_color.startswith("#"):
            text_color = self.hex_to_rgb(ai_text_color)
        elif isinstance(ai_text_color, (list, tuple)) and len(ai_text_color) == 3:
            text_color = tuple(ai_text_color)
        else:
            text_color = fallback_text_color

        # Calculate font sizes
        brand_font_size = int(height * self.brand_text_size_ratio)
        tagline_font_size = int(brand_font_size * 0.6)

        # Try to load a good font
        font_paths = [
            "/usr/share/fonts/truetype/ubuntu/Ubuntu-Bold.ttf",
            "/usr/share/fonts/truetype/roboto/Roboto-Bold.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
            "arial.ttf",
        ]

        brand_font = None
        tagline_font = None

        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    brand_font = ImageFont.truetype(font_path, brand_font_size)
                    tagline_font = ImageFont.truetype(font_path, tagline_font_size)
                    break
            except Exception:
                continue

        if not brand_font:
            try:
                brand_font = ImageFont.truetype("arial.ttf", brand_font_size)
            except Exception:
                brand_font = ImageFont.load_default()
        if not tagline_font:
            try:
                tagline_font = ImageFont.truetype("arial.ttf", tagline_font_size)
            except Exception:
                tagline_font = ImageFont.load_default()

        # Calculate text dimensions
        temp_img = Image.new("RGB", (1, 1))
        temp_draw = ImageDraw.Draw(temp_img)

        brand_width = brand_height = 0
        if brand_name.strip():
            try:
                brand_bbox = brand_font.getbbox(brand_name)
                brand_width = brand_bbox[2] - brand_bbox[0]
                brand_height = brand_bbox[3] - brand_bbox[1]
            except AttributeError:
                brand_width, brand_height = brand_font.getsize(brand_name)

        tagline_width = tagline_height = 0
        if brand_tagline and brand_tagline.strip():
            try:
                tagline_bbox = tagline_font.getbbox(brand_tagline)
                tagline_width = tagline_bbox[2] - tagline_bbox[0]
                tagline_height = tagline_bbox[3] - tagline_bbox[1]
            except AttributeError:
                tagline_width, tagline_height = tagline_font.getsize(brand_tagline)

        # Create text image
        text_spacing = 15
        total_text_height = brand_height + (
            tagline_height + text_spacing if brand_tagline and brand_tagline.strip() else 0
        )
        text_img_width = max(brand_width, tagline_width) + 40  # padding
        text_img_height = max(total_text_height + 40, 50)  # padding, minimum height

        # Handle case where no text provided
        if text_img_width <= 40 and text_img_height <= 50:
            return None

        text_img = Image.new("RGBA", (text_img_width, text_img_height), (0, 0, 0, 0))
        text_draw = ImageDraw.Draw(text_img)

        current_y = 20

        # Draw brand name (centered) if provided
        if brand_name.strip():
            brand_x = (text_img_width - brand_width) // 2
            text_draw.text((brand_x, current_y), brand_name, font=brand_font, fill=text_color)
            current_y += brand_height + text_spacing

        # Draw tagline if provided
        if brand_tagline and brand_tagline.strip():
            tagline_x = (text_img_width - tagline_width) // 2
            text_draw.text(
                (tagline_x, current_y),
                brand_tagline,
                font=tagline_font,
                fill=text_color,
            )

        # FIXED: Convert PIL Image to numpy array for MoviePy 2.x compatibility
        text_img_array = np.array(text_img)

        # Use provided duration or fall back to default outro_duration
        text_duration = duration if duration is not None else self.outro_duration

        logger.info(
            f"Text overlay created with color RGB{text_color} for better visibility (duration: {text_duration}s)"
        )
        return ImageClip(text_img_array, duration=text_duration, transparent=True)

    def analyze_video_style_with_gemini(self, video_path: str) -> Dict[str, Any]:
        """
        Analyzes the style of a video using Gemini AI.
        """
        if not self.llm_service:
            logger.warning("LLM service is not available, using fallback style.")
            return self.get_fallback_style()
        try:
            # Load video and extract frames
            video_clip = VideoFileClip(video_path)
            video_duration = video_clip.duration
            video_size = video_clip.size

            # Extract frames from the video
            frames = []
            for i in range(0, int(video_duration * 2), 2):  # Sample more frames for better analysis
                frame = video_clip.get_frame(i / 2)
                frames.append(frame)

            # Convert frames to a numpy array
            frames_np = np.array(frames)

            # Analyze the style using Gemini
            prompt = f"""
            Analyze the style of the following video frames.
            Provide a JSON response with the following keys:
            - style: A general description of the video's visual style (e.g., "modern", "cinematic", "artistic").
            - mood: A general description of the video's emotional tone (e.g., "happy", "sad", "neutral").
            - background_type: A description of the primary background element (e.g., "cityscape", "landscape", "abstract").
            - reasoning: A brief explanation of why the style and mood were determined.
            - overlay_color: A hex color string for a semi-transparent overlay (e.g., "#000000" for black, "#FFFFFF" for white).
            - text_color: A hex color string for the text color (e.g., "#FFFFFF" for white).
            - text_size_ratio: A ratio (0.0 to 1.0) for the text size relative to video height.
            - text_position: A string indicating the text's horizontal and vertical position (e.g., "top-center", "bottom-center", "center").

            Video Path: {video_path}
            Video Duration: {video_duration}s
            Video Size: {video_size}
            Number of Frames Analyzed: {len(frames)}

            Analyze the visual style, mood, and background of the video.
            """

            result = self.llm_service.generate_content(prompt)
            if result["success"]:
                analysis_text = result["content"]
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                # Return default analysis
                analysis_text = '{"dominant_colors": ["#FFFFFF", "#000000"], "mood": "neutral", "style": "modern"}'

            # Clean up the response text - remove markdown code blocks if present
            if analysis_text.startswith("```json"):
                analysis_text = analysis_text[7:]  # Remove ```json
            if analysis_text.startswith("```"):
                analysis_text = analysis_text[3:]  # Remove ```
            if analysis_text.endswith("```"):
                analysis_text = analysis_text[:-3]  # Remove trailing ```

            # Clean up any extra whitespace
            analysis_text = analysis_text.strip()

            # Parse the JSON response
            try:
                analysis_data = json.loads(analysis_text)
                logger.info(f"✅ Gemini AI style analysis successful: {analysis_data}")
                return analysis_data
            except json.JSONDecodeError:
                logger.error(f"❌ Failed to decode Gemini AI style analysis JSON: {analysis_text}")
                return {
                    "style": "Unknown",
                    "mood": "Unknown",
                    "background_type": "Unknown",
                    "reasoning": "Could not parse Gemini AI response.",
                    "overlay_color": "black",
                    "text_color": "white",
                    "text_size_ratio": 0.06,
                    "text_position": "bottom-center",
                }

        except Exception as e:
            logger.error(f"Gemini AI style analysis failed: {str(e)}")
            return {
                "style": "Unknown",
                "mood": "Unknown",
                "background_type": "Unknown",
                "reasoning": f"Error during Gemini AI style analysis: {str(e)}",
                "overlay_color": "black",
                "text_color": "white",
                "text_size_ratio": 0.06,
                "text_position": "bottom-center",
            }

    def get_fallback_style(self) -> dict:
        """Fallback style when Gemini is not available"""
        return {
            "style": "modern",
            "mood": "professional",
            "background_type": "gradient",
            "primary_color": [45, 45, 45],  # Dark gray
            "accent_color": [100, 149, 237],  # Cornflower blue
            "text_color": [255, 255, 255],  # White (good contrast on dark backgrounds)
            "overlay_color": "black",  # Default to black overlay
            "text_reasoning": "White text provides good contrast on dark overlay",
            "reasoning": "Professional dark theme (fallback)",
        }

    def get_smart_text_color(self, style_data: dict) -> tuple:
        """
        Determine smart text color based on background/overlay for better visibility.
        """
        overlay_color = style_data.get("overlay_color", "black")
        primary_color = style_data.get("primary_color", [45, 45, 45])

        # For Option A: Consider overlay color
        if overlay_color == "black":
            # Black overlay - use light text
            return (255, 255, 255)  # White
        elif overlay_color == "white":
            # White overlay - use dark text
            return (0, 0, 0)  # Black
        else:
            # For Option B: Consider background brightness
            avg_brightness = sum(primary_color) / 3
            if avg_brightness < 128:  # Dark background
                return (255, 255, 255)  # White text
            else:  # Light background
                return (0, 0, 0)  # Black text

    def create_brand_outro_option_a(
        self,
        video_path: str,
        logo_path: str,
        brand_name: str,
        brand_tagline: str = "",
        blur_last_seconds: float = 3.0,
    ) -> dict:
        """
        Option A: Blur last few seconds of video + semi-transparent overlay + brand overlay
        UPDATED: Added semi-transparent black/white overlay layer
        """
        try:
            logger.info(f"Creating Brand Outro Option A for: {video_path}")

            # Load video
            video_clip = VideoFileClip(video_path)
            video_duration = video_clip.duration
            video_size = video_clip.size

            # Calculate split point
            split_time = max(0, video_duration - blur_last_seconds)

            # Split video: normal part + part to blur
            normal_part = video_clip.subclipped(0, split_time) if split_time > 0 else None
            blur_part = video_clip.subclipped(split_time, video_duration)

            # Apply blur to the last part using MoviePy 2.x compatible method
            blur_method_used = "unknown"
            try:
                from moviepy.video.fx import blur

                blurred_part = blur_part.with_effects([blur.blur(self.blur_intensity)])
                blur_method_used = "moviepy.video.fx.blur"
                logger.info(f"✅ Using {blur_method_used} for blur effect")
            except (ImportError, AttributeError):
                try:
                    blurred_part = blur_part.with_effects([vfx.blur(self.blur_intensity)])
                    blur_method_used = "vfx.blur"
                    logger.info(f"✅ Using {blur_method_used} for blur effect")
                except (ImportError, AttributeError):
                    blur_method_used = "resize-based blur fallback"
                    logger.info(f"✅ Using {blur_method_used} (no native blur available)")
                    original_size = blur_part.size
                    blur_factor = 0.05
                    temp_size = (
                        int(original_size[0] * blur_factor),
                        int(original_size[1] * blur_factor),
                    )
                    blurred_part = blur_part.with_effects([vfx.Resize(temp_size)]).with_effects(
                        [vfx.Resize(original_size)]
                    )

            # Get style analysis
            style_data = self.analyze_video_style_with_gemini(video_path)

            # NEW: Create semi-transparent overlay
            overlay_color = style_data.get("overlay_color", "black")
            semi_transparent_overlay = self.create_semi_transparent_overlay(
                video_size, overlay_color, blur_last_seconds
            )

            # Resize logo for outro
            resized_logo_path = self.resize_logo_for_outro(logo_path, video_size)

            # Calculate logo dimensions for text positioning
            with Image.open(resized_logo_path) as logo_img:
                logo_width, logo_height = logo_img.size

            # Create logo clip
            logo_clip = (
                ImageClip(resized_logo_path, transparent=True)
                .with_duration(blur_last_seconds)
                .with_position("center")
            )

            # Create text overlay with smart positioning (only if brand_name is provided)
            text_clips = []
            if brand_name and brand_name.strip():
                text_clip = self.create_brand_text_overlay(
                    video_size,
                    brand_name,
                    brand_tagline,
                    style_data,
                    logo_height,
                    blur_last_seconds,
                )

                if text_clip:
                    video_center_x = video_size[0] // 2
                    video_center_y = video_size[1] // 2
                    logo_bottom_y = video_center_y + (logo_height // 2)
                    text_gap = 20
                    text_y_position = logo_bottom_y + text_gap

                    text_clip = text_clip.with_position(("center", text_y_position))
                    text_clips.append(text_clip)

            # Composite blurred part with layers in correct order
            if normal_part:
                composite_clips = [
                    blurred_part,
                    semi_transparent_overlay,
                    logo_clip.with_effects([vfx.FadeIn(0.3)]),
                ]
                if text_clips:
                    composite_clips.extend(
                        [clip.with_effects([vfx.FadeIn(0.3)]) for clip in text_clips]
                    )

                blurred_composite = CompositeVideoClip(composite_clips)
                final_video = concatenate_videoclips([normal_part, blurred_composite])
            else:
                composite_clips = [
                    blurred_part,
                    semi_transparent_overlay,
                    logo_clip.with_effects([vfx.FadeIn(0.3)]),
                ]
                if text_clips:
                    composite_clips.extend(
                        [clip.with_effects([vfx.FadeIn(0.3)]) for clip in text_clips]
                    )

                blurred_composite = CompositeVideoClip(composite_clips)
                final_video = blurred_composite

            # Generate output path
            input_name = Path(video_path).stem
            output_path = (
                self.brand_outro_output_dir
                / f"{input_name}_brand_outro_A_{uuid.uuid4().hex[:8]}.mp4"
            )

            # Write video
            final_video.write_videofile(
                str(output_path),
                codec="libx264",
                audio_codec="aac",
                temp_audiofile="temp-audio.m4a",
                remove_temp=True,
                logger=None,
            )

            # Cleanup
            video_clip.close()
            final_video.close()
            Path(resized_logo_path).unlink(missing_ok=True)

            return {
                "status": "success",
                "input_video": video_path,
                "output_video": str(output_path),
                "option": "A - Blurred Outro with Semi-Transparent Overlay",
                "blur_duration": blur_last_seconds,
                "overlay_color": overlay_color,
                "overlay_opacity": f"{(1-self.overlay_opacity)*100}% transparent",
                "style_analysis": style_data,
                "brand_name": brand_name,
                "brand_tagline": brand_tagline,
            }

        except Exception as e:
            logger.error(f"Option A failed: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "input_video": video_path,
                "option": "A - Blurred Outro with Semi-Transparent Overlay",
            }

    def create_brand_outro_option_b(
        self,
        video_path: str,
        logo_path: str,
        brand_name: str,
        brand_tagline: str = "",
    ) -> dict:
        """
        Option B: Add 2-second outro with smart background + brand
        """
        try:
            logger.info(f"Creating Brand Outro Option B for: {video_path}")

            # Load video
            video_clip = VideoFileClip(video_path)
            video_size = video_clip.size

            # Get style analysis
            style_data = self.analyze_video_style_with_gemini(video_path)

            # Create smart background
            background_clip = self.create_brand_background(video_size, style_data)

            # Resize logo for outro
            resized_logo_path = self.resize_logo_for_outro(logo_path, video_size)

            # Calculate logo dimensions for text positioning
            with Image.open(resized_logo_path) as logo_img:
                logo_width, logo_height = logo_img.size

            # Create logo clip (centered)
            logo_clip = (
                ImageClip(resized_logo_path, transparent=True)
                .with_duration(self.outro_duration)
                .with_position("center")
            )

            # Create text overlay with smart positioning (only if brand_name is provided)
            text_clips = []
            if brand_name and brand_name.strip():
                text_clip = self.create_brand_text_overlay(
                    video_size,
                    brand_name,
                    brand_tagline,
                    style_data,
                    logo_height,
                    self.outro_duration,
                )

                if text_clip:
                    video_center_x = video_size[0] // 2
                    video_center_y = video_size[1] // 2
                    logo_bottom_y = video_center_y + (logo_height // 2)
                    text_gap = 20
                    text_y_position = logo_bottom_y + text_gap

                    text_clip = text_clip.with_position(("center", text_y_position))
                    text_clips.append(text_clip)

            # Composite outro section
            composite_clips = [
                background_clip,
                logo_clip.with_effects([vfx.FadeIn(0.3)]),
            ]
            if text_clips:
                composite_clips.extend(
                    [clip.with_effects([vfx.FadeIn(0.4)]) for clip in text_clips]
                )

            outro_section = CompositeVideoClip(composite_clips)

            # Concatenate original video + outro
            final_video = concatenate_videoclips([video_clip, outro_section])

            # Generate output path
            input_name = Path(video_path).stem
            output_path = (
                self.brand_outro_output_dir
                / f"{input_name}_brand_outro_B_{uuid.uuid4().hex[:8]}.mp4"
            )

            # Write video
            final_video.write_videofile(
                str(output_path),
                codec="libx264",
                audio_codec="aac",
                temp_audiofile="temp-audio.m4a",
                remove_temp=True,
                logger=None,
            )

            # Cleanup
            video_clip.close()
            final_video.close()
            Path(resized_logo_path).unlink(missing_ok=True)

            return {
                "status": "success",
                "input_video": video_path,
                "output_video": str(output_path),
                "option": "B - Added Outro",
                "outro_duration": self.outro_duration,
                "style_analysis": style_data,
                "brand_name": brand_name,
                "brand_tagline": brand_tagline,
            }

        except Exception as e:
            logger.error(f"Option B failed: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "input_video": video_path,
                "option": "B - Added Outro",
            }

    def create_brand_background(self, video_size: tuple, style_data: dict):
        """
        Create intelligent background based on style analysis.
        """
        width, height = video_size
        background_type = style_data.get("background_type", "solid")
        primary_color = tuple(style_data.get("primary_color", [45, 45, 45]))
        accent_color = tuple(style_data.get("accent_color", [100, 149, 237]))

        if background_type == "solid":
            return ColorClip(size=video_size, color=primary_color, duration=self.outro_duration)

        elif background_type == "gradient":
            # Create gradient background
            img = Image.new("RGB", video_size, primary_color)
            draw = ImageDraw.Draw(img)

            # Create vertical gradient
            for y in range(height):
                # Interpolate between primary and accent color
                ratio = y / height
                r = int(primary_color[0] * (1 - ratio) + accent_color[0] * ratio)
                g = int(primary_color[1] * (1 - ratio) + accent_color[1] * ratio)
                b = int(primary_color[2] * (1 - ratio) + accent_color[2] * ratio)
                draw.line([(0, y), (width, y)], fill=(r, g, b))

            img_array = np.array(img)
            return ImageClip(img_array, duration=self.outro_duration)

        elif background_type == "subtle_pattern":
            # Create subtle geometric pattern
            img = Image.new("RGB", video_size, primary_color)
            draw = ImageDraw.Draw(img)

            # Add subtle dots pattern
            dot_spacing = 60
            dot_size = 3
            for x in range(0, width, dot_spacing):
                for y in range(0, height, dot_spacing):
                    color_var = tuple(min(255, c + 10) for c in primary_color)
                    draw.ellipse([x, y, x + dot_size, y + dot_size], fill=color_var)

            img_array = np.array(img)
            return ImageClip(img_array, duration=self.outro_duration)

        else:
            # Default solid
            return ColorClip(size=video_size, color=primary_color, duration=self.outro_duration)

    def _download_text_overlay_video_from_s3(self, script_id: str) -> str:
        """Download text overlay video from S3 for the given script_id"""
        try:
            # List objects in the text overlay prefix to find the video
            response = self.s3_client.s3_client.list_objects_v2(
                Bucket=self.s3_bucket, Prefix=f"{self.text_overlay_prefix}text_overlay_{script_id}_"
            )

            if "Contents" not in response:
                raise FileNotFoundError(f"No text overlay videos found for script_id: {script_id}")

            # Get the most recent text overlay video
            objects = sorted(response["Contents"], key=lambda x: x["LastModified"], reverse=True)
            if not objects:
                raise FileNotFoundError(f"No text overlay videos found for script_id: {script_id}")

            video_key = objects[0]["Key"]

            # Download to temp directory
            temp_video_path = self.temp_dir / f"text_overlay_video_{script_id}.mp4"

            logger.info(f"Downloading text overlay video from S3: {self.s3_bucket}/{video_key}")
            downloaded_path = self.s3_client.download_file(
                bucket=self.s3_bucket, key=video_key, local_path=str(temp_video_path)
            )

            if os.path.exists(downloaded_path):
                logger.info(f"✓ Successfully downloaded text overlay video: {downloaded_path}")
                return downloaded_path
            else:
                raise FileNotFoundError(f"Downloaded file not found: {downloaded_path}")

        except Exception as e:
            logger.error(f"✗ Error downloading text overlay video from S3: {e}")
            # Fallback to local file system
            local_paths = [
                f"output/text_videos/text_overlay_{script_id}_*.mp4",
                f"output/final_videos/video_{script_id}.mp4",
                f"output/stitched_videos/video_{script_id}.mp4",
            ]

            for local_pattern in local_paths:
                import glob

                found_files = glob.glob(local_pattern)
                if found_files:
                    # Use the most recent file
                    most_recent = max(found_files, key=os.path.getctime)
                    logger.info(f"✓ Using local video: {most_recent}")
                    return most_recent

            raise FileNotFoundError(f"No text overlay video found for script_id: {script_id}")

    def _upload_video_to_s3(self, local_video_path: str, script_id: str, overlay_type: str) -> str:
        """Upload processed video to S3"""
        try:
            # Generate unique filename
            unique_id = str(uuid.uuid4())[:8]
            s3_key = f"{self.image_overlay_prefix}image_overlay_{script_id}_{overlay_type}_{unique_id}.mp4"

            logger.info(f"Uploading image overlay video to S3: {self.s3_bucket}/{s3_key}")

            # Upload to S3
            s3_url = self.s3_client.upload_file(
                bucket=self.s3_bucket, key=s3_key, file_path=local_video_path
            )

            logger.info(f"✓ Successfully uploaded image overlay video to S3: {s3_url}")
            return s3_url

        except Exception as e:
            logger.error(f"✗ Error uploading video to S3: {e}")
            raise

    def process_image_overlay_with_s3(
        self,
        script_id: str,
        logo_path: str,
        brand_name: str,
        brand_tagline: str = "",
        overlay_type: str = "simple",  # "simple", "outro_a", "outro_b"
        position: str = "top-right",
        opacity: float = 0.8,
        margin: int = 20,
    ) -> Dict[str, Any]:
        """Process image overlay with S3 integration - downloads text overlay video and uploads result"""
        try:
            logger.info(f"=== Processing Image Overlay with S3 for Script ID: {script_id} ===")

            # Download text overlay video from S3
            video_path = self._download_text_overlay_video_from_s3(script_id)

            if not os.path.exists(video_path):
                return {
                    "success": False,
                    "error": f"Text overlay video not found: {video_path}",
                }

            logger.info(f"Text overlay video downloaded: {video_path}")

            # Process based on overlay type
            if overlay_type == "simple":
                result = self.add_simple_logo_overlay(
                    video_path=video_path,
                    logo_path=logo_path,
                    position=position,
                    opacity=opacity,
                    margin=margin,
                )
            elif overlay_type == "outro_a":
                result = self.create_brand_outro_option_a(
                    video_path=video_path,
                    logo_path=logo_path,
                    brand_name=brand_name,
                    brand_tagline=brand_tagline,
                )
            elif overlay_type == "outro_b":
                result = self.create_brand_outro_option_b(
                    video_path=video_path,
                    logo_path=logo_path,
                    brand_name=brand_name,
                    brand_tagline=brand_tagline,
                )
            else:
                return {"success": False, "error": f"Invalid overlay type: {overlay_type}"}

            if result.get("status") != "success":
                return {
                    "success": False,
                    "error": result.get("message", "Image overlay processing failed"),
                }

            # Upload result to S3
            s3_url = self._upload_video_to_s3(result["output_video"], script_id, overlay_type)

            # Clean up local file after S3 upload
            if os.path.exists(result["output_video"]):
                os.remove(result["output_video"])

            return {
                "success": True,
                "s3_url": s3_url,
                "script_id": script_id,
                "overlay_type": overlay_type,
                "brand_name": brand_name,
                "brand_tagline": brand_tagline,
                "message": f"Successfully processed {overlay_type} image overlay",
                "local_result": result,
            }

        except Exception as e:
            logger.error(f"✗ Error processing image overlay with S3: {e}")
            return {"success": False, "error": str(e)}


def create_brand_outro_option_b(
    video_path: str,
    logo_path: str,
    brand_name: str,
    brand_tagline: str = "",
) -> tuple[str, str]:
    return enhanced_brand_overlay_processor.create_brand_outro_option_b(
        video_path, logo_path, brand_name, brand_tagline
    )


def get_brand_logo_preview_enhanced(logo_path: str) -> str:
    return enhanced_brand_overlay_processor.get_brand_logo_preview_enhanced(logo_path)


enhanced_brand_overlay_processor = EnhancedBrandImageOverlayProcessor()


def apply_simple_logo_overlay_enhanced(
    video_path: str,
    logo_path: str,
    position: str = "top-right",
    opacity: float = 0.8,
    margin: int = 20,
) -> tuple[str, str]:
    result = enhanced_brand_overlay_processor.add_simple_logo_overlay(
        video_path, logo_path, position, opacity, margin
    )
    if result["status"] == "success":
        return result["status"], result["output_video"]
    else:
        return result["status"], None


def apply_brand_outro_option_a(
    video_path: str,
    logo_path: str,
    brand_name: str,
    brand_tagline: str = "",
    blur_duration: float = 3.0,
) -> tuple[str, str]:
    result = enhanced_brand_overlay_processor.create_brand_outro_option_a(
        video_path, logo_path, brand_name, brand_tagline, blur_duration
    )
    if result["status"] == "success":
        return result["status"], result["output_video"]
    else:
        return result["status"], None


def apply_brand_outro_option_b(
    video_path: str,
    logo_path: str,
    brand_name: str,
    brand_tagline: str = "",
) -> tuple[str, str]:
    result = enhanced_brand_overlay_processor.create_brand_outro_option_b(
        video_path, logo_path, brand_name, brand_tagline
    )
    if result["status"] == "success":
        return result["status"], result["output_video"]
    else:
        return result["status"], None
