from fastapi import APIRouter, HTTPException, Depends
from src.image_overlay_service.services.overlay_processor import (
    apply_simple_logo_overlay_enhanced,
    apply_brand_outro_option_a,
    apply_brand_outro_option_b,
    EnhancedBrandImageOverlayProcessor,
)
from src.image_overlay_service.schemas.requests import (
    SimpleOverlayRequest,
    OptionARequest,
    OptionBRequest,
)
from src.image_overlay_service.schemas.responses import OverlayResponse
from src.shared.utils.s3_client import S3Client
from src.shared.models.database_models import VideoAsset, Script
from src.shared.config.database import get_database_session
from src.shared.core.dependencies import require_auth
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, and_
from datetime import datetime, timedelta
import os
import uuid
import base64
import tempfile
import traceback
from loguru import logger

router = APIRouter(prefix="/image-overlay", tags=["Image Overlay"])

S3_BUCKET = "assets-vidflux"
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)


async def get_or_generate_text_overlay_video_for_script(
    session: AsyncSession, script_id: str
) -> str:
    """Get text overlay video from S3 for the given script_id"""
    try:
        # List objects in the text overlay prefix to find the video
        response = s3_client.s3_client.list_objects_v2(
            Bucket=S3_BUCKET, Prefix=f"Vidflux-Assets/text_overlay_assets/text_overlay_{script_id}_"
        )

        if "Contents" not in response:
            raise HTTPException(
                status_code=404, detail=f"No text overlay video found for script {script_id}"
            )

        # Get the most recent text overlay video
        objects = sorted(response["Contents"], key=lambda x: x["LastModified"], reverse=True)
        if not objects:
            raise HTTPException(
                status_code=404, detail=f"No text overlay video found for script {script_id}"
            )

        video_key = objects[0]["Key"]

        # Download to local directory
        local_dir = "output/text_videos"
        os.makedirs(local_dir, exist_ok=True)
        local_path = os.path.join(local_dir, os.path.basename(video_key))

        if not os.path.exists(local_path):
            s3_client.download_file(S3_BUCKET, video_key, local_path=local_path)

        return local_path

    except Exception as e:
        logger.error(f"Error getting text overlay video: {e}")
        raise HTTPException(
            status_code=404, detail=f"Failed to get text overlay video for script {script_id}"
        )


def decode_logo_base64(logo_base64: str) -> str:
    try:
        # Remove any data URL prefix if present
        if logo_base64.startswith("data:"):
            logo_base64 = logo_base64.split(",")[1]

        # Decode base64
        logo_data = base64.b64decode(logo_base64)

        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png", mode="wb")
        temp_file.write(logo_data)
        temp_file.close()

        # Verify the file can be opened
        from PIL import Image

        with Image.open(temp_file.name) as img:
            img.verify()

        return temp_file.name
    except Exception as e:
        logger.error(f"Error decoding logo base64: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid logo data: {str(e)}")


async def validate_script_access(
    session: AsyncSession, script_id: str, org_id: str, user_id: str
) -> bool:
    """Validate that the script belongs to the authenticated org (not user)"""
    stmt = select(Script).where(
        and_(
            Script.id == script_id,
            Script.org_id == org_id,
            Script.status.in_(["active", "pending"]),  # Allow both active and pending scripts
        )
    )
    result = await session.execute(stmt)
    script = result.scalar_one_or_none()
    return script is not None


async def store_video_asset(session: AsyncSession, s3_key: str, org_id: str = "default"):
    asset_id = str(uuid.uuid4())
    video_asset = VideoAsset(
        asset_id=asset_id,
        org_id=org_id,
        s3_url=s3_key,
        local_path=None,
        generation_method="image_overlay",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    session.add(video_asset)
    await session.commit()
    return asset_id


@router.post("/simple", response_model=OverlayResponse)
async def simple_logo_overlay(
    request: SimpleOverlayRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    try:
        org_id, user_id, email = user_info

        # Validate script access
        if not await validate_script_access(session, request.script_id, org_id, user_id):
            raise HTTPException(status_code=403, detail="Access denied to this script")

        video_path = await get_or_generate_text_overlay_video_for_script(session, request.script_id)
        logo_path = decode_logo_base64(request.logo_base64)

        result = apply_simple_logo_overlay_enhanced(
            video_path, logo_path, request.position, request.opacity, request.margin
        )

        if isinstance(result, tuple):
            message, output_path = result
            if not output_path:
                return OverlayResponse(status="error", message=message)
        else:
            output_path = result.get("output_video")
            message = result.get("status")
        filename = os.path.basename(output_path)
        s3_key = f"Vidflux-Assets/image-overlay/{filename}"
        s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=output_path)
        logger.info(f" Generating presigned URL for: {s3_key}")
        presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
        logger.info(f" Storing video asset in DB...")
        await store_video_asset(session, s3_key, org_id)
        logger.info(f" Success! Returning response.")
        return OverlayResponse(
            status="success", message=message, s3_key=s3_key, presigned_url=presigned_url
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(traceback.format_exc())
        return OverlayResponse(status="error", message=str(e))


@router.post("/option-a", response_model=OverlayResponse)
async def brand_outro_option_a(
    request: OptionARequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    try:
        org_id, user_id, email = user_info

        # Validate script access
        if not await validate_script_access(session, request.script_id, org_id, user_id):
            raise HTTPException(status_code=403, detail="Access denied to this script")

        logger.info(f" Fetching text overlay video for script_id: {request.script_id}")
        video_path = await get_or_generate_text_overlay_video_for_script(session, request.script_id)
        logger.info(f" Text overlay video path: {video_path}")
        logo_path = decode_logo_base64(request.logo_base64)
        logger.info(f" Logo path: {logo_path}")
        result = apply_brand_outro_option_a(
            video_path, logo_path, request.brand_name, request.brand_tagline, request.blur_duration
        )
        logger.info(f" Overlay processor result: {result}")
        if isinstance(result, tuple):
            message, output_path = result
            logger.info(f" Output path: {output_path}")
            if not output_path:
                logger.info(f" No output path returned. Message: {message}")
                return OverlayResponse(status="error", message=message)
        else:
            output_path = result.get("output_video")
            message = result.get("status")
        logger.info(f" Uploading to S3: {output_path}")
        filename = os.path.basename(output_path)
        s3_key = f"Vidflux-Assets/image-overlay/{filename}"
        s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=output_path)
        logger.info(f" Generating presigned URL for: {s3_key}")
        presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
        logger.info(f" Storing video asset in DB...")
        await store_video_asset(session, s3_key, org_id)
        logger.info(f" Success! Returning response.")
        return OverlayResponse(
            status="success", message=message, s3_key=s3_key, presigned_url=presigned_url
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(traceback.format_exc())
        return OverlayResponse(status="error", message=str(e))


@router.post("/option-b", response_model=OverlayResponse)
async def brand_outro_option_b(
    request: OptionBRequest,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    try:
        org_id, user_id, email = user_info

        # Validate script access
        if not await validate_script_access(session, request.script_id, org_id, user_id):
            raise HTTPException(status_code=403, detail="Access denied to this script")

        logger.info(f" Fetching text overlay video for script_id: {request.script_id}")
        video_path = await get_or_generate_text_overlay_video_for_script(session, request.script_id)
        logger.info(f" Text overlay video path: {video_path}")
        logo_path = decode_logo_base64(request.logo_base64)
        logger.info(f" Logo path: {logo_path}")
        result = apply_brand_outro_option_b(
            video_path, logo_path, request.brand_name, request.brand_tagline
        )
        logger.info(f" Overlay processor result: {result}")
        if isinstance(result, tuple):
            message, output_path = result
            logger.info(f" Output path: {output_path}")
            if not output_path:
                logger.info(f" No output path returned. Message: {message}")
                return OverlayResponse(status="error", message=message)
        else:
            output_path = result.get("output_video")
            message = result.get("status")
        logger.info(f" Uploading to S3: {output_path}")
        filename = os.path.basename(output_path)
        s3_key = f"Vidflux-Assets/image-overlay/{filename}"
        s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=output_path)
        logger.info(f" Generating presigned URL for: {s3_key}")
        presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
        logger.info(f" Storing video asset in DB...")
        await store_video_asset(session, s3_key, org_id)
        logger.info(f" Success! Returning response.")
        return OverlayResponse(
            status="success", message=message, s3_key=s3_key, presigned_url=presigned_url
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(traceback.format_exc())
        return OverlayResponse(status="error", message=str(e))
