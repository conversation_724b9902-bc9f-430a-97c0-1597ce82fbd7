"""
🔐 **VidFlux Authentication API Routes**

Handles user authentication, authorization, and account management including:
- User registration with OTP verification
- Email/password and OAuth login
- JWT token management and refresh
- User profile access
"""

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import uuid
from datetime import datetime, timedelta
from src.shared.config.database import get_database_session
from src.shared.models.database_models import (
    User,
    PendingUser,
    Organization,
    UserSignin,
    RefreshToken,
)
from src.shared.core.security import JWTManager
from src.shared.config.settings import settings
from src.auth_service.schemas.auth_schemas import (
    SignupRequest,
    OTPVerify,
    LoginRequest,
    GoogleOAuthRequest,
    TokenResponse,
)
from src.shared.schemas.auth import UserResponse
from src.auth_service.core.password import hash_password, verify_password
from src.auth_service.core.otp import generate_and_store_otp, verify_otp
from src.auth_service.core.oauth import verify_google_token
from sqlalchemy import insert, update

router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
    responses={
        400: {"description": "Bad Request - Invalid input parameters"},
        401: {"description": "Unauthorized - Invalid credentials"},
        403: {"description": "Forbidden - Account disabled or insufficient permissions"},
        422: {"description": "Validation Error - Request body validation failed"},
        500: {"description": "Internal Server Error - Server-side failures"},
    },
)


@router.post(
    "/request-otp",
    status_code=200,
    summary="Request OTP for user signup",
    description="Initiates user registration by generating and sending an OTP to the provided email",
    response_description="OTP generation confirmation with the OTP code (for development)",
)
async def request_signup_otp(
    data: SignupRequest, session: AsyncSession = Depends(get_database_session)
):
    """
    🎯 **Purpose**: Initiate user registration process with OTP verification

    📝 **Description**:
    Creates a pending user account and generates a 6-digit OTP for email verification.
    The user data is temporarily stored until OTP verification is completed.

    🔐 **Authentication**: Not required (public endpoint)

    🔄 **Process Flow**:
    1. Validate that email is not already registered
    2. Remove any existing pending registration for this email
    3. Hash the password and store user data temporarily
    4. Generate a 6-digit OTP and store it with expiration
    5. Send OTP via email to user

    📥 **Request Parameters**:
    - **Request Body**: SignupRequest with user details and organization info

    📤 **Response Format**:
    - **Success (200)**: Confirmation message that OTP was sent to email
    - **Error (400)**: Email already registered
    - **Error (422)**: Validation errors for input fields

    ⚠️ **Error Handling**:
    - **400**: Email is already registered with an active account
    - **422**: Invalid email format, password too short, or missing required fields
    - **500**: Database errors or OTP generation failures

    🔗 **Dependencies**:
    - PostgreSQL for user and pending_user storage
    - Password hashing utility
    - OTP generation and storage system

    📊 **Business Rules**:
    - Email addresses must be unique across the system
    - Passwords must be at least 8 characters long
    - OTP expires after 10 minutes
    - Only one pending registration per email at a time

    💡 **Examples**:

    **Request Example**:
    ```json
    {
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "first_name": "John",
        "last_name": "Doe",
        "org_name": "Acme Corporation"
    }
    ```

    **Success Response Example**:
    ```json
    {
        "message": "OTP sent successfully to your email"
    }
    ```

    **Error Response Example**:
    ```json
    {
        "detail": "Email already registered"
    }
    ```

    📅 **Version**: v2.0.0
    📚 **Related Endpoints**:
    - POST /auth/verify-otp - Complete registration with OTP
    - POST /auth/login - Login after registration
    """

    # Check if user already exists
    stmt = select(User).where(User.user_email == data.email)
    result = await session.execute(stmt)
    existing_user = result.scalar_one_or_none()

    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Remove existing pending user if any
    stmt = select(PendingUser).where(PendingUser.email == data.email)
    result = await session.execute(stmt)
    existing_pending = result.scalar_one_or_none()

    if existing_pending:
        await session.delete(existing_pending)
        await session.commit()

    # Create new pending user
    pending_user = PendingUser(
        email=data.email,
        password_hash=hash_password(data.password),
        first_name=data.first_name,
        last_name=data.last_name,
        org_name=data.org_name,
    )

    session.add(pending_user)
    await session.commit()

    # Generate OTP and send email
    await generate_and_store_otp(data.email, data.first_name)

    return {"message": "OTP sent successfully to your email"}


@router.post(
    "/verify-otp",
    response_model=TokenResponse,
    summary="Verify OTP and complete user registration",
    description="Completes user registration by verifying OTP and creating user account with organization",
    response_description="JWT tokens for authenticated access",
)
async def verify_signup_otp(data: OTPVerify, session: AsyncSession = Depends(get_database_session)):
    """
    🎯 **Purpose**: Complete user registration by verifying OTP and creating account

    📝 **Description**:
    Verifies the OTP code and creates the final user account with organization.
    Returns JWT tokens for immediate authenticated access to the platform.

    🔐 **Authentication**: Not required (public endpoint)

    🔄 **Process Flow**:
    1. Verify the provided OTP code against stored value
    2. Retrieve pending user data from temporary storage
    3. Create new organization with unique ID
    4. Create verified user account with organization association
    5. Generate signin record for audit trail
    6. Create JWT access and refresh tokens
    7. Clean up pending user data

    📥 **Request Parameters**:
    - **Request Body**: OTPVerify with email and 6-digit OTP code

    📤 **Response Format**:
    - **Success (200)**: TokenResponse with access and refresh tokens
    - **Error (400)**: Invalid or expired OTP
    - **Error (404)**: Signup data not found (expired session)

    ⚠️ **Error Handling**:
    - **400**: Invalid OTP, expired OTP, or OTP verification failed
    - **404**: Pending user data not found (may have expired)
    - **422**: Invalid email format or OTP format
    - **500**: Database errors during account creation

    🔗 **Dependencies**:
    - PostgreSQL for user, organization, and signin storage
    - JWT token generation system
    - OTP verification system

    📊 **Business Rules**:
    - OTP codes expire after 10 minutes
    - Each OTP can only be used once
    - Organization ID is auto-generated with "org-" prefix
    - User accounts are created as active and verified
    - Access tokens expire in 24 hours, refresh tokens in 7 days

    💡 **Examples**:

    **Request Example**:
    ```json
    {
        "email": "<EMAIL>",
        "otp": "123456"
    }
    ```

    **Success Response Example**:
    ```json
    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```

    **Error Response Example**:
    ```json
    {
        "detail": "Invalid or expired OTP"
    }
    ```

    📅 **Version**: v2.0.0
    📚 **Related Endpoints**:
    - POST /auth/request-otp - Initial registration step
    - POST /auth/login - Login with created account
    - GET /auth/me - Get user profile with tokens
    """

    # Verify OTP
    if not await verify_otp(data.email, data.otp):
        raise HTTPException(status_code=400, detail="Invalid or expired OTP")

    # Get pending user
    stmt = select(PendingUser).where(PendingUser.email == data.email)
    result = await session.execute(stmt)
    pending = result.scalar_one_or_none()

    if not pending:
        raise HTTPException(status_code=404, detail="Signup data not found")

    # Create organization
    org_id = f"org-{uuid.uuid4().hex[:8]}"
    organization = Organization(org_id=org_id, org_name=pending.org_name)
    session.add(organization)
    await session.flush()  # Get the org_id

    # Create user
    user_id = uuid.uuid4()
    user = User(
        org_id=org_id,
        user_id=user_id,
        user_email=pending.email,
        password_hash=pending.password_hash,
        first_name=pending.first_name,
        last_name=pending.last_name,
        is_verified=True,
    )
    session.add(user)

    # Create signin record
    signin = UserSignin(org_id=org_id, user_id=user_id)
    session.add(signin)

    # Remove pending user
    await session.delete(pending)
    await session.commit()

    # Generate JWT tokens
    access_token = JWTManager.create_user_token(org_id, str(user_id), pending.email)
    refresh_token_str = JWTManager.create_refresh_token(org_id, str(user_id), pending.email)
    # Save refresh token in DB
    expires_at = datetime.utcnow() + timedelta(days=7)
    stmt = (
        insert(RefreshToken)
        .values(org_id=org_id, user_id=user_id, token=refresh_token_str, expires_at=expires_at)
        .returning(RefreshToken.created_at, RefreshToken.updated_at)
    )
    result = await session.execute(stmt)
    await session.commit()
    created_at, updated_at = result.fetchone()

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token_str,
        token_type="bearer",
        expires_in=settings.jwt_access_token_expire_minutes * 60,
        user_info={
            "org_id": org_id,
            "user_id": str(user_id),
            "email": pending.email,
            "first_name": pending.first_name,
            "last_name": pending.last_name,
        },
        refresh_token_created_at=created_at,
        refresh_token_updated_at=updated_at,
    )


@router.post(
    "/login",
    response_model=TokenResponse,
    summary="User login with email and password",
    description="Authenticate user with email/password and return JWT tokens",
    response_description="JWT tokens for authenticated access",
)
async def login_user(data: LoginRequest, session: AsyncSession = Depends(get_database_session)):
    """
    🎯 **Purpose**: Authenticate user with email and password credentials

    📝 **Description**:
    Validates user credentials and returns JWT tokens for authenticated access.
    Creates a signin record for audit purposes and session tracking.

    🔐 **Authentication**: Not required (public endpoint)

    🔄 **Process Flow**:
    1. Look up user account by email address
    2. Verify provided password against stored hash
    3. Check if user account is active
    4. Create signin record for audit trail
    5. Generate JWT access and refresh tokens
    6. Store refresh token in database with expiration

    📥 **Request Parameters**:
    - **Request Body**: LoginRequest with email and password

    📤 **Response Format**:
    - **Success (200)**: TokenResponse with access and refresh tokens
    - **Error (401)**: Invalid credentials or disabled account

    ⚠️ **Error Handling**:
    - **401**: Invalid email/password combination
    - **401**: Account is disabled or inactive
    - **422**: Invalid email format or missing required fields
    - **500**: Database errors during authentication

    🔗 **Dependencies**:
    - PostgreSQL for user lookup and signin records
    - Password verification utility (bcrypt)
    - JWT token generation system
    - Refresh token storage system

    📊 **Business Rules**:
    - Password verification uses secure bcrypt hashing
    - Signin records are created for all successful logins
    - Access tokens expire in 24 hours (1440 minutes)
    - Refresh tokens expire in 7 days
    - Disabled accounts cannot login

    💡 **Examples**:

    **Request Example**:
    ```json
    {
        "email": "<EMAIL>",
        "password": "SecurePass123!"
    }
    ```

    **Success Response Example**:
    ```json
    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```

    **Error Response Example**:
    ```json
    {
        "detail": "Invalid credentials"
    }
    ```

    📅 **Version**: v2.0.0
    📚 **Related Endpoints**:
    - POST /auth/request-otp - User registration
    - POST /auth/refresh - Refresh access token
    - GET /auth/me - Get authenticated user profile
    """

    # Find user
    stmt = select(User).where(User.user_email == data.email)
    result = await session.execute(stmt)
    user = result.scalar_one_or_none()

    if not user or not verify_password(data.password, user.password_hash):
        raise HTTPException(status_code=401, detail="Invalid credentials")

    if not user.is_active:
        raise HTTPException(status_code=401, detail="Account is disabled")

    # Create signin record
    signin = UserSignin(org_id=user.org_id, user_id=user.user_id)
    session.add(signin)
    await session.commit()

    # Generate JWT tokens
    access_token = JWTManager.create_user_token(user.org_id, str(user.user_id), user.user_email)
    refresh_token_str = JWTManager.create_refresh_token(
        user.org_id, str(user.user_id), user.user_email
    )
    expires_at = datetime.utcnow() + timedelta(days=7)
    stmt = (
        insert(RefreshToken)
        .values(
            org_id=user.org_id, user_id=user.user_id, token=refresh_token_str, expires_at=expires_at
        )
        .returning(RefreshToken.created_at, RefreshToken.updated_at)
    )
    result = await session.execute(stmt)
    await session.commit()
    created_at, updated_at = result.fetchone()

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token_str,
        token_type="bearer",
        expires_in=settings.jwt_access_token_expire_minutes * 60,
        user_info={
            "org_id": user.org_id,
            "user_id": str(user.user_id),
            "email": user.user_email,
            "first_name": user.first_name,
            "last_name": user.last_name,
        },
        refresh_token_created_at=created_at,
        refresh_token_updated_at=updated_at,
    )


@router.post("/google", response_model=TokenResponse)
async def google_oauth_login(
    data: GoogleOAuthRequest, session: AsyncSession = Depends(get_database_session)
):
    """Google OAuth login"""

    # Verify Google token
    google_info = verify_google_token(data.token)
    email = google_info["email"]

    # Check if user exists
    stmt = select(User).where(User.user_email == email)
    result = await session.execute(stmt)
    user = result.scalar_one_or_none()

    if user:
        # Existing user login
        if not user.is_active:
            raise HTTPException(status_code=401, detail="Account is disabled")

        # Create signin record
        signin = UserSignin(org_id=user.org_id, user_id=user.user_id)
        session.add(signin)
        await session.commit()

        # Generate JWT tokens
        access_token = JWTManager.create_user_token(user.org_id, str(user.user_id), user.user_email)
        refresh_token_str = JWTManager.create_refresh_token(
            user.org_id, str(user.user_id), user.user_email
        )
        expires_at = datetime.utcnow() + timedelta(days=7)
        stmt = (
            insert(RefreshToken)
            .values(
                org_id=user.org_id,
                user_id=user.user_id,
                token=refresh_token_str,
                expires_at=expires_at,
            )
            .returning(RefreshToken.created_at, RefreshToken.updated_at)
        )
        result = await session.execute(stmt)
        await session.commit()
        created_at, updated_at = result.fetchone()

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token_str,
            token_type="bearer",
            expires_in=settings.jwt_access_token_expire_minutes * 60,
            user_info={
                "org_id": user.org_id,
                "user_id": str(user.user_id),
                "email": user.user_email,
                "first_name": user.first_name,
                "last_name": user.last_name,
            },
            refresh_token_created_at=created_at,
            refresh_token_updated_at=updated_at,
        )

    else:
        # New user registration
        # Create organization
        org_id = f"org-{uuid.uuid4().hex[:8]}"
        organization = Organization(
            org_id=org_id, org_name=f"{google_info['first_name']}'s Organization"
        )
        session.add(organization)
        await session.flush()

        # Create user
        user_id = uuid.uuid4()
        user = User(
            org_id=org_id,
            user_id=user_id,
            user_email=email,
            first_name=google_info["first_name"],
            last_name=google_info["last_name"],
            is_verified=True,
        )
        session.add(user)

        # Create signin record
        signin = UserSignin(org_id=org_id, user_id=user_id)
        session.add(signin)
        await session.commit()

        # Generate JWT tokens
        access_token = JWTManager.create_user_token(org_id, str(user_id), email)
        refresh_token_str = JWTManager.create_refresh_token(org_id, str(user_id), email)
        expires_at = datetime.utcnow() + timedelta(days=7)
        stmt = (
            insert(RefreshToken)
            .values(org_id=org_id, user_id=user_id, token=refresh_token_str, expires_at=expires_at)
            .returning(RefreshToken.created_at, RefreshToken.updated_at)
        )
        result = await session.execute(stmt)
        await session.commit()
        created_at, updated_at = result.fetchone()

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token_str,
            token_type="bearer",
            expires_in=settings.jwt_access_token_expire_minutes * 60,
            user_info={
                "org_id": org_id,
                "user_id": str(user_id),
                "email": email,
                "first_name": google_info["first_name"],
                "last_name": google_info["last_name"],
            },
            refresh_token_created_at=created_at,
            refresh_token_updated_at=updated_at,
        )


@router.post(
    "/refresh",
    response_model=TokenResponse,
    summary="Refresh access token",
    description="Generate new access token using valid refresh token",
    response_description="New JWT tokens with extended validity",
)
async def refresh_access_token(
    refresh_token: str = Body(..., embed=True),
    session: AsyncSession = Depends(get_database_session),
):
    """
    🎯 **Purpose**: Generate new access token using valid refresh token

    📝 **Description**:
    Validates refresh token and generates new access/refresh token pair.
    Used to maintain authenticated sessions without requiring re-login.

    🔐 **Authentication**: Requires valid refresh token

    🔄 **Process Flow**:
    1. Validate and decode refresh token
    2. Verify token type is "refresh"
    3. Check refresh token exists in database
    4. Verify associated user account is still active
    5. Generate new access and refresh tokens
    6. Update refresh token in database
    7. Return new token pair

    📥 **Request Parameters**:
    - **Request Body**: JSON with refresh_token string field

    📤 **Response Format**:
    - **Success (200)**: TokenResponse with new access and refresh tokens
    - **Error (401)**: Invalid, expired, or malformed refresh token
    - **Error (404)**: User account not found

    ⚠️ **Error Handling**:
    - **401**: Invalid refresh token format or signature
    - **401**: Expired refresh token
    - **401**: Token type is not "refresh"
    - **401**: Refresh token not found in database
    - **401**: Associated user account is disabled
    - **422**: Missing refresh_token in request body
    - **500**: Database errors during token refresh

    🔗 **Dependencies**:
    - JWT token verification and generation system
    - PostgreSQL for refresh token and user lookup
    - Token signature validation

    📊 **Business Rules**:
    - Refresh tokens are single-use (new one generated each time)
    - Access tokens expire in 24 hours
    - Refresh tokens expire in 7 days
    - Disabled user accounts cannot refresh tokens
    - Only "refresh" type tokens are accepted

    💡 **Examples**:

    **Request Example**:
    ```json
    {
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```

    **Success Response Example**:
    ```json
    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```

    **Error Response Example**:
    ```json
    {
        "detail": "Invalid refresh token"
    }
    ```

    📅 **Version**: v2.0.0
    📚 **Related Endpoints**:
    - POST /auth/login - Login to get initial tokens
    - GET /auth/me - Use access token for authenticated requests
    """
    from src.shared.core.security import verify_token, JWTManager

    try:
        payload = verify_token(refresh_token)
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid refresh token")
    if payload.get("type") != "refresh":
        raise HTTPException(status_code=401, detail="Invalid token type")
    org_id = payload.get("org_id")
    user_id = payload.get("user_id")
    email = payload.get("sub")
    if not all([org_id, user_id, email]):
        raise HTTPException(status_code=401, detail="Invalid token payload")

    # Validate refresh token in DB
    stmt = select(RefreshToken).where(
        RefreshToken.token == refresh_token, RefreshToken.revoked == False
    )
    result = await session.execute(stmt)
    db_token = result.scalar_one_or_none()
    if not db_token:
        raise HTTPException(status_code=401, detail="Refresh token not found or revoked")
    # Update last_used_at and updated_at
    await session.execute(
        update(RefreshToken)
        .where(RefreshToken.token == refresh_token)
        .values(last_used_at=datetime.utcnow())
    )
    await session.commit()

    access_token = JWTManager.create_user_token(org_id, user_id, email)
    new_refresh_token_str = JWTManager.create_refresh_token(org_id, user_id, email)
    expires_at = datetime.utcnow() + timedelta(days=7)
    stmt = (
        insert(RefreshToken)
        .values(org_id=org_id, user_id=user_id, token=new_refresh_token_str, expires_at=expires_at)
        .returning(RefreshToken.created_at, RefreshToken.updated_at)
    )
    result = await session.execute(stmt)
    await session.commit()
    created_at, updated_at = result.fetchone()

    return TokenResponse(
        access_token=access_token,
        refresh_token=new_refresh_token_str,
        token_type="bearer",
        expires_in=settings.jwt_access_token_expire_minutes * 60,
        user_info={"org_id": org_id, "user_id": user_id, "email": email},
        refresh_token_created_at=created_at,
        refresh_token_updated_at=updated_at,
    )


@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get current user profile",
    description="Retrieve authenticated user's profile information",
    response_description="User profile data including personal and organization details",
)
async def get_current_user_profile(
    session: AsyncSession = Depends(get_database_session),
    user_info: tuple = Depends(lambda: None),  # Will be implemented with auth dependency
):
    """
    🎯 **Purpose**: Retrieve authenticated user's profile information

    📝 **Description**:
    Returns the current user's profile data including personal information,
    organization details, and account status. Requires valid JWT token.

    🔐 **Authentication**: Required (Bearer token in Authorization header)

    🔄 **Process Flow**:
    1. Validate JWT token from Authorization header
    2. Extract user and organization IDs from token
    3. Look up user record in database
    4. Retrieve associated organization information
    5. Return formatted user profile data

    📥 **Request Parameters**:
    - **Headers**: Authorization: Bearer {access_token}
    - **No request body required**

    📤 **Response Format**:
    - **Success (200)**: UserResponse with profile information
    - **Error (401)**: Invalid or expired access token
    - **Error (404)**: User account not found

    ⚠️ **Error Handling**:
    - **401**: Missing Authorization header
    - **401**: Invalid or malformed JWT token
    - **401**: Expired access token
    - **404**: User account no longer exists
    - **500**: Database errors during profile lookup

    🔗 **Dependencies**:
    - JWT token validation system
    - PostgreSQL for user and organization data
    - Authentication middleware

    📊 **Business Rules**:
    - Only returns data for the authenticated user
    - Sensitive information (password hash) is excluded
    - Organization information is included if user belongs to one
    - Account status and verification status included

    💡 **Examples**:

    **Request Example**:
    ```http
    GET /auth/me
    Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
    ```

    **Success Response Example**:
    ```json
    {
        "user_id": "2f8a12bc-7f78-4fc3-b12c-feb3bf49d915",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "org_id": "org-9267a092",
        "org_name": "Acme Corporation",
        "is_active": true,
        "is_verified": true,
        "created_at": "2025-01-15T10:30:00Z"
    }
    ```

    **Error Response Example**:
    ```json
    {
        "detail": "Invalid or expired token"
    }
    ```

    📅 **Version**: v2.0.0
    📚 **Related Endpoints**:
    - POST /auth/login - Login to get access token
    - POST /auth/refresh - Refresh expired token
    """
    # This will be implemented when we add the auth dependency
    raise HTTPException(status_code=501, detail="Not implemented yet")
