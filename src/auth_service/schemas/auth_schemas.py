"""
🔐 **VidFlux Authentication Schemas**

Pydantic models for authentication endpoints including signup, login,
OTP verification, and OAuth integration.
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime


class SignupRequest(BaseModel):
    """
    📝 **Description**: User signup request with organization creation

    🔍 **Validation Rules**:
    - Email must be valid format
    - Password minimum 8 characters
    - Names must be 1-100 characters
    - Organization name 1-200 characters
    """

    email: EmailStr = Field(
        ...,
        description="Valid email address for the new user account",
        example="<EMAIL>",
    )

    password: str = Field(
        ...,
        min_length=8,
        description="Password must be at least 8 characters long",
        example="SecurePass123!",
    )

    first_name: str = Field(
        ..., min_length=1, max_length=100, description="User's first name", example="<PERSON>"
    )

    last_name: str = Field(
        ..., min_length=1, max_length=100, description="User's last name", example="Doe"
    )

    org_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="Organization name for the new account",
        example="Acme Corporation",
    )

    @validator("password")
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        return v

    class Config:
        """Configuration for SignupRequest model"""

        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "John",
                "last_name": "Doe",
                "org_name": "Acme Corporation",
            }
        }


class OTPRequest(BaseModel):
    """
    📝 **Description**: OTP request for email verification

    🔍 **Validation Rules**: Valid email format required
    """

    email: EmailStr = Field(
        ..., description="Email address to send OTP to", example="<EMAIL>"
    )

    class Config:
        """Configuration for OTPRequest model"""

        schema_extra = {"example": {"email": "<EMAIL>"}}


class OTPVerify(BaseModel):
    """
    📝 **Description**: OTP verification for completing signup

    🔍 **Validation Rules**:
    - Valid email format
    - OTP must be exactly 6 characters
    """

    email: EmailStr = Field(
        ..., description="Email address associated with the OTP", example="<EMAIL>"
    )

    otp: str = Field(
        ...,
        min_length=6,
        max_length=6,
        description="6-digit OTP code received via email",
        example="123456",
    )

    class Config:
        """Configuration for OTPVerify model"""

        schema_extra = {"example": {"email": "<EMAIL>", "otp": "123456"}}


class LoginRequest(BaseModel):
    """
    📝 **Description**: User login request with email and password

    🔍 **Validation Rules**: Valid email format required
    """

    email: EmailStr = Field(
        ..., description="User's registered email address", example="<EMAIL>"
    )

    password: str = Field(..., description="User's password", example="SecurePass123!")

    class Config:
        """Configuration for LoginRequest model"""

        schema_extra = {"example": {"email": "<EMAIL>", "password": "SecurePass123!"}}


class GoogleOAuthRequest(BaseModel):
    """
    📝 **Description**: Google OAuth login request

    🔍 **Validation Rules**: Valid Google OAuth token required
    """

    token: str = Field(
        ...,
        description="Google OAuth ID token from client-side authentication",
        example="eyJhbGciOiJSUzI1NiIsImtpZCI6...",
    )

    class Config:
        """Configuration for GoogleOAuthRequest model"""

        schema_extra = {"example": {"token": "eyJhbGciOiJSUzI1NiIsImtpZCI6..."}}


class TokenResponse(BaseModel):
    """
    📝 **Description**: Authentication token response

    🎯 **Purpose**: Returns JWT tokens for authenticated access
    """

    access_token: str = Field(
        ...,
        description="JWT access token for API authentication (expires in 24 hours)",
        example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    )

    refresh_token: Optional[str] = Field(
        None,
        description="JWT refresh token for obtaining new access tokens (expires in 7 days)",
        example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    )
    token_type: str = "bearer"
    expires_in: int
    user_info: dict
    refresh_token_created_at: Optional[datetime] = None
    refresh_token_updated_at: Optional[datetime] = None


# Shared UserResponse schema has been moved to src/shared/schemas/auth.py


class PasswordResetRequest(BaseModel):
    """Password reset request"""

    email: EmailStr


class PasswordResetVerify(BaseModel):
    """Password reset verification"""

    email: EmailStr
    otp: str = Field(..., min_length=6, max_length=6)
    new_password: str = Field(..., min_length=8)
