"""
OAuth integration (Google, etc.)
"""

from fastapi import HTTP<PERSON><PERSON><PERSON>
from google.oauth2 import id_token
from google.auth.transport import requests
from typing import Dict, Optional
from src.shared.config.settings import settings


def verify_google_token(token: str) -> Dict[str, str]:
    """Verify Google OAuth token"""
    if not settings.google_client_id:
        raise HTTPException(status_code=500, detail="Google OAuth not configured")

    try:
        # Verify the token
        info = id_token.verify_oauth2_token(token, requests.Request(), settings.google_client_id)

        email = info["email"]
        name = info.get("name", "")

        # Check if email domain is allowed
        if settings.allowed_email_domains:
            domain = email.split("@")[-1]
            if domain not in settings.allowed_email_domains:
                raise HTTPException(status_code=403, detail="Email domain not allowed")

        return {
            "email": email,
            "name": name,
            "first_name": info.get("given_name", ""),
            "last_name": info.get("family_name", ""),
        }

    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid Google token")


def get_email_domain(email: str) -> str:
    """Extract domain from email"""
    return email.split("@")[-1]
