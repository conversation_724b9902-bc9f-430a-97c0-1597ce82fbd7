"""
Email service for sending <PERSON>TP and other notifications
Supports both Resend and AWS SES with Resend as default
"""

import boto3
from botocore.exceptions import ClientError, BotoCoreError
from loguru import logger
from typing import Dict, Optional
from src.shared.config.settings import settings

try:
    import resend

    RESEND_AVAILABLE = True
except ImportError:
    RESEND_AVAILABLE = False
    logger.warning("Resend package not available. Install with: pip install resend")


class EmailService:
    """Email service supporting both Resend and AWS SES"""

    def __init__(self):
        self.provider = settings.email_provider.lower()
        self.ses_client = None
        self.resend_client = None
        self._initialize_clients()

    def _initialize_clients(self):
        """Initialize email clients based on provider setting"""
        if self.provider == "resend":
            self._initialize_resend_client()
        elif self.provider == "ses":
            self._initialize_ses_client()
        else:
            logger.warning(f"Unknown email provider: {self.provider}. Defaulting to Resend.")
            self.provider = "resend"
            self._initialize_resend_client()

    def _initialize_resend_client(self):
        """Initialize Resend client"""
        if not RESEND_AVAILABLE:
            logger.error("Resend package not available. Falling back to SES.")
            self._initialize_ses_client()
            return

        try:
            if settings.resend_api_key:
                # Set the API key globally for the resend module
                resend.api_key = settings.resend_api_key
                self.resend_client = resend
                logger.info(
                    f"Resend client initialized successfully with API key: {settings.resend_api_key[:10]}..."
                )
            else:
                logger.warning("Resend API key not provided. Falling back to SES.")
                self._initialize_ses_client()
        except Exception as e:
            logger.error(f"Failed to initialize Resend client: {e}. Falling back to SES.")
            self._initialize_ses_client()

    def _initialize_ses_client(self):
        """Initialize AWS SES client using credentials from settings"""
        try:
            # Try to use explicit credentials from settings first
            if settings.aws_access_key_id and settings.aws_secret_access_key:
                self.ses_client = boto3.client(
                    "ses",
                    region_name=settings.aws_region,
                    aws_access_key_id=settings.aws_access_key_id,
                    aws_secret_access_key=settings.aws_secret_access_key,
                )
                logger.info(
                    f"AWS SES client initialized with explicit credentials for region: {settings.aws_region}"
                )
            else:
                # Fallback to default AWS credential chain
                self.ses_client = boto3.client("ses", region_name=settings.aws_region)
                logger.info(
                    f"AWS SES client initialized with default credential chain for region: {settings.aws_region}"
                )
        except Exception as e:
            logger.error(f"Failed to initialize AWS SES client: {e}")
            self.ses_client = None

    def send_otp_email(self, email: str, otp: str, first_name: str = "User") -> Dict[str, str]:
        """
        Send OTP email to user using configured provider (Resend or SES)

        Args:
            email: Recipient email address
            otp: 6-digit OTP code
            first_name: User's first name for personalization

        Returns:
            Dict with success status and message
        """
        logger.debug(f"Attempting to send OTP email to {email} using provider: {self.provider}")
        logger.debug(f"Resend client available: {self.resend_client is not None}")
        logger.debug(f"SES client available: {self.ses_client is not None}")

        # Try primary provider first, fallback to console if both fail
        if self.provider == "resend" and self.resend_client:
            logger.debug("Using Resend provider")
            return self._send_otp_via_resend(email, otp, first_name)
        elif self.provider == "ses" and self.ses_client:
            logger.debug("Using SES provider")
            return self._send_otp_via_ses(email, otp, first_name)

        # Fallback to console output if no provider is available
        logger.error(f"No email client available for provider: {self.provider}")
        logger.error(f"Resend client: {self.resend_client}, SES client: {self.ses_client}")
        logger.info(f"🔐 OTP EMAIL FALLBACK: {email} -> OTP: {otp}")
        print(f"OTP Email to {email}: Your OTP is {otp}")
        return {"message": "OTP sent successfully (console fallback)", "success": True}

    def _send_otp_via_resend(self, email: str, otp: str, first_name: str) -> Dict[str, str]:
        """Send OTP email via Resend"""
        subject = "Your Supermaya Verification Code"
        html_body, text_body = self._get_email_templates(otp, first_name)

        try:
            # Use the correct Resend API format
            params = {
                "from": f"{settings.from_name} <{settings.from_email}>",
                "to": [email],
                "subject": subject,
                "html": html_body,
                "text": text_body,
            }

            response = self.resend_client.Emails.send(params)

            logger.info(
                f"OTP email sent successfully via Resend to {email}, MessageId: {response['id']}"
            )
            return {
                "message": "OTP sent successfully to your email",
                "success": True,
                "message_id": response["id"],
                "provider": "resend",
            }

        except Exception as e:
            logger.error(f"Resend error sending OTP email to {email}: {e}")
            # Fallback to console output
            logger.info(f"🔐 OTP EMAIL FALLBACK: {email} -> OTP: {otp}")
            print(f"OTP Email to {email}: Your OTP is {otp}")
            return {
                "message": f"Resend service error: {str(e)}. OTP sent to console for development.",
                "success": False,
                "error": "ResendError",
            }

    def _send_otp_via_ses(self, email: str, otp: str, first_name: str) -> Dict[str, str]:
        """Send OTP email via AWS SES"""
        subject = "Your Supermaya Verification Code"
        html_body, text_body = self._get_email_templates(otp, first_name)

        try:
            response = self.ses_client.send_email(
                Source=f"{settings.from_name} <{settings.from_email}>",
                Destination={"ToAddresses": [email]},
                Message={
                    "Subject": {"Data": subject, "Charset": "UTF-8"},
                    "Body": {
                        "Html": {"Data": html_body, "Charset": "UTF-8"},
                        "Text": {"Data": text_body, "Charset": "UTF-8"},
                    },
                },
            )

            logger.info(
                f"OTP email sent successfully via SES to {email}, MessageId: {response['MessageId']}"
            )
            return {
                "message": "OTP sent successfully to your email",
                "success": True,
                "message_id": response["MessageId"],
                "provider": "ses",
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]
            logger.error(
                f"AWS SES error sending OTP email to {email}: {error_code} - {error_message}"
            )

            # Fallback to console output for development
            logger.info(f"🔐 OTP EMAIL FALLBACK: {email} -> OTP: {otp}")
            print(f"OTP Email to {email}: Your OTP is {otp}")
            return {
                "message": f"Email service error: {error_message}. OTP sent to console for development.",
                "success": False,
                "error": error_code,
            }

        except BotoCoreError as e:
            logger.error(f"BotoCore error sending OTP email to {email}: {e}")
            logger.info(f"🔐 OTP EMAIL FALLBACK: {email} -> OTP: {otp}")
            print(f"OTP Email to {email}: Your OTP is {otp}")
            return {
                "message": f"Email service error: {str(e)}. OTP sent to console for development.",
                "success": False,
                "error": "BotoCoreError",
            }

        except Exception as e:
            logger.error(f"Unexpected error sending OTP email to {email}: {e}")
            logger.info(f"🔐 OTP EMAIL FALLBACK: {email} -> OTP: {otp}")
            print(f"OTP Email to {email}: Your OTP is {otp}")
            return {
                "message": f"Unexpected error: {str(e)}. OTP sent to console for development.",
                "success": False,
                "error": "UnexpectedError",
            }

    def _get_email_templates(self, otp: str, first_name: str) -> tuple[str, str]:
        """Get HTML and text email templates for OTP"""
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Supermaya Verification Code</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">Supermaya</h1>
                <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">Video Generation Platform</p>
            </div>
            
            <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
                <h2 style="color: #333; margin-top: 0;">Hello {first_name}!</h2>
                
                <p>Thank you for signing up for Supermaya. To complete your registration, please use the verification code below:</p>
                
                <div style="background: white; border: 2px solid #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                    <h1 style="color: #667eea; font-size: 32px; letter-spacing: 5px; margin: 0; font-family: 'Courier New', monospace;">{otp}</h1>
                </div>
                
                <p style="color: #666; font-size: 14px;">
                    <strong>Important:</strong> This code will expire in 10 minutes for security reasons.
                </p>
                
                <p style="color: #666; font-size: 14px;">
                    If you didn't request this code, please ignore this email.
                </p>
                
                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
                
                <p style="color: #999; font-size: 12px; text-align: center;">
                    This email was sent by Supermaya. Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        Hello {first_name}!
        
        Thank you for signing up for Supermaya. To complete your registration, please use the verification code below:
        
        Verification Code: {otp}
        
        Important: This code will expire in 10 minutes for security reasons.
        
        If you didn't request this code, please ignore this email.
        
        ---
        This email was sent by Supermaya. Please do not reply to this email.
        """

        return html_body, text_body


# Global email service instance
email_service = EmailService()


def send_otp_email(email: str, otp: str, first_name: str = "User") -> Dict[str, str]:
    """
    Send OTP email using the email service

    Args:
        email: Recipient email address
        otp: 6-digit OTP code
        first_name: User's first name for personalization

    Returns:
        Dict with success status and message
    """
    return email_service.send_otp_email(email, otp, first_name)
