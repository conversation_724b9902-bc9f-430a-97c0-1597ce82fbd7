"""
OTP (One-Time Password) management
"""

import random
from datetime import datetime, timedelta
from typing import Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from src.shared.models.database_models import PendingUser
from src.shared.config.database import db_manager
from src.auth_service.core.email import send_otp_email

# In-memory OTP store for simple implementation
# In production, consider using Redis or database
otp_store: Dict[str, Dict] = {}


async def generate_and_store_otp(email: str, first_name: str = "User") -> str:
    """Generate OTP, store it with expiration, and send email"""
    otp = str(random.randint(100000, 999999))
    expires_at = datetime.utcnow() + timedelta(minutes=10)  # OTP valid for 10 minutes

    # Store in memory
    otp_store[email] = {"otp": otp, "expires_at": expires_at}

    # Also store in database for pending users
    async for session in db_manager.get_session():
        try:
            stmt = (
                update(PendingUser)
                .where(PendingUser.email == email)
                .values(otp_code=otp, otp_expires_at=expires_at)
            )
            await session.execute(stmt)
            await session.commit()
        except Exception:
            await session.rollback()
        finally:
            break

    # Send OTP email
    send_otp_email(email, otp, first_name)

    return otp


async def verify_otp(email: str, otp: str) -> bool:
    """Verify OTP for email"""
    # Check in-memory store first
    if email in otp_store:
        stored_data = otp_store[email]
        if stored_data["otp"] == otp and datetime.utcnow() <= stored_data["expires_at"]:
            # Remove from memory after successful verification
            del otp_store[email]
            return True
        else:
            # Remove expired OTP
            del otp_store[email]

    # Check database as fallback
    async for session in db_manager.get_session():
        try:
            stmt = select(PendingUser).where(PendingUser.email == email)
            result = await session.execute(stmt)
            pending_user = result.scalar_one_or_none()

            if (
                pending_user
                and pending_user.otp_code == otp
                and pending_user.otp_expires_at
                and datetime.utcnow() <= pending_user.otp_expires_at
            ):
                return True

        except Exception:
            pass
        finally:
            break

    return False
