"""
Auth Service FastAPI application
"""

# Global imports
from loguru import logger
from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware

# Local imports
# Local Imports
from src.auth_service.api.routes import auth
from src.shared.config.settings import settings
from src.shared.config.database import db_manager
from src.shared.core.middleware import RequestLoggingMiddleware, ErrorHandlingMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Auth Service...")

    # Initialize database
    db_manager.initialize()
    logger.info("Database initialized")

    logger.info("Auth Service started successfully")
    yield

    # Shutdown
    logger.info("Shutting down Auth Service...")

    # Close database connections
    await db_manager.close()
    logger.info("Database connections closed")

    logger.info("Auth Service shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=f"{settings.app_name} - Auth Service",
    version=settings.app_version,
    description="Authentication and user management service",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.backend_cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(ErrorHandlingMiddleware)

# Include routers
app.include_router(auth.router, prefix=settings.api_v1_str)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Auth Service",
        "version": settings.app_version,
        "status": "running",
        "docs_url": "/docs" if settings.debug else "disabled",
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "src.auth_service.main:app",
        host="0.0.0.0",
        port=8001,  # Different port from script service
        reload=settings.debug,
        log_level="info",
    )
