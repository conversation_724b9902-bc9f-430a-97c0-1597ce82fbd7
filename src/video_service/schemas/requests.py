# Global imports
from pydantic import BaseModel
from typing import Optional, List


class GenerateVideoRequest(BaseModel):
    project_id: Optional[str] = None  # If you want to filter by project
    aspect_ratio: Optional[str] = "16:9"
    duration: Optional[str] = "5"


class GenerateVideoByScenesRequest(BaseModel):
    scene_ids: List[str]  # List of scene IDs to generate videos for
    aspect_ratio: Optional[str] = "16:9"
    duration: Optional[str] = "5"


class UpdateVideoPromptAndRegenerateRequest(BaseModel):
    updated_prompt: str
    aspect_ratio: Optional[str] = "16:9"
    duration: Optional[str] = "5"
