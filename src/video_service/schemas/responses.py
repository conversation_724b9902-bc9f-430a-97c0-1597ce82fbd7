# Global imports
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional, Dict


class VideoGenerationResult(BaseModel):
    scene_number: int
    scene_description: str
    image_url: str
    professional_prompt: str
    success: bool
    status: str
    video_url: Optional[str]
    error: Optional[str]
    task_id: Optional[str]
    quality: Optional[str]


class VideoGenerationInitResponse(BaseModel):
    """Response for video generation initiation"""

    message: str = "Video generation started"
    scene_ids: List[str]
    estimated_completion_time: int = 480  # 8 minutes in seconds
    status: str = "processing"


class VideoGenerationStatusResponse(BaseModel):
    """Response for video generation status check"""

    scene_id: str
    status: str  # "processing", "completed", "failed"
    video_url: Optional[str] = None
    local_path: Optional[str] = None
    error: Optional[str] = None
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class VideoGenerationStatusListResponse(BaseModel):
    """Response for multiple video generation status checks"""

    script_id: str
    scenes: List[VideoGenerationStatusResponse]
    overall_status: str  # "processing", "completed", "failed", "partial"
    completed_count: int
    total_count: int


class SceneVideoResponse(BaseModel):
    """Response for single scene video retrieval"""

    scene_id: str
    video_id: str
    url: str
    created_at: datetime
    status: str = "completed"


# New response models for the status endpoint
class SceneStatusResponse(BaseModel):
    """Response for individual scene status"""

    id: str
    scene_number: int
    generation_status: Optional[str] = None
    presigned_url: Optional[str] = None  # Add this field for completed scenes
    video_prompt: Optional[str] = None  # Add video prompt field


class VideoGenerationScriptStatusResponse(BaseModel):
    """Response for script video generation status check"""

    status: str = "success"
    script_id: str
    scenes: List[SceneStatusResponse]


class VideoRegenerationResponse(BaseModel):
    """Response for video regeneration with updated prompt"""

    scene_id: str
    status: str
    message: str
    video_prompt: str
