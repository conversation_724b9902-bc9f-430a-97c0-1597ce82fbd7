"""
Enhanced Agentic Video and Audio Stitching System for VidFlux
Combines the best features from both agentic_video_stitcher.py and audio_stitcher.py
"""

import os
import logging
import tempfile
import asyncio
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum

# MoviePy imports
from moviepy import (
    Video<PERSON><PERSON><PERSON><PERSON>,
    AudioFileClip,
    CompositeAudioClip,
    CompositeVideoClip,
    concatenate_audioclips,
    concatenate_videoclips,
)
from moviepy import afx, vfx

# Optional imports with fallbacks
try:
    import librosa

    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False

try:
    from pydub import AudioSegment

    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False

# Import audio stitcher for advanced audio processing
from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProcessingPhase(Enum):
    INITIALIZATION = "initialization"
    ASSET_DOWNLOAD = "asset_download"
    ASSET_VALIDATION = "asset_validation"
    VIDEO_STITCHING = "video_stitching"
    AUDIO_PROCESSING = "audio_processing"
    AI_ENHANCEMENT = "ai_enhancement"
    FINAL_MIXING = "final_mixing"
    QUALITY_CHECK = "quality_check"
    UPLOAD = "upload"
    COMPLETE = "complete"


@dataclass
class AssetInfo:
    """Information about a video or audio asset"""

    asset_id: str
    local_path: str
    s3_url: str
    asset_type: str  # 'video', 'voiceover', 'background_music', 'background_sound'
    scene_id: Optional[str] = None
    duration: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ProcessingResult:
    """Result of processing operation"""

    success: bool
    output_path: Optional[str] = None
    s3_url: Optional[str] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class AudioActivityDetector:
    """
    Enhanced audio activity detector from audio_stitcher.py
    Identifies and removes silence gaps for seamless audio looping
    """

    def __init__(self, silence_threshold: float = 0.01, min_silence_duration: float = 0.1):
        self.silence_threshold = silence_threshold
        self.min_silence_duration = min_silence_duration

    def detect_audio_regions(self, audio_clip) -> list:
        """Detect regions in the audio that contain actual audio content"""
        try:
            import numpy as np

            audio_array = audio_clip.to_soundarray()
            fps = audio_clip.fps

            frame_size = int(fps * 0.01)  # 10ms frames
            audio_regions = []

            # Handle mono and stereo audio
            if len(audio_array.shape) == 1:
                audio_data = audio_array
            else:
                audio_data = np.max(np.abs(audio_array), axis=1)

            # Analyze audio in small frames
            is_audio_active = []
            for i in range(0, len(audio_data), frame_size):
                frame = audio_data[i : i + frame_size]
                if len(frame) > 0:
                    rms = np.sqrt(np.mean(frame**2))
                    is_audio_active.append(rms > self.silence_threshold)
                else:
                    is_audio_active.append(False)

            # Find continuous audio regions
            current_start = None
            frame_duration = frame_size / fps

            for i, is_active in enumerate(is_audio_active):
                time_pos = i * frame_duration

                if is_active and current_start is None:
                    current_start = time_pos
                elif not is_active and current_start is not None:
                    region_duration = time_pos - current_start
                    if region_duration > self.min_silence_duration:
                        audio_regions.append((current_start, time_pos))
                    current_start = None

            if current_start is not None:
                audio_regions.append((current_start, len(audio_data) / fps))

            logger.info(f"Detected {len(audio_regions)} audio regions: {audio_regions}")
            return audio_regions

        except Exception as e:
            logger.error(f"Error detecting audio regions: {e}")
            return [(0, audio_clip.duration)]

    def extract_audio_content(self, audio_clip):
        """Extract only the parts of the audio that contain actual content (no silence)"""
        try:
            audio_regions = self.detect_audio_regions(audio_clip)

            if not audio_regions:
                logger.warning("No audio regions detected, using original clip")
                return audio_clip

            if len(audio_regions) == 1:
                start, end = audio_regions[0]
                start = max(0, start - 0.1)
                end = min(audio_clip.duration, end + 0.1)

                trimmed_clip = audio_clip.subclipped(start, end)
                logger.info(
                    f"Trimmed audio from {audio_clip.duration:.2f}s to {trimmed_clip.duration:.2f}s"
                )
                return trimmed_clip

            # Multiple regions - concatenate them
            audio_segments = []
            for start, end in audio_regions:
                start = max(0, start - 0.05)
                end = min(audio_clip.duration, end + 0.05)
                segment = audio_clip.subclipped(start, end)
                audio_segments.append(segment)

            if audio_segments:
                combined_audio = concatenate_audioclips(audio_segments)
                logger.info(
                    f"Combined {len(audio_segments)} audio regions: {audio_clip.duration:.2f}s → {combined_audio.duration:.2f}s"
                )
                return combined_audio
            else:
                return audio_clip

        except Exception as e:
            logger.error(f"Error extracting audio content: {e}")
            return audio_clip

    def create_seamless_loop(self, audio_clip, target_duration: float):
        """Create a seamless loop of the audio content for the target duration"""
        try:
            if audio_clip.duration >= target_duration:
                return audio_clip.subclipped(0, target_duration)

            loops_needed = int(target_duration / audio_clip.duration) + 1
            loops = [audio_clip] * loops_needed
            looped_audio = concatenate_audioclips(loops)
            final_audio = looped_audio.subclipped(0, target_duration)

            logger.info(
                f"Created seamless loop: {audio_clip.duration:.2f}s × {loops_needed} = {final_audio.duration:.2f}s"
            )
            return final_audio

        except Exception as e:
            logger.error(f"Error creating seamless loop: {e}")
            loops_needed = int(target_duration / audio_clip.duration) + 1
            loops = [audio_clip] * loops_needed
            looped_audio = concatenate_audioclips(loops)
            return looped_audio.subclipped(0, target_duration)


class AIEnhancementEngine:
    """
    AI enhancement engine from audio_stitcher.py
    Handles AI-based audio enhancement using external tools
    """

    def apply_ai_enhancement(self, audio_path: str, enhancement_type: str = "denoising") -> str:
        """Apply AI-based audio enhancement using external tools"""
        try:
            enhanced_path = audio_path.replace(".wav", "_enhanced.wav").replace(
                ".mp3", "_enhanced.wav"
            )

            if enhancement_type == "denoising" and shutil.which("python"):
                # Try to use DeepFilterNet if available
                try:
                    result = subprocess.run(
                        ["python", "-m", "deepfilternet", audio_path, "--output", enhanced_path],
                        capture_output=True,
                        text=True,
                        timeout=30,
                    )

                    if result.returncode == 0 and os.path.exists(enhanced_path):
                        logger.info("Applied DeepFilterNet enhancement")
                        return enhanced_path
                    else:
                        logger.warning("DeepFilterNet not available or failed")

                except (
                    subprocess.TimeoutExpired,
                    FileNotFoundError,
                    subprocess.SubprocessError,
                ) as e:
                    logger.warning(f"DeepFilterNet enhancement failed: {e}")

            elif enhancement_type == "speech_enhance":
                # Placeholder for other AI enhancement tools
                logger.info(
                    "Speech enhancement placeholder - integrate with Adobe Speech Enhancer API"
                )

            # If AI enhancement fails, try simple noise reduction with ffmpeg
            if shutil.which("ffmpeg"):
                try:
                    result = subprocess.run(
                        [
                            "ffmpeg",
                            "-i",
                            audio_path,
                            "-af",
                            "highpass=f=80,lowpass=f=8000,volume=1.1",
                            enhanced_path,
                            "-y",
                        ],
                        capture_output=True,
                        text=True,
                        timeout=30,
                    )

                    if result.returncode == 0 and os.path.exists(enhanced_path):
                        logger.info("Applied basic ffmpeg audio enhancement")
                        return enhanced_path

                except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                    logger.warning(f"FFmpeg enhancement failed: {e}")

            logger.info("No AI enhancement applied - returning original audio")
            return audio_path  # Return original if enhancement fails

        except Exception as e:
            logger.error(f"Error in AI enhancement: {str(e)}")
            return audio_path


class AudioQualityAnalyzer:
    """
    Audio quality analyzer from audio_stitcher.py
    Performs comprehensive audio quality analysis
    """

    def __init__(self):
        self.min_speech_clarity_score = 0.7
        self.min_dynamic_range = 10  # dB

    def analyze_audio_levels(self, audio_path: str) -> Dict[str, float]:
        """Analyze audio levels with fallback if librosa not available"""
        if not LIBROSA_AVAILABLE:
            return self.analyze_audio_levels_fallback(audio_path)

        try:
            import numpy as np

            # Load audio with librosa
            y, sr = librosa.load(audio_path, sr=None)

            # Calculate RMS energy
            rms = librosa.feature.rms(y=y)[0]
            rms_db = 20 * np.log10(np.mean(rms) + 1e-6)

            # Calculate peak level
            peak_db = 20 * np.log10(np.max(np.abs(y)) + 1e-6)

            # Calculate dynamic range
            dynamic_range = peak_db - rms_db

            # Estimate LUFS (simplified calculation)
            lufs_estimate = rms_db - 23  # Rough conversion

            # Speech clarity estimation (spectral centroid variance)
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            clarity_score = 1 / (1 + np.std(spectral_centroids) / np.mean(spectral_centroids))

            return {
                "rms_db": float(rms_db),
                "peak_db": float(peak_db),
                "dynamic_range": float(dynamic_range),
                "lufs_estimate": float(lufs_estimate),
                "clarity_score": float(clarity_score),
                "duration": len(y) / sr,
            }

        except Exception as e:
            logger.error(f"Error analyzing audio levels: {e}")
            return self.analyze_audio_levels_fallback(audio_path)

    def analyze_audio_levels_fallback(self, audio_path: str) -> Dict[str, float]:
        """Fallback audio analysis using pydub"""
        try:
            if not PYDUB_AVAILABLE:
                raise ImportError("pydub not available")

            audio = AudioSegment.from_file(audio_path)

            return {
                "rms_db": float(audio.dBFS),
                "peak_db": float(audio.max_dBFS),
                "dynamic_range": 20.0,  # estimated
                "lufs_estimate": float(audio.dBFS - 23),
                "clarity_score": 0.7,  # estimated
                "duration": len(audio) / 1000.0,
            }
        except Exception as e:
            logger.error(f"Error in fallback audio analysis: {str(e)}")
            return {
                "rms_db": -30.0,
                "peak_db": -6.0,
                "dynamic_range": 20.0,
                "lufs_estimate": -16.0,
                "clarity_score": 0.5,
                "duration": 0.0,
            }

    def perform_audio_quality_analysis(self, final_audio_path: str) -> Dict[str, Any]:
        """Perform comprehensive audio quality analysis"""
        try:
            analysis = self.analyze_audio_levels(final_audio_path)

            # Quality checks
            checks = {
                "voice_clarity": {
                    "score": analysis["clarity_score"],
                    "pass": analysis["clarity_score"] >= self.min_speech_clarity_score,
                    "message": (
                        "Good speech clarity"
                        if analysis["clarity_score"] >= self.min_speech_clarity_score
                        else "Speech clarity could be improved"
                    ),
                },
                "loudness_level": {
                    "value": analysis["lufs_estimate"],
                    "pass": -25 <= analysis["lufs_estimate"] <= -10,
                    "message": (
                        "Good loudness level"
                        if -25 <= analysis["lufs_estimate"] <= -10
                        else "Loudness level outside optimal range"
                    ),
                },
                "dynamic_range": {
                    "value": analysis["dynamic_range"],
                    "pass": analysis["dynamic_range"] >= self.min_dynamic_range,
                    "message": (
                        "Good dynamic range"
                        if analysis["dynamic_range"] >= self.min_dynamic_range
                        else "Limited dynamic range detected"
                    ),
                },
                "peak_levels": {
                    "value": analysis["peak_db"],
                    "pass": analysis["peak_db"] <= -1.0,
                    "message": (
                        "No clipping detected"
                        if analysis["peak_db"] <= -1.0
                        else "Potential clipping detected"
                    ),
                },
            }

            # Overall quality score
            passed_checks = sum(1 for check in checks.values() if check["pass"])
            quality_score = passed_checks / len(checks)

            return {
                "overall_score": quality_score,
                "checks": checks,
                "audio_metrics": analysis,
                "recommendations": self._generate_quality_recommendations(checks),
            }

        except Exception as e:
            logger.error(f"Error in audio quality analysis: {str(e)}")
            return {
                "overall_score": 0.5,
                "checks": {},
                "audio_metrics": {},
                "recommendations": ["Unable to perform quality analysis"],
            }

    def _generate_quality_recommendations(self, checks: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on quality checks"""
        recommendations = []

        if not checks["voice_clarity"]["pass"]:
            recommendations.append(
                "Consider using an AI speech enhancer like DeepFilterNet for clarity"
            )

        if not checks["loudness_level"]["pass"]:
            if checks["loudness_level"]["value"] < -25:
                recommendations.append(
                    "Audio might be a bit quiet - consider gentle volume increase"
                )
            else:
                recommendations.append(
                    "Audio might be a bit loud - consider gentle volume decrease"
                )

        if not checks["dynamic_range"]["pass"]:
            recommendations.append("Consider adjusting volume levels for better dynamic range")

        if not checks["peak_levels"]["pass"]:
            recommendations.append("Consider gentle volume reduction to prevent clipping")

        if not recommendations:
            recommendations.append("Audio quality is good - no improvements needed")

        return recommendations


class TransitionEngine:
    """Intelligent transition selection and application engine"""

    def __init__(self):
        self.available_transitions = [
            "crossfade",
            "fade",
            "slide",
            "dissolve",
            "zoom",
            "wipe",
            "cut",
        ]
        self.transition_weights = {
            "crossfade": 0.3,  # Most versatile
            "fade": 0.2,  # Good for dramatic changes
            "slide": 0.15,  # Dynamic movement
            "dissolve": 0.15,  # Smooth blending
            "zoom": 0.1,  # Attention-grabbing
            "wipe": 0.05,  # Creative effect
            "cut": 0.05,  # Direct transition
        }

    def select_intelligent_transition(
        self, scene1_data: Dict[str, Any], scene2_data: Dict[str, Any]
    ) -> str:
        """Intelligently select transition based on scene content and flow"""
        try:
            # Analyze scene characteristics
            scene1_energy = self._estimate_scene_energy(scene1_data)
            scene2_energy = self._estimate_scene_energy(scene2_data)

            energy_diff = abs(scene1_energy - scene2_energy)
            avg_energy = (scene1_energy + scene2_energy) / 2

            # Select transition based on energy and content
            if energy_diff > 0.6:  # High energy difference
                if avg_energy > 0.7:  # Both high energy
                    selected = "zoom"  # Dynamic transition
                else:  # Low to high or high to low energy
                    selected = "fade"  # Smooth dramatic change

            elif energy_diff > 0.3:  # Medium energy difference
                if avg_energy > 0.5:
                    selected = "slide"  # Dynamic but smooth
                else:
                    selected = "dissolve"  # Gentle blend

            else:  # Similar energy levels
                if avg_energy > 0.6:  # Both high energy
                    selected = "crossfade"  # Keep momentum
                else:  # Both low energy
                    selected = "crossfade"  # Gentle transition

            logger.info(
                f"Agentic transition selection: {selected} (energy1={scene1_energy:.2f}, energy2={scene2_energy:.2f}, diff={energy_diff:.2f})"
            )
            return selected

        except Exception as e:
            logger.warning(f"Error in intelligent transition selection: {e}")
            return "crossfade"  # Safe fallback

    def _estimate_scene_energy(self, scene_data: Dict[str, Any]) -> float:
        """Estimate energy level of a scene (0.0 to 1.0)"""
        try:
            # Base energy from scene content (placeholder analysis)
            energy = 0.5  # Default medium energy

            # Adjust based on scene characteristics
            scene_number = scene_data.get("scene_number", 1)

            # First and last scenes typically lower energy
            if scene_number == 1:
                energy = 0.3
            elif scene_number > 5:  # Later scenes often higher energy
                energy = 0.7

            # Add some variation based on scene position
            energy += (scene_number % 3) * 0.1

            return min(1.0, max(0.0, energy))

        except Exception:
            return 0.5  # Default medium energy


class EnhancedAudioProcessor:
    """
    Enhanced audio processor combining features from both implementations
    """

    def __init__(self):
        # Enhanced audio mixing settings from audio_stitcher
        self.voiceover_volume = 0.8  # Higher volume for voice-over
        self.background_music_volume = 0.3  # More audible background music
        self.existing_audio_volume = 0.2  # More noticeable existing background sounds

        # Advanced fade settings
        self.fade_in_duration = 1.0  # Longer fade-in for smoother transitions
        self.fade_out_duration = 1.5  # Longer fade-out for smoother transitions
        self.crossfade_duration = 0.5  # Crossfade between audio segments
        self.ducking_ratio = 0.4  # Less aggressive ducking

        # Initialize components
        self.activity_detector = AudioActivityDetector()
        self.ai_enhancer = AIEnhancementEngine()
        self.quality_analyzer = AudioQualityAnalyzer()

    def apply_gentle_volume_adjustment(self, audio_clip, target_volume: float) -> AudioFileClip:
        """Apply gentle volume adjustment (NO aggressive normalization)"""
        try:
            return audio_clip.with_effects([afx.MultiplyVolume(target_volume)])
        except Exception as e:
            logger.error(f"Error adjusting volume: {str(e)}")
            return audio_clip

    def apply_advanced_fade(
        self, audio_clip, fade_in: float = 1.0, fade_out: float = 1.5
    ) -> AudioFileClip:
        """Apply improved fade effects with smooth curves"""
        try:
            effects = []

            if fade_in > 0:
                effects.append(afx.AudioFadeIn(fade_in))

            if fade_out > 0:
                effects.append(afx.AudioFadeOut(fade_out))

            if effects:
                return audio_clip.with_effects(effects)
            else:
                return audio_clip

        except Exception as e:
            logger.error(f"Error applying fades: {str(e)}")
            return audio_clip

    def apply_intelligent_ducking(
        self, background_audio, speech_audio, ratio: float = 0.4
    ) -> AudioFileClip:
        """Apply GENTLE ducking that slightly reduces background audio during speech"""
        try:
            if speech_audio and speech_audio.duration > 0:
                # GENTLE ducking - reduce background volume by a smaller ratio
                ducked_volume = 1.0 - ratio
                ducked_background = background_audio.with_effects(
                    [afx.MultiplyVolume(ducked_volume)]
                )
                logger.info(
                    f"Applied gentle ducking: reduced background to {ducked_volume*100:.0f}% volume during speech"
                )
                return ducked_background
            else:
                return background_audio

        except Exception as e:
            logger.error(f"Error applying ducking: {str(e)}")
            return background_audio

    async def combine_voiceover_files(self, voiceover_paths: List[str]) -> str:
        """Combine multiple voiceover files into one"""
        if not voiceover_paths:
            return None

        try:
            audio_clips = []
            for path in voiceover_paths:
                if os.path.exists(path):
                    clip = AudioFileClip(path)
                    audio_clips.append(clip)

            if not audio_clips:
                return None

            # Concatenate audio clips
            combined_audio = concatenate_audioclips(audio_clips)

            # Save combined audio
            output_path = os.path.join(
                "/tmp", f"combined_voiceover_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            combined_audio.write_audiofile(output_path)

            # Close clips
            for clip in audio_clips:
                clip.close()
            combined_audio.close()

            return output_path

        except Exception as e:
            logger.error(f"Error combining voiceover files: {e}")
            return None

    async def create_seamless_loop(self, audio_path: str) -> str:
        """Create a seamless loop of background music"""
        try:
            if not os.path.exists(audio_path):
                return None

            audio_clip = AudioFileClip(audio_path)

            # Apply activity detection to remove silence
            processed_clip = self.activity_detector.extract_audio_content(audio_clip)

            # Save processed audio
            output_path = os.path.join(
                "/tmp", f"looped_music_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            processed_clip.write_audiofile(output_path)

            # Close clips
            audio_clip.close()
            processed_clip.close()

            return output_path

        except Exception as e:
            logger.error(f"Error creating seamless loop: {e}")
            return None

    async def combine_background_sounds(self, bg_sound_paths: List[str]) -> str:
        """Combine multiple background sound files"""
        if not bg_sound_paths:
            return None

        try:
            audio_clips = []
            for path in bg_sound_paths:
                if os.path.exists(path):
                    clip = AudioFileClip(path)
                    # Apply gentle volume adjustment
                    clip = self.apply_gentle_volume_adjustment(clip, self.existing_audio_volume)
                    audio_clips.append(clip)

            if not audio_clips:
                return None

            # Concatenate audio clips
            combined_audio = concatenate_audioclips(audio_clips)

            # Save combined audio
            output_path = os.path.join(
                "/tmp", f"combined_bg_sounds_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            combined_audio.write_audiofile(output_path)

            # Close clips
            for clip in audio_clips:
                clip.close()
            combined_audio.close()

            return output_path

        except Exception as e:
            logger.error(f"Error combining background sounds: {e}")
            return None

    async def create_final_audio_with_voiceover_and_music(
        self,
        video_clip,
        voiceover_path: str = None,
        background_music_path: str = None,
        background_sounds_path: str = None,
        enable_ducking: bool = True,
    ) -> str:
        """Create final audio mix with all components"""
        try:
            audio_components = []

            # Add existing video audio
            if video_clip.audio:
                existing_audio = video_clip.audio
                existing_audio = self.apply_gentle_volume_adjustment(
                    existing_audio, self.existing_audio_volume
                )
                audio_components.append(existing_audio)

            # Add voiceover
            if voiceover_path and os.path.exists(voiceover_path):
                voiceover_clip = AudioFileClip(voiceover_path)
                voiceover_clip = self.apply_gentle_volume_adjustment(
                    voiceover_clip, self.voiceover_volume
                )
                audio_components.append(voiceover_clip)

            # Add background music
            if background_music_path and os.path.exists(background_music_path):
                bg_music_clip = AudioFileClip(background_music_path)
                bg_music_clip = self.apply_gentle_volume_adjustment(
                    bg_music_clip, self.background_music_volume
                )
                audio_components.append(bg_music_clip)

            # Add background sounds
            if background_sounds_path and os.path.exists(background_sounds_path):
                bg_sounds_clip = AudioFileClip(background_sounds_path)
                bg_sounds_clip = self.apply_gentle_volume_adjustment(
                    bg_sounds_clip, self.existing_audio_volume
                )
                audio_components.append(bg_sounds_clip)

            if not audio_components:
                return None

            # Composite all audio components
            final_audio = CompositeAudioClip(audio_components)

            # Save final audio
            output_path = os.path.join(
                "/tmp", f"final_audio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
            )
            final_audio.write_audiofile(output_path)

            # Close clips
            for clip in audio_components:
                clip.close()
            final_audio.close()

            return output_path

        except Exception as e:
            logger.error(f"Error creating final audio: {e}")
            return None

    async def concatenate_voiceovers(
        self, voiceover_paths: List[str], output_path: str, target_duration: float
    ) -> ProcessingResult:
        """Concatenate voiceovers sequentially with enhanced processing"""
        try:
            audio_clips = []
            for path in voiceover_paths:
                if os.path.exists(path):
                    clip = AudioFileClip(path)

                    # Apply volume adjustment
                    clip = self.apply_gentle_volume_adjustment(clip, self.voiceover_volume)
                    audio_clips.append(clip)
                    logger.info(
                        f"Processed voiceover: {os.path.basename(path)} - Duration: {clip.duration:.2f}s"
                    )

            if not audio_clips:
                return ProcessingResult(success=False, error_message="No voiceover clips found")

            # Concatenate clips
            concatenated = concatenate_audioclips(audio_clips)
            logger.info(
                f"Concatenated {len(audio_clips)} voiceover clips - Total duration: {concatenated.duration:.2f}s"
            )

            # Adjust to target duration if needed
            if concatenated.duration > target_duration:
                logger.warning(
                    f"Voiceover duration ({concatenated.duration:.2f}s) exceeds target ({target_duration:.2f}s), trimming"
                )
                concatenated = concatenated.subclipped(0, target_duration)
            elif concatenated.duration < target_duration:
                # Add minimal silence to fill duration (but don't extend too much)
                silence_duration = min(
                    5.0, target_duration - concatenated.duration
                )  # Max 5s silence
                if silence_duration > 0:
                    from moviepy import AudioClip

                    silence = AudioClip(lambda t: [0, 0], duration=silence_duration)
                    concatenated = concatenate_audioclips([concatenated, silence])
                    logger.info(f"Added {silence_duration:.2f}s silence to match target duration")

            # Write output
            concatenated.write_audiofile(output_path, logger=None)

            # Cleanup
            for clip in audio_clips:
                clip.close()
            concatenated.close()

            return ProcessingResult(success=True, output_path=output_path)

        except Exception as e:
            logger.error(f"Error concatenating voiceovers: {e}")
            return ProcessingResult(success=False, error_message=str(e))

    async def process_background_music(
        self,
        bg_music_paths: List[str],
        output_path: str,
        target_duration: float,
        enable_ai_enhancement: bool = False,
    ) -> ProcessingResult:
        """Process background music with enhanced features"""
        try:
            if not bg_music_paths:
                return ProcessingResult(
                    success=False, error_message="No background music paths provided"
                )

            # If multiple files, concatenate them first, otherwise use the single file
            if len(bg_music_paths) > 1:
                logger.info(f"Concatenating {len(bg_music_paths)} background music files")
                bg_clips = [AudioFileClip(path) for path in bg_music_paths]
                bg_clip = concatenate_audioclips(bg_clips)
                # Close individual clips
                for clip in bg_clips:
                    clip.close()
            else:
                bg_clip = AudioFileClip(bg_music_paths[0])

            logger.info(
                f"Loaded background music: duration={bg_clip.duration:.2f}s, target={target_duration:.2f}s"
            )

            # Apply AI enhancement if enabled
            if enable_ai_enhancement:
                # Save temporary file for AI enhancement
                temp_path = output_path.replace(".mp3", "_temp.mp3")
                bg_clip.write_audiofile(temp_path, logger=None)
                bg_clip.close()

                # Apply AI enhancement
                enhanced_path = self.ai_enhancer.apply_ai_enhancement(temp_path, "denoising")
                if enhanced_path != temp_path:
                    bg_clip = AudioFileClip(enhanced_path)
                    logger.info("Applied AI enhancement to background music")
                else:
                    bg_clip = AudioFileClip(temp_path)

                # Clean up temp file
                if os.path.exists(temp_path):
                    os.remove(temp_path)

            # Remove silence and create seamless loops using activity detector
            audio_only_clip = self.activity_detector.extract_audio_content(bg_clip)
            bg_clip = self.activity_detector.create_seamless_loop(audio_only_clip, target_duration)

            # Apply volume adjustment
            bg_clip = self.apply_gentle_volume_adjustment(bg_clip, self.background_music_volume)

            logger.info(f"Processing background music: final duration={bg_clip.duration:.2f}s")

            # Write output
            bg_clip.write_audiofile(output_path, logger=None)
            bg_clip.close()

            logger.info(f"Background music processed successfully: {output_path}")
            return ProcessingResult(success=True, output_path=output_path)

        except Exception as e:
            logger.error(f"Error processing background music: {e}")
            return ProcessingResult(success=False, error_message=str(e))

    async def mix_final_video(
        self,
        video_path: str,
        voiceover_path: Optional[str],
        background_music_path: Optional[str],
        background_sounds_path: Optional[str],
        output_path: str,
        enable_ducking: bool = True,
        enable_ai_enhancement: bool = False,
        enable_quality_analysis: bool = True,
    ) -> ProcessingResult:
        """Mix final video with all audio components using enhanced processing"""
        try:
            # Load video
            video_clip = VideoFileClip(video_path)
            audio_tracks = []
            track_info = []

            # Add existing video audio
            if video_clip.audio is not None:
                existing_audio = self.apply_gentle_volume_adjustment(
                    video_clip.audio, self.existing_audio_volume
                )
                existing_audio = self.apply_advanced_fade(
                    existing_audio, self.fade_in_duration, self.fade_out_duration
                )
                audio_tracks.append(existing_audio)
                track_info.append("existing_video_audio")
                logger.info("Added existing video audio to final mix")

            # Add background sounds (higher priority than background music)
            if background_sounds_path and os.path.exists(background_sounds_path):
                bg_sounds = AudioFileClip(background_sounds_path)
                bg_sounds = self.apply_gentle_volume_adjustment(
                    bg_sounds, 0.4
                )  # Moderate volume for background sounds
                audio_tracks.append(bg_sounds)
                track_info.append("background_sounds")
                logger.info(
                    f"Added background sounds to final mix: {os.path.basename(background_sounds_path)}"
                )
            else:
                logger.warning(
                    f"Background sounds not found or path invalid: {background_sounds_path}"
                )

            # Add background music
            if background_music_path and os.path.exists(background_music_path):
                bg_music = AudioFileClip(background_music_path)
                audio_tracks.append(bg_music)
                track_info.append("background_music")
                logger.info(
                    f"Added background music to final mix: {os.path.basename(background_music_path)}"
                )
            else:
                logger.warning(
                    f"Background music not found or path invalid: {background_music_path}"
                )

            # Add voiceover (highest priority)
            voiceover_clip = None
            if voiceover_path and os.path.exists(voiceover_path):
                voiceover_clip = AudioFileClip(voiceover_path)

                # Apply AI enhancement if enabled
                if enable_ai_enhancement:
                    # Save temporary file for AI enhancement
                    temp_path = voiceover_path.replace(".mp3", "_temp_enhanced.mp3")
                    voiceover_clip.write_audiofile(temp_path, logger=None)
                    voiceover_clip.close()

                    # Apply AI enhancement
                    enhanced_path = self.ai_enhancer.apply_ai_enhancement(
                        temp_path, "speech_enhance"
                    )
                    if enhanced_path != temp_path:
                        voiceover_clip = AudioFileClip(enhanced_path)
                        logger.info("Applied AI enhancement to voiceover")
                    else:
                        voiceover_clip = AudioFileClip(temp_path)

                    # Clean up temp file
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

                audio_tracks.append(voiceover_clip)
                track_info.append("voiceover")
                logger.info(f"Added voiceover to final mix: {os.path.basename(voiceover_path)}")
            else:
                logger.warning(f"Voiceover not found or path invalid: {voiceover_path}")

            # Apply intelligent ducking if enabled
            if enable_ducking and voiceover_clip and len(audio_tracks) > 1:
                # Duck background audio when voiceover is present
                for i, track in enumerate(audio_tracks[:-1]):  # All except voiceover
                    audio_tracks[i] = self.apply_intelligent_ducking(
                        track, voiceover_clip, self.ducking_ratio
                    )
                logger.info("Applied intelligent ducking for voiceover clarity")

            # Composite all audio tracks
            if audio_tracks:
                final_audio = CompositeAudioClip(audio_tracks)
                final_video = video_clip.with_audio(final_audio)
                logger.info(
                    f"Mixed {len(audio_tracks)} audio tracks into final video: {', '.join(track_info)}"
                )
            else:
                final_video = video_clip
                logger.warning("No audio tracks found, proceeding with video only")

            # Write final video
            final_video.write_videofile(
                output_path,
                codec="libx264",
                audio_codec="aac",
                temp_audiofile="temp-audio.m4a",
                remove_temp=True,
                logger=None,
            )

            # Perform quality analysis if enabled
            quality_analysis = None
            if enable_quality_analysis and len(audio_tracks) > 0:
                # Extract audio for analysis
                temp_audio_path = output_path.replace(".mp4", "_temp_audio.wav")
                final_audio.write_audiofile(temp_audio_path, logger=None)
                quality_analysis = self.quality_analyzer.perform_audio_quality_analysis(
                    temp_audio_path
                )
                os.unlink(temp_audio_path)  # Clean up temporary audio file

            # Cleanup
            video_clip.close()
            for track in audio_tracks:
                track.close()
            final_video.close()

            logger.info("Final video mixing completed successfully")
            return ProcessingResult(
                success=True,
                output_path=output_path,
                metadata={
                    "audio_tracks_mixed": len(audio_tracks),
                    "track_info": track_info,
                    "quality_analysis": quality_analysis,
                    "ai_enhancement_applied": enable_ai_enhancement,
                },
            )

        except Exception as e:
            logger.error(f"Error mixing final video: {e}")
            return ProcessingResult(success=False, error_message=str(e))


class QualityChecker:
    """Basic quality checker for video files"""

    def perform_quality_check(self, video_path: str) -> Dict[str, Any]:
        """Perform basic quality checks on the final video"""
        try:
            if not os.path.exists(video_path):
                return {"success": False, "error": "Video file not found"}

            # Basic file checks
            file_size = os.path.getsize(video_path)

            # Load video to check basic properties
            video_clip = VideoFileClip(video_path)

            quality_result = {
                "success": True,
                "duration": video_clip.duration,
                "fps": video_clip.fps,
                "size": video_clip.size,
                "has_audio": video_clip.audio is not None,
                "file_size_mb": file_size / (1024 * 1024),
                "quality_score": 0.8,  # Basic score
            }

            video_clip.close()

            return quality_result

        except Exception as e:
            logger.error(f"Error in quality check: {e}")
            return {"success": False, "error": str(e)}


class EnhancedAgenticVideoStitcher:
    """
    Enhanced Agentic Video Stitcher combining the best features from both implementations
    """

    def __init__(self, s3_client, progress_callback=None):
        self.s3_client = s3_client
        self.progress_callback = progress_callback

        # Initialize processing components
        self.transition_engine = TransitionEngine()
        self.audio_processor = EnhancedAudioProcessor()

        # Initialize advanced audio mixer from audio_stitcher.py
        self.advanced_audio_mixer = EnhancedVideoAudioMixer(
            output_dir=os.path.join(os.getcwd(), "output", "final_videos")
        )

        # Working directories
        self.temp_dir = "/tmp/enhanced_agentic_stitcher"
        self.video_dir = os.path.join(self.temp_dir, "videos")
        self.audio_dir = os.path.join(self.temp_dir, "audio")
        # Use project output directory for final videos
        self.output_dir = os.path.join(os.getcwd(), "output", "final_videos")

        # Create directories
        for dir_path in [self.temp_dir, self.video_dir, self.audio_dir, self.output_dir]:
            os.makedirs(dir_path, exist_ok=True)

    async def _download_and_validate_assets(
        self,
        script_id: str,
        org_id: str,
        scenes_data: List[Dict[str, Any]],
        background_music_assets: List[Any],
    ) -> Dict[str, List[AssetInfo]]:
        """Download and validate all required assets"""
        assets = {"videos": [], "voiceovers": [], "background_music": [], "background_sounds": []}

        s3_bucket = "assets-vidflux"

        # Download videos, voiceovers, and background sounds for each scene
        for scene_data in scenes_data:
            scene_id = scene_data["id"]
            scene_number = scene_data["scene_number"]

            # Download video
            video_asset = scene_data.get("video_asset")
            if video_asset and video_asset.s3_url:
                local_path = os.path.join(self.video_dir, f"scene_{scene_number}_{scene_id}.mp4")
                try:
                    await self._download_file(video_asset.s3_url, local_path, s3_bucket)
                    assets["videos"].append(
                        AssetInfo(
                            asset_id=video_asset.asset_id,
                            local_path=local_path,
                            s3_url=video_asset.s3_url,
                            asset_type="video",
                            scene_id=str(scene_id),
                        )
                    )
                    logger.info(f"Downloaded video for scene {scene_number}")
                except Exception as e:
                    logger.warning(f"Failed to download video for scene {scene_number}: {e}")

            # Download voiceover
            voiceover_asset = scene_data.get("voiceover_asset")
            if voiceover_asset and voiceover_asset.s3_url:
                local_path = os.path.join(self.audio_dir, f"voiceover_{scene_id}.mp3")
                try:
                    await self._download_file(voiceover_asset.s3_url, local_path, s3_bucket)
                    assets["voiceovers"].append(
                        AssetInfo(
                            asset_id=voiceover_asset.asset_id,
                            local_path=local_path,
                            s3_url=voiceover_asset.s3_url,
                            asset_type="voiceover",
                            scene_id=str(scene_id),
                        )
                    )
                    logger.info(f"Downloaded voiceover for scene {scene_number}")
                except Exception as e:
                    logger.warning(f"Failed to download voiceover for scene {scene_number}: {e}")

            # Download background sound
            background_sound_asset = scene_data.get("background_sound_asset")
            if background_sound_asset and background_sound_asset.s3_url:
                local_path = os.path.join(self.audio_dir, f"bg_sound_{scene_id}.mp3")
                try:
                    await self._download_file(background_sound_asset.s3_url, local_path, s3_bucket)
                    assets["background_sounds"].append(
                        AssetInfo(
                            asset_id=background_sound_asset.asset_id,
                            local_path=local_path,
                            s3_url=background_sound_asset.s3_url,
                            asset_type="background_sound",
                            scene_id=str(scene_id),
                        )
                    )
                    logger.info(f"Downloaded background sound for scene {scene_number}")
                except Exception as e:
                    logger.warning(
                        f"Failed to download background sound for scene {scene_number}: {e}"
                    )

        # Download background music assets
        for bg_music_asset in background_music_assets:
            if bg_music_asset and bg_music_asset.s3_url:
                local_path = os.path.join(self.audio_dir, f"bg_music_{bg_music_asset.asset_id}.mp3")
                try:
                    await self._download_file(bg_music_asset.s3_url, local_path, s3_bucket)
                    assets["background_music"].append(
                        AssetInfo(
                            asset_id=bg_music_asset.asset_id,
                            local_path=local_path,
                            s3_url=bg_music_asset.s3_url,
                            asset_type="background_music",
                            scene_id=None,
                        )
                    )
                    logger.info(f"Downloaded background music: {bg_music_asset.asset_id}")
                except Exception as e:
                    logger.warning(
                        f"Failed to download background music {bg_music_asset.asset_id}: {e}"
                    )

        return assets

    async def _download_file(self, s3_url: str, local_path: str, bucket: str) -> None:
        """Download a file from S3 to local path"""
        if self.s3_client:
            await self.s3_client.download_file(bucket, s3_url, local_path)
        else:
            # Fallback: create empty file for testing
            Path(local_path).parent.mkdir(parents=True, exist_ok=True)
            Path(local_path).touch()

    async def _cleanup_temp_files(self) -> None:
        """Clean up temporary files"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to cleanup temporary files: {e}")

    async def _stitch_videos(
        self, video_assets: List[AssetInfo], scenes_data: List[Dict[str, Any]]
    ) -> str:
        """Stitch video assets using intelligent transitions"""
        if not video_assets:
            raise ValueError("No video assets provided for stitching")

        logger.info(f"Starting video stitching with {len(video_assets)} video assets")
        video_clips = []
        transition_engine = TransitionEngine()

        for i, video_asset in enumerate(video_assets):
            try:
                clip = VideoFileClip(video_asset.local_path)
                video_clips.append(clip)
                logger.info(f"Loaded video clip {i+1}: {video_asset.local_path}")
            except Exception as e:
                logger.error(f"Failed to load video clip {video_asset.local_path}: {e}")
                raise

        # Apply intelligent transitions
        if len(video_clips) > 1:
            logger.info(f"Applying transitions between {len(video_clips)} video clips")
            final_clip = video_clips[0]
            for i in range(1, len(video_clips)):
                scene1_data = scenes_data[i - 1] if i - 1 < len(scenes_data) else {}
                scene2_data = scenes_data[i] if i < len(scenes_data) else {}

                transition_type = transition_engine.select_intelligent_transition(
                    scene1_data, scene2_data
                )
                logger.info(f"Applying {transition_type} transition between scenes {i} and {i+1}")

                # Apply transition
                if transition_type == "crossfade":
                    # Apply crossfade transition using fade effects
                    clip1_fade = final_clip.with_effects([vfx.FadeOut(0.5)])
                    clip2_fade = video_clips[i].with_effects([vfx.FadeIn(0.5)])
                    final_clip = concatenate_videoclips([clip1_fade, clip2_fade])
                elif transition_type == "fade":
                    clip1_fade = final_clip.with_effects([vfx.FadeOut(0.5)])
                    clip2_fade = video_clips[i].with_effects([vfx.FadeIn(0.5)])
                    final_clip = concatenate_videoclips([clip1_fade, clip2_fade])
                else:
                    # Default concatenation
                    final_clip = concatenate_videoclips([final_clip, video_clips[i]])
        else:
            logger.warning(f"Only {len(video_clips)} video clip(s) found - no transitions applied")
            final_clip = video_clips[0]

        # Save stitched video
        output_path = os.path.join(
            self.output_dir, f"stitched_video_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        )
        final_clip.write_videofile(output_path, codec="libx264", audio_codec="aac")

        # Close clips
        for clip in video_clips:
            clip.close()
        final_clip.close()

        return output_path

    async def _process_audio(
        self,
        voiceover_assets: List[AssetInfo],
        background_sound_assets: List[AssetInfo],
        background_music_assets: List[AssetInfo],
        enable_ai_enhancement: bool = False,
    ) -> Dict[str, str]:
        """Process all audio components"""
        audio_processor = EnhancedAudioProcessor()
        ai_engine = AIEnhancementEngine()
        quality_analyzer = AudioQualityAnalyzer()

        processed_audio = {}

        # Process voiceovers
        if voiceover_assets:
            voiceover_paths = [asset.local_path for asset in voiceover_assets]
            combined_voiceover = await audio_processor.combine_voiceover_files(voiceover_paths)

            if enable_ai_enhancement:
                enhanced_voiceover = ai_engine.apply_ai_enhancement(
                    combined_voiceover, "speech_enhancement"
                )
                processed_audio["voiceover"] = enhanced_voiceover
            else:
                processed_audio["voiceover"] = combined_voiceover

            # Analyze quality
            quality_analysis = quality_analyzer.perform_audio_quality_analysis(
                processed_audio["voiceover"]
            )
            logger.info(f"Voiceover quality analysis: {quality_analysis}")

        # Process background music
        if background_music_assets:
            bg_music_path = background_music_assets[0].local_path  # Use first background music
            looped_music = await audio_processor.create_seamless_loop(bg_music_path)
            processed_audio["background_music"] = looped_music

        # Process background sounds
        if background_sound_assets:
            bg_sound_paths = [asset.local_path for asset in background_sound_assets]
            combined_bg_sounds = await audio_processor.combine_background_sounds(bg_sound_paths)
            processed_audio["background_sounds"] = combined_bg_sounds

        return processed_audio

    async def _mix_final_video(
        self, stitched_video_path: str, processed_audio: Dict[str, str], enable_ducking: bool = True
    ) -> str:
        """Mix final video with all audio components using advanced audio mixer"""
        try:
            # Prepare audio paths for the advanced mixer
            background_music_paths = []
            voiceover_paths = []

            # Add background music if available
            if processed_audio.get("background_music"):
                background_music_paths.append(processed_audio["background_music"])

            # Add voiceover if available
            if processed_audio.get("voiceover"):
                voiceover_paths.append(processed_audio["voiceover"])

            # Generate output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"final_video_{timestamp}.mp4"

            # Use the advanced audio mixer from audio_stitcher.py
            logger.info("Using advanced audio mixer for final video processing")
            result = self.advanced_audio_mixer.mix_final_video_audio_enhanced(
                stitched_video_path=stitched_video_path,
                background_music_paths=background_music_paths if background_music_paths else None,
                voiceover_paths=voiceover_paths if voiceover_paths else None,
                output_filename=output_filename,
                enable_ducking=enable_ducking,
                enable_quality_analysis=True,
                enable_ai_enhancement=False,  # Can be enabled if needed
            )

            if result.get("success"):
                logger.info(f"Advanced audio mixing completed: {result['output_path']}")
                return result["output_path"]
            else:
                raise Exception(
                    f"Advanced audio mixing failed: {result.get('error', 'Unknown error')}"
                )

        except Exception as e:
            logger.error(f"Error in advanced audio mixing: {e}")
            # Fallback to basic mixing
            logger.info("Falling back to basic audio mixing")
            return await self._mix_final_video_basic(
                stitched_video_path, processed_audio, enable_ducking
            )

    async def _mix_final_video_basic(
        self, stitched_video_path: str, processed_audio: Dict[str, str], enable_ducking: bool = True
    ) -> str:
        """Fallback basic audio mixing method"""
        audio_processor = EnhancedAudioProcessor()

        # Load stitched video
        video_clip = VideoFileClip(stitched_video_path)

        # Create final audio mix
        final_audio_path = await audio_processor.create_final_audio_with_voiceover_and_music(
            video_clip=video_clip,
            voiceover_path=processed_audio.get("voiceover"),
            background_music_path=processed_audio.get("background_music"),
            background_sounds_path=processed_audio.get("background_sounds"),
            enable_ducking=enable_ducking,
        )

        # Create final video with mixed audio
        final_video_path = os.path.join(
            self.output_dir, f"final_video_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        )

        # Replace audio in video
        final_video = video_clip.with_audio(AudioFileClip(final_audio_path))
        final_video.write_videofile(final_video_path, codec="libx264", audio_codec="aac")

        # Close clips
        video_clip.close()
        final_video.close()

        return final_video_path

    async def _quality_check_and_upload(
        self, final_video_path: str, script_id: str
    ) -> Dict[str, Any]:
        """Perform quality check and upload final video"""
        quality_checker = QualityChecker()

        # Perform quality check
        quality_result = quality_checker.perform_quality_check(final_video_path)
        logger.info(f"Quality check result: {quality_result}")

        # Upload to S3 if s3_client is available
        s3_url = None
        if self.s3_client:
            try:
                s3_key = (
                    f"final_videos/{script_id}_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
                )
                await self.s3_client.upload_file(final_video_path, "assets-vidflux", s3_key)
                s3_url = f"s3://assets-vidflux/{s3_key}"
                logger.info(f"Uploaded final video to S3: {s3_url}")
            except Exception as e:
                logger.error(f"Failed to upload final video to S3: {e}")

        return {
            "success": True,
            "final_video_path": final_video_path,
            "s3_url": s3_url,
            "quality_result": quality_result,
        }

    def _update_progress(self, message: str, progress: int):
        """Update progress callback if available"""
        if self.progress_callback:
            try:
                if asyncio.iscoroutinefunction(self.progress_callback):
                    asyncio.create_task(self.progress_callback(progress, message))
                else:
                    self.progress_callback(progress, message)
            except Exception as e:
                logger.warning(f"Failed to update progress: {e}")

        logger.info(f"Progress {progress}%: {message}")

    async def process_complete_video(
        self,
        script_id: str,
        org_id: str,
        scenes_data: List[Dict[str, Any]],
        background_music_assets: List[Any] = None,
        enable_ducking: bool = True,
        enable_ai_enhancement: bool = False,
        enable_quality_analysis: bool = True,
    ) -> ProcessingResult:
        """
        Complete video processing pipeline with enhanced features
        """
        try:
            self._update_progress("Starting enhanced agentic video processing", 0)

            # Phase 1: Asset Download and Validation
            assets = await self._download_and_validate_assets(
                script_id, org_id, scenes_data, background_music_assets or []
            )
            self._update_progress("Assets downloaded and validated", 20)

            # Phase 2: Video Stitching with Intelligent Transitions
            stitched_video_path = await self._stitch_videos(assets["videos"], scenes_data)
            self._update_progress("Video stitching completed", 40)

            # Phase 3: Enhanced Audio Processing
            processed_audio = await self._process_audio(
                assets["voiceovers"],
                assets["background_sounds"],
                assets["background_music"],
                enable_ai_enhancement,
            )
            self._update_progress("Enhanced audio processing completed", 70)

            # Phase 4: Final Mixing with Quality Analysis
            final_video_path = await self._mix_final_video(
                stitched_video_path, processed_audio, enable_ducking
            )
            self._update_progress("Final mixing completed", 90)

            # Phase 5: Quality Check and Upload
            quality_result = await self._quality_check_and_upload(final_video_path, script_id)
            self._update_progress("Processing completed successfully", 100)

            return ProcessingResult(
                success=True,
                output_path=final_video_path,
                s3_url=quality_result.get("s3_url"),
                metadata={
                    "video_count": len(assets["videos"]),
                    "voiceover_count": len(assets["voiceovers"]),
                    "background_music_count": len(assets["background_music"]),
                    "background_sounds_count": len(assets["background_sounds"]),
                    "final_duration": quality_result.get("duration", 0),
                    "file_size_mb": quality_result.get("file_size_mb", 0),
                    "ai_enhancement_applied": enable_ai_enhancement,
                    "quality_analysis": processed_audio.get("quality_analysis"),
                },
            )

        except Exception as e:
            logger.error(f"Error in enhanced complete video processing: {str(e)}")
            return ProcessingResult(success=False, error_message=str(e))
        finally:
            # Cleanup temporary files
            await self._cleanup_temp_files()

    # ... (Include all other methods from the original agentic stitcher)
    # This is a simplified version - the full implementation would include
    # all the methods from the original agentic_video_stitcher.py

    def _update_progress(self, message: str, progress: int):
        """Update progress if callback is provided"""
        if self.progress_callback:
            self.progress_callback(message, progress)
        logger.info(f"Progress {progress}%: {message}")

    async def _cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            import shutil

            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            logger.info("Temporary files cleaned up")
        except Exception as e:
            logger.warning(f"Error cleaning up temp files: {e}")

    async def _download_file(self, s3_url: str, local_path: str, s3_bucket: str):
        """Download file from S3"""
        try:
            key = s3_url.split("amazonaws.com/")[-1]
            self.s3_client.download_file(s3_bucket, key, local_path)
            logger.info(f"Downloaded: {os.path.basename(local_path)}")
        except Exception as e:
            logger.error(f"Failed to download {s3_url}: {e}")
            raise
