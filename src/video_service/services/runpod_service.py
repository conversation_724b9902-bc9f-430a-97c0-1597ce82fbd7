"""
RunPod integration service for VidFlux video stitching
Handles communication with RunPod serverless endpoints
"""

import os
import requests
import asyncio
from typing import Dict, Any, Optional
from loguru import logger
from datetime import datetime


class RunPodService:
    """Service for interacting with RunPod serverless endpoints"""

    def __init__(self):
        self.api_key = os.getenv("RUNPOD_API_KEY")
        self.endpoint_id = os.getenv("RUNPOD_ENDPOINT_ID")
        self.base_url = "https://api.runpod.ai/v2"

        if not self.api_key:
            logger.warning("RUNPOD_API_KEY not set - RunPod integration disabled")
        if not self.endpoint_id:
            logger.warning("RUNPOD_ENDPOINT_ID not set - RunPod integration disabled")

    def is_configured(self) -> bool:
        """Check if RunPod is properly configured"""
        return bool(self.api_key and self.endpoint_id)

    async def submit_video_stitching_job(
        self,
        task_id: str,
        script_id: str,
        org_id: str,
        enable_ducking: bool = True,
        enable_ai_enhancement: bool = False,
        auto_audio_stitch: bool = True,
        webhook_url: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Submit a video stitching job to RunPod

        Args:
            task_id: The task ID for tracking
            script_id: Script ID to process
            org_id: Organization ID
            enable_ducking: Enable audio ducking
            enable_ai_enhancement: Enable AI audio enhancement
            auto_audio_stitch: Perform audio stitching after video stitching
            webhook_url: Optional webhook URL for completion notification

        Returns:
            Dict containing RunPod job information
        """
        if not self.is_configured():
            raise Exception("RunPod not properly configured")

        # Prepare the request payload
        payload = {
            "input": {
                "task_id": task_id,
                "script_id": script_id,
                "org_id": org_id,
                "enable_ducking": enable_ducking,
                "enable_ai_enhancement": enable_ai_enhancement,
                "auto_audio_stitch": auto_audio_stitch,
            }
        }

        # Add webhook if provided
        if webhook_url:
            payload["webhook"] = webhook_url

        # Set execution timeout to 30 minutes for video processing
        payload["policy"] = {
            "executionTimeout": 1800000,  # 30 minutes in milliseconds
            "lowPriority": False,
        }

        headers = {
            "Authorization": self.api_key,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        url = f"{self.base_url}/{self.endpoint_id}/run"

        try:
            logger.info(
                f"Submitting video stitching job to RunPod - Task: {task_id}, Script: {script_id}"
            )

            # Use asyncio to make the request non-blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, lambda: requests.post(url, json=payload, headers=headers, timeout=30)
            )

            if response.status_code == 200:
                result = response.json()
                logger.info(
                    f"Successfully submitted RunPod job: {result.get('id')} for task {task_id}"
                )
                return {
                    "status": "submitted",
                    "runpod_job_id": result.get("id"),
                    "task_id": task_id,
                    "submitted_at": datetime.utcnow().isoformat(),
                }
            else:
                error_msg = f"RunPod API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except requests.exceptions.Timeout:
            error_msg = "RunPod API request timeout"
            logger.error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"RunPod API request failed: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Failed to submit RunPod job: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    async def get_job_status(self, runpod_job_id: str) -> Dict[str, Any]:
        """
        Get the status of a RunPod job

        Args:
            runpod_job_id: The RunPod job ID

        Returns:
            Dict containing job status information
        """
        if not self.is_configured():
            raise Exception("RunPod not properly configured")

        headers = {"Authorization": self.api_key, "Accept": "application/json"}

        url = f"{self.base_url}/{self.endpoint_id}/status/{runpod_job_id}"

        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, lambda: requests.get(url, headers=headers, timeout=10)
            )

            if response.status_code == 200:
                return response.json()
            else:
                error_msg = f"RunPod status API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except requests.exceptions.Timeout:
            error_msg = "RunPod status API request timeout"
            logger.error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"RunPod status API request failed: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Failed to get RunPod job status: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    async def cancel_job(self, runpod_job_id: str) -> Dict[str, Any]:
        """
        Cancel a RunPod job

        Args:
            runpod_job_id: The RunPod job ID to cancel

        Returns:
            Dict containing cancellation result
        """
        if not self.is_configured():
            raise Exception("RunPod not properly configured")

        headers = {"Authorization": self.api_key, "Accept": "application/json"}

        url = f"{self.base_url}/{self.endpoint_id}/cancel/{runpod_job_id}"

        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, lambda: requests.post(url, headers=headers, timeout=10)
            )

            if response.status_code == 200:
                logger.info(f"Successfully cancelled RunPod job: {runpod_job_id}")
                return response.json()
            else:
                error_msg = f"RunPod cancel API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except requests.exceptions.RequestException as e:
            error_msg = f"RunPod cancel API request failed: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Failed to cancel RunPod job: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)


# Global instance
runpod_service = RunPodService()
