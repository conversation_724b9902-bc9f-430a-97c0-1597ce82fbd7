"""
RunPod Job Polling Service
Periodically checks RunPod job status and updates task queue
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from src.video_service.services.runpod_service import RunPodService
from src.shared.config.database import get_database_session
from src.shared.models.database_models import TaskQueue

logger = logging.getLogger(__name__)


class RunPodPollingService:
    """Service to poll RunPod jobs and update task status"""

    def __init__(self, poll_interval: int = 30):
        """
        Initialize polling service

        Args:
            poll_interval: Seconds between polls (default 30)
        """
        self.runpod_service = RunPodService()
        self.poll_interval = poll_interval
        self.is_running = False

    async def poll_pending_jobs(self) -> None:
        """Poll all pending RunPod jobs and update their status"""
        if not self.runpod_service.is_configured():
            logger.warning("RunPod not configured, skipping polling")
            return

        try:
            async for session in get_database_session():
                try:
                    # Find all processing tasks with RunPod job IDs
                    stmt = select(TaskQueue).where(
                        and_(TaskQueue.status == "processing", TaskQueue.result.is_not(None))
                    )
                    result = await session.execute(stmt)
                    tasks = result.scalars().all()

                    for task in tasks:
                        if task.result and "runpod_job_id" in task.result:
                            runpod_job_id = task.result["runpod_job_id"]
                            await self._check_and_update_job(session, task, runpod_job_id)

                    await session.commit()

                except Exception as e:
                    await session.rollback()
                    logger.error(f"Error polling RunPod jobs: {e}")
                finally:
                    break

        except Exception as e:
            logger.error(f"Database connection error during polling: {e}")

    async def _check_and_update_job(
        self, session: AsyncSession, task: TaskQueue, runpod_job_id: str
    ) -> None:
        """Check a specific RunPod job and update task status"""
        try:
            # Get job status from RunPod
            status_response = await self.runpod_service.get_job_status(runpod_job_id)
            runpod_status = status_response.get("status", "UNKNOWN")

            logger.info(f"Polling job {runpod_job_id}: {runpod_status}")

            # Update task based on RunPod status
            if runpod_status == "COMPLETED":
                task.status = "completed"
                task.progress = 100
                task.completed_at = datetime.utcnow()

                # Store output if available
                if "output" in status_response:
                    if not task.result:
                        task.result = {}
                    task.result.update(status_response["output"])

                logger.info(f"✅ Task {task.task_id} completed")

            elif runpod_status == "FAILED":
                task.status = "failed"
                task.error_message = (
                    f"RunPod job failed: {status_response.get('output', 'Unknown error')}"
                )
                task.completed_at = datetime.utcnow()

                logger.error(f"❌ Task {task.task_id} failed: {task.error_message}")

            elif runpod_status == "IN_PROGRESS":
                # Update progress if available
                if "progress" in status_response:
                    task.progress = min(
                        99, status_response["progress"]
                    )  # Keep at 99 until completed

                logger.info(f"⏳ Task {task.task_id} in progress: {task.progress}%")

            elif runpod_status == "IN_QUEUE":
                logger.info(f"⏳ Task {task.task_id} still in queue")

            else:
                logger.warning(f"⚠️ Unknown RunPod status for task {task.task_id}: {runpod_status}")

        except Exception as e:
            logger.error(f"Error checking RunPod job {runpod_job_id}: {e}")

    async def start_polling(self) -> None:
        """Start the polling loop"""
        if self.is_running:
            logger.warning("Polling already running")
            return

        self.is_running = True
        logger.info(f"Starting RunPod polling service (interval: {self.poll_interval}s)")

        try:
            while self.is_running:
                await self.poll_pending_jobs()
                await asyncio.sleep(self.poll_interval)
        except asyncio.CancelledError:
            logger.info("Polling service cancelled")
        except Exception as e:
            logger.error(f"Polling service error: {e}")
        finally:
            self.is_running = False
            logger.info("RunPod polling service stopped")

    def stop_polling(self) -> None:
        """Stop the polling loop"""
        self.is_running = False
        logger.info("Stopping RunPod polling service")


# Global polling service instance
polling_service = RunPodPollingService()


async def start_runpod_polling():
    """Start the RunPod polling service"""
    await polling_service.start_polling()


def stop_runpod_polling():
    """Stop the RunPod polling service"""
    polling_service.stop_polling()
