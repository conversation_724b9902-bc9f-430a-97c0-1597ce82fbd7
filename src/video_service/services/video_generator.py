"""
Enhanced LTX Video Generation module - Using FAL AI LTX Video (Latest)
Professional video generation with expert cinematography and character consistency
Maintains interface compatibility for seamless UI integration,
Uses professional Gemini prompts for cinematic excellence and character continuity
"""

# Global imports
import os
import json
import time
import base64
import requests
import fal_client
from io import BytesIO
from PIL import Image
from loguru import logger
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple

# Local imports
from src.shared.config.settings import settings
from src.shared.services.llm_service import llm_service


class KlingVideoGenerator:
    """Professional Video Generator using FAL AI's LTX Video (Latest) with expert cinematography and character consistency"""

    def __init__(self):
        """Initialize professional video generator with both LTX and Gemini clients"""
        logger.info("🎬 Initializing Professional LTX Video Generator with Cinematography AI...")

        # -- Enhanced LLM setup with professional video expertise --
        if not llm_service.is_available():
            raise RuntimeError(
                "❌ Could not initialize Cinematography LLM. No LLM providers available."
            )

        self.llm_service = llm_service
        self._last_generated_prompt = None  # Store the last generated prompt

        available_providers = llm_service.get_available_providers()
        logger.info(
            f"✅ Professional Cinematography LLM initialized with providers: {available_providers}"
        )

        # -- Professional LTX setup --
        load_dotenv()
        self.api_key = os.getenv("FAL_KEY")
        if not self.api_key:
            raise EnvironmentError(
                "❌ FAL_KEY not found in environment variables. Please add it to your .env file."
            )
        os.environ["FAL_KEY"] = self.api_key

        # Professional LTX Video (Latest) settings
        self.model_endpoint = "fal-ai/ltx-video/image-to-video"
        self.professional_settings = {
            "negative_prompt": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly",
            "num_inference_steps": 30,
            "guidance_scale": 3.0,
        }

        # Professional character tracking and location consistency
        self.character_profile = ""
        self.scene_context = []  # Track previous scenes for story continuity
        self.location_groups = {}  # Track location groups for background consistency

        # Mapping from old Kling params to new LTX params (for compatibility)
        self.kling_to_ltx_mapping = {
            "duration": None,  # Not supported in new LTX
            "cfg_scale": "guidance_scale",  # Maps to guidance_scale
            "aspect_ratio": None,  # Not supported in new LTX
            "tail_image_url": None,  # Not supported in new LTX
            # Legacy LTX parameters (no longer supported)
            "resolution": None,
            "number_of_frames": None,
            "frame_rate": None,
            "first_pass_number_of_steps": "num_inference_steps",  # Maps to num_inference_steps
            "first_pass_skip_final_steps": None,
            "second_pass_number_of_steps": None,
            "second_pass_skip_initial_steps": None,
            "expand_prompt": None,
            "reverse_video": None,
            "enable_safety_checker": None,
            "constant_rate_factor": None,
            "loras": None,
            # New LTX parameters
            "prompt": "prompt",
            "negative_prompt": "negative_prompt",
            "seed": "seed",
            "num_inference_steps": "num_inference_steps",
            "guidance_scale": "guidance_scale",
            "image_url": "image_url",
        }

        logger.info("🎭 Professional LTX video generator ready with character consistency support")

    def set_character_profile(self, character_profile: str):
        """Set character profile for consistency across all video scenes"""
        self.character_profile = character_profile

    def set_location_groups(self, location_groups: Dict[str, str]):
        """Set location groups for background consistency across videos"""
        self.location_groups = location_groups
        logger.info(f"🏢 Location groups set for video consistency: {len(location_groups)} groups")

    def _extract_location_context(self, scene_results: List[Dict]) -> Dict[str, str]:
        """
        Extract location group information from scene results for video consistency

        Args:
            scene_results: List of scene result dictionaries

        Returns:
            Dictionary mapping location groups to background descriptions
        """
        location_context = {}

        for scene in scene_results:
            # Check if scene has location group information from image prompts
            location_group = scene.get("location_group", "")
            location_background = scene.get("location_background", "")

            if location_group and location_background:
                location_context[location_group] = location_background
                logger.debug(f"📍 Extracted location context: {location_group}")

        if location_context:
            self.location_groups = location_context
            logger.info(f"🏢 Location context extracted: {len(location_context)} groups")

        return location_context

    def _download_and_process_image(self, image_url: str) -> Optional[Image.Image]:
        """
        Download image from URL and convert to PIL Image for professional analysis
        """
        try:
            logger.info(f"📥 Downloading image for professional analysis: {image_url}")
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # Convert to PIL Image
            image = Image.open(BytesIO(response.content))

            # Convert to RGB if necessary (handles RGBA, etc.)
            if image.mode != "RGB":
                image = image.convert("RGB")

            logger.info(f"✅ Image processed for cinematography analysis: {image.size}")
            return image

        except Exception as e:
            logger.error(f"❌ Error downloading/processing image: {e}")
            return None

    def _analyze_image_for_character_consistency(self, image: Image.Image) -> str:
        """
        Professional analysis of image to extract character details for consistency
        """
        try:
            logger.info("🎭 Analyzing image for character consistency...")

            character_analysis_prompt = """
            You are a PROFESSIONAL CHARACTER CONTINUITY SUPERVISOR for film production.
            
            Analyze this image and extract detailed character information to ensure consistency in video generation:
            
            **CHARACTER ANALYSIS REQUIRED:**
            1. **Physical Characteristics:**
               - Age range and gender
               - Ethnicity and skin tone
               - Hair color, style, length
               - Eye color
               - Facial features (distinctive elements)
               - Body type and posture
               - Height relative to environment
            
            2. **Clothing & Style:**
               - Detailed clothing descriptions
               - Colors and patterns
               - Accessories (glasses, jewelry, etc.)
               - Overall style (casual, formal, etc.)
            
            3. **Positioning & Context:**
               - Current pose and position
               - Interaction with environment
               - Emotional expression
               - Lighting on the character
            
            4. **Continuity Notes:**
               - Key visual elements that must remain consistent
               - Any props or objects associated with the character
               - Environmental context
            
            Provide a detailed character description that will ensure the SAME PERSON appears in the generated video with consistent appearance, clothing, and style.
            
            Be specific and professional - this analysis will be used for video production continuity.
            """

            result = self.llm_service.generate_content([character_analysis_prompt, image])
            if result["success"]:
                character_analysis = result["content"].strip()
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                character_analysis = "Character analysis failed"

            logger.info(
                f"✅ Character consistency analysis complete ({len(character_analysis)} chars)"
            )
            return character_analysis

        except Exception as e:
            logger.error(f"❌ Error in character analysis: {e}")
            return "Professional character with consistent appearance throughout the video"

    def _refine_video_prompt_with_image(
        self, scene_description: str, image_url: str, scene_number: int = 1
    ) -> str:
        """
        Professional cinematography AI analyzes the generated image and scene description
        to create expert-level, character-consistent video prompts optimized for LTX
        """
        try:
            logger.info(f"🎬 Professional video prompt generation for Scene {scene_number}")

            # Download and process the image
            image = self._download_and_process_image(image_url)

            if image is None:
                logger.warning(
                    "⚠️ Could not process image, falling back to professional text-only prompt generation"
                )
                return self._refine_video_prompt(scene_description, scene_number)

            # Analyze character consistency
            character_analysis = self._analyze_image_for_character_consistency(image)

            # Add scene to context for story flow
            scene_context = {
                "scene_number": scene_number,
                "description": scene_description,
                "character_analysis": character_analysis,
                "location_group": self._determine_scene_location_group(scene_description),
                "location_background": "",
            }

            # Add location background if available
            location_group = scene_context["location_group"]
            if location_group in self.location_groups:
                scene_context["location_background"] = self.location_groups[location_group]

            self.scene_context.append(scene_context)

            # Build professional cinematography instruction optimized for LTX
            instruction = f"""You are a PROFESSIONAL CINEMATOGRAPHER and VIDEO PROMPT ENGINEER with 20+ years of experience in commercial video production, specializing in LTX Video model.

Your expertise includes:
- Advanced camera movements and framing optimized for LTX Video
- Professional lighting and color grading
- Character consistency and continuity
- Cinematic storytelling through motion
- Technical video production standards for LTX model

**SCENE ANALYSIS:**
Scene Number: {scene_number}
Scene Description: {scene_description.strip()}

**CHARACTER CONSISTENCY REQUIREMENTS:**
Global Character Profile: {self.character_profile if self.character_profile else "Maintain character consistency from image"}
Current Image Character Analysis: {character_analysis}

**PREVIOUS SCENES CONTEXT:**
{self._format_scene_context() if len(self.scene_context) > 1 else "First scene - establish character and style"}

**LTX VIDEO PROMPT CREATION:**

1. **IMAGE ANALYSIS** - Carefully examine the provided image to understand:
   - Visual composition, lighting, and cinematographic mood
   - Character positioning, expressions, and body language
   - Environmental details and spatial relationships
   - Color palette, contrast, and visual style
   - Depth of field and focus points

2. **CHARACTER CONTINUITY** - Ensure video maintains:
   - EXACT same character appearance as analyzed above
   - Consistent clothing, hair, and physical features
   - Natural character movement that fits their personality
   - Proper character-environment interaction

3. **LTX-OPTIMIZED CINEMATOGRAPHY** - Create video with:
   - **Camera Work**: Smooth, cinematic movements (gentle dolly, pan, tilt, tracking shots)
   - **Motion Design**: Natural, fluid character and object movements
   - **Lighting Continuity**: Maintain and enhance existing lighting setup
   - **Depth & Focus**: Professional depth of field and focus pulls
   - **Color Grading**: Cinematic color enhancement
   - **Pacing**: Appropriate motion speed and natural flow

4. **LTX TECHNICAL SPECIFICATIONS**:
   - Optimized for LTX Video model
   - Focus on natural, realistic motion
   - Professional camera stabilization
   - Realistic physics and motion blur
   - Cinematic quality output
   - Smooth temporal consistency

**OUTPUT REQUIREMENTS:**
- Create a 120-180 word professional video prompt optimized for LTX
- Focus on realistic, smooth camera movements that enhance storytelling
- Specify character actions and environmental motion
- Include professional lighting and atmospheric effects
- Ensure character consistency with previous scenes
- Make motion feel natural, engaging, and cinematically professional
- Avoid text overlays, watermarks, or amateur elements
- Optimize for LTX's strengths in character animation and motion quality

**PROFESSIONAL STANDARDS:**
- Commercial-grade video production quality
- Authentic character representation and diversity
- Culturally respectful and appropriate content
- Brand-safe and professional presentation
- LTX model optimization

Provide ONLY the professional video prompt optimized for LTX Video generation. No explanations or additional text."""

            # Generate professional content with image and text
            result = self.llm_service.generate_content([instruction, image])
            if result["success"]:
                professional_prompt = result["content"].strip()
            else:
                logger.error(f"LLM service failed: {result.get('error')}")
                professional_prompt = scene_description  # Fallback to original description

            logger.info(
                f"✅ Professional LTX cinematography prompt generated: {professional_prompt[:100]}..."
            )
            return professional_prompt

        except Exception as e:
            logger.error(f"❌ Error in professional video prompt generation: {e}")
            logger.warning("⚠️ Falling back to professional text-only prompt generation")
            return self._refine_video_prompt(scene_description, scene_number)

    def _format_scene_context(self) -> str:
        """Format previous scenes context for continuity"""
        if len(self.scene_context) <= 1:
            return "First scene - establish cinematic style and character"

        context_lines = []
        for scene in self.scene_context[:-1]:  # Exclude current scene
            context_lines.append(f"Scene {scene['scene_number']}: {scene['description'][:50]}...")

        return "Previous scenes: " + " | ".join(context_lines[-3:])  # Last 3 scenes for context

    def _refine_video_prompt(self, description: str, scene_number: int = 1) -> str:
        """
        Professional fallback: Use Cinematography AI to create expert video prompts (text-only version) optimized for LTX
        """
        logger.info(f"🎬 Creating professional text-only video prompt for Scene {scene_number}")

        instruction = f"""You are a PROFESSIONAL CINEMATOGRAPHER and VIDEO PROMPT ENGINEER specializing in commercial video production with LTX Video model.

**PROFESSIONAL VIDEO PROMPT CREATION**

Scene Number: {scene_number}
Scene Description: {description.strip()}

**CHARACTER CONSISTENCY:**
Character Profile: {self.character_profile if self.character_profile else "Maintain consistent character appearance throughout"}

**SCENE CONTEXT:**
{self._format_scene_context()}

**EXPERT CINEMATOGRAPHY REQUIREMENTS FOR LTX:**

Transform the scene description into a professional, commercial-grade video prompt for LTX Video generation.

**FOCUS AREAS:**
1. **Professional Camera Work:**
   - Smooth, cinematic camera movements (gentle dolly, pan, tilt, tracking shots)
   - Professional framing and composition
   - Dynamic but stable motion optimized for LTX
   - Appropriate lens choices and focal lengths

2. **Character & Motion:**
   - Natural, realistic character movements
   - Professional acting and expressions
   - Consistent character appearance from previous scenes
   - Authentic human interaction and behavior

3. **Technical Excellence:**
   - Professional lighting setup and mood
   - Cinematic color grading and atmosphere
   - High-quality commercial standards
   - Smooth motion with proper physics
   - Professional depth of field effects

4. **Production Quality:**
   - Commercial advertisement standards
   - Broadcast-ready visual quality
   - Professional audio-visual synchronization ready
   - Culturally authentic and respectful representation

**LTX TECHNICAL SPECIFICATIONS:**
- Optimized for LTX Video model
- Quality: Commercial-grade output
- Style: Cinematic, engaging, professional
- Motion: Natural, fluid, purposeful
- Character: Consistent with established profile

**RESTRICTIONS:**
- NO text overlays or watermarks
- NO amateur or low-quality elements
- NO cultural stereotypes or inappropriate content
- NO choppy or unnatural motion

Create a 120-180 word professional video prompt that delivers commercial-quality results with LTX Video.

Provide ONLY the refined video prompt, nothing else."""

        try:
            result = self.llm_service.generate_content(instruction)
            if result["success"]:
                professional_prompt = result["content"].strip()
                logger.info(
                    f"✅ Professional text-only prompt created: {professional_prompt[:100]}..."
                )
                return professional_prompt
            else:
                logger.error(f"AI service failed: {result.get('error')}")
                return scene_description  # Fallback to original description
        except Exception as e:
            logger.error(f"❌ Error in professional text-only prompt generation: {e}")
            return f"Professional cinematic video of {description}, smooth camera movement, natural lighting, commercial quality"

    def _determine_scene_location_group(self, scene_description: str) -> str:
        """
        Determine location group from scene description for video consistency

        Args:
            scene_description: Scene description text

        Returns:
            Location group identifier
        """
        # Check for explicit location group mentions
        desc_lower = scene_description.lower()

        if "location 1" in desc_lower:
            return "Location_1"
        elif "location 2" in desc_lower:
            return "Location_2"
        elif "location 3" in desc_lower:
            return "Location_3"
        elif "location 4" in desc_lower:
            return "Location_4"

        # Fallback to location group detection based on scene context
        scene_num = len(self.scene_context) + 1
        if scene_num <= 2:
            return "Location_1"
        elif scene_num <= 4:
            return "Location_2"
        elif scene_num <= 6:
            return "Location_3"
        else:
            return "Location_4"

    def on_queue_update(self, update):
        """Handle LTX queue logs with professional formatting"""
        if isinstance(update, fal_client.InProgress):
            for log in update.logs:
                logger.info(f"🎬 Professional Video Production: {log['message']}")

    def _map_params_to_ltx(self, params: Dict) -> Dict:
        """Map various parameter formats to new LTX parameters"""
        ltx_params = {}

        for key, val in params.items():
            # Direct LTX parameters (new API)
            if key in [
                "prompt",
                "image_url",
                "negative_prompt",
                "seed",
                "num_inference_steps",
                "guidance_scale",
            ]:
                ltx_params[key] = val

            # Map legacy parameters to new LTX parameters
            elif key == "cfg_scale":
                # Map cfg_scale to guidance_scale
                try:
                    ltx_params["guidance_scale"] = float(val)
                except:
                    ltx_params["guidance_scale"] = 3.0

            elif key in ["first_pass_number_of_steps", "inference_steps"]:
                # Map to num_inference_steps
                try:
                    ltx_params["num_inference_steps"] = int(val)
                except:
                    ltx_params["num_inference_steps"] = 30

            # Handle legacy parameters that are no longer supported
            elif key in [
                "duration",
                "resolution",
                "aspect_ratio",
                "number_of_frames",
                "frame_rate",
                "first_pass_skip_final_steps",
                "second_pass_number_of_steps",
                "second_pass_skip_initial_steps",
                "expand_prompt",
                "reverse_video",
                "enable_safety_checker",
                "constant_rate_factor",
                "loras",
                "tail_image_url",
            ]:
                # These parameters are not supported in the new LTX API
                logger.info(f"ℹ️ Parameter '{key}' not supported in new LTX model, ignoring")

        return ltx_params

    def _frames_to_duration(self, frames: int, fps: int = 30) -> float:
        """Convert number of frames to duration in seconds (for backward compatibility)"""
        return frames / fps

    def create_video_task(self, input_data: Dict, config: Optional[Dict] = None) -> Dict:
        """
        Create a professional video generation task using LTX
        """
        try:
            validated = self._validate_and_fix_input(input_data)
            if not validated["valid"]:
                return {"success": False, "error": validated["error"]}

            args = {**self.professional_settings}
            args.update(self._map_params_to_ltx(validated["data"]))
            if config:
                args.update(self._map_params_to_ltx(config))

            logger.info("🚀 Creating professional LTX video task")
            logger.info(f"📋 Professional Arguments: {json.dumps(args, indent=2)}")

            handler = fal_client.submit(self.model_endpoint, arguments=args)
            logger.info(f"✅ Professional video task submitted: {handler.request_id}")

            return {
                "success": True,
                "task_id": handler.request_id,
                "status": "submitted",
                "model_endpoint": self.model_endpoint,
                "arguments": args,
            }

        except Exception as e:
            err = f"Exception during professional video task creation: {e}"
            logger.error(f"❌ {err}")
            return {"success": False, "error": err}

    def _validate_and_fix_input(self, input_data: Dict) -> Dict:
        """
        Professional validation to ensure prompt and image_url exist with quality standards
        """
        try:
            data = {}
            prompt = input_data.get("prompt", "").strip()
            if not prompt:
                return {
                    "valid": False,
                    "error": "Professional text prompt is required for video generation",
                }
            data["prompt"] = prompt

            url = input_data.get("image_url", input_data.get("image", "")).strip()
            if not url:
                return {
                    "valid": False,
                    "error": "High-quality image URL is required for professional image-to-video generation",
                }
            data["image_url"] = url

            # Professional overrides with new LTX parameter names
            if "negative_prompt" in input_data:
                data["negative_prompt"] = input_data["negative_prompt"]

            # Handle seed parameter
            if "seed" in input_data:
                try:
                    data["seed"] = int(input_data["seed"])
                except:
                    pass

            # Handle num_inference_steps parameter
            if "num_inference_steps" in input_data:
                try:
                    steps_val = int(input_data["num_inference_steps"])
                    data["num_inference_steps"] = max(1, min(100, steps_val))  # Reasonable range
                except:
                    data["num_inference_steps"] = 30  # Default

            # Handle guidance_scale parameter
            if "guidance_scale" in input_data:
                try:
                    guidance_val = float(input_data["guidance_scale"])
                    data["guidance_scale"] = max(1.0, min(20.0, guidance_val))  # Reasonable range
                except:
                    data["guidance_scale"] = 3.0  # Default

            logger.info(f"🔧 Professional input validation complete: {json.dumps(data, indent=2)}")
            return {"valid": True, "data": data}

        except Exception as e:
            return {
                "valid": False,
                "error": f"Professional input validation error: {e}",
            }

    def get_task_status(self, task_id: str) -> Dict:
        """Check professional LTX task status"""
        try:
            logger.info(f"🔍 Checking professional video task status: {task_id}")
            status = fal_client.status(self.model_endpoint, task_id, with_logs=True)
            logger.info(f"📊 Professional Status: {status}")
            return {"success": True, "data": status}
        except Exception as e:
            return {
                "success": False,
                "error": f"Exception during professional status check: {e}",
            }

    def get_task_result(self, task_id: str) -> Dict:
        """Fetch professional LTX task result"""
        try:
            logger.info(f"📥 Fetching professional video result: {task_id}")
            result = fal_client.result(self.model_endpoint, task_id)
            logger.info(f"📊 Professional Result: {result}")
            return {"success": True, "data": result}
        except Exception as e:
            return {
                "success": False,
                "error": f"Exception during professional result fetch: {e}",
            }

    def wait_for_completion(
        self, task_id: str, max_wait_time: int = 600, check_interval: int = 15
    ) -> Dict:
        """Poll until professional video completion or timeout"""
        logger.info(f"⏳ Waiting for professional video task {task_id} to complete…")
        start = time.time()
        while time.time() - start < max_wait_time:
            st = self.get_task_status(task_id)
            if not st["success"]:
                return st
            state = st["data"].get("status", "unknown")
            logger.info(f"📊 Professional Video Status: {state}")
            if state == "COMPLETED":
                return self.get_task_result(task_id)
            if state == "FAILED":
                return {
                    "success": False,
                    "status": "failed",
                    "error": st["data"].get("error"),
                }
            time.sleep(check_interval)
        return {
            "success": False,
            "status": "timeout",
            "error": f"Professional video did not complete within {max_wait_time}s",
        }

    def generate_video_simple(self, scene_data: Dict) -> Dict:
        """
        Simple video generation that uses subscribe approach
        """
        try:
            num = scene_data.get("scene_number", "Unknown")
            desc = scene_data.get("scene_description", "").strip()

            # Get the first image URL from the scene
            imgs = scene_data.get("images", [])
            image_url = imgs[0].get("url", "") if imgs else ""

            if not image_url:
                return {
                    "success": False,
                    "error": "No high-quality image URL available for professional video analysis",
                    "video_url": None,
                }

            logger.info(f"🎬 SIMPLE VIDEO GENERATION for scene {num}")
            logger.info(f"📝 Description: {desc}")
            logger.info(f"🖼️ Image: {image_url}")

            # Generate professional prompt
            professional_prompt = self._refine_video_prompt_with_image(
                desc or "Professional cinematic video scene", image_url, num
            )

            # Store the prompt for later retrieval
            self._last_generated_prompt = professional_prompt
            logger.info(f"✨ Professional prompt: {professional_prompt[:150]}...")

            # Use subscribe approach (simple and direct)
            video_result = self.generate_video_with_subscribe(
                image_url=image_url, prompt=professional_prompt
            )

            if not video_result.get("success"):
                return {
                    "success": False,
                    "error": video_result.get("error", "Video generation failed"),
                    "video_url": None,
                }

            # Extract video URL from result
            data = video_result.get("data", {})
            video_url = None
            if isinstance(data.get("video"), dict):
                video_url = data["video"].get("url")
            video_url = video_url or data.get("video_url")

            if video_url:
                logger.info(f"✅ Video generated successfully for Scene {num}")
                return {"success": True, "video_url": video_url, "status": "completed"}
            else:
                return {
                    "success": False,
                    "error": "Video URL not found in result",
                    "video_url": None,
                }

        except Exception as e:
            error_msg = f"Exception during simple video generation: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "video_url": None,
            }

    def generate_video_non_blocking(self, scene_data: Dict) -> Dict:
        """
        Non-blocking video generation that creates a task and returns immediately.
        This allows the FastAPI server to remain responsive while video generation happens in background.
        """
        try:
            num = scene_data.get("scene_number", "Unknown")
            desc = scene_data.get("scene_description", "").strip()

            # Get the first image URL from the scene
            imgs = scene_data.get("images", [])
            image_url = imgs[0].get("url", "") if imgs else ""

            if not image_url:
                logger.error(f"❌ No image URL found for scene {num}")
                return {
                    "success": False,
                    "error": "No high-quality image URL available for professional video analysis",
                    "video_url": None,
                }

            logger.info(f"🎬 NON-BLOCKING SCENE {num} PROCESSING")
            logger.info(f"📝 Original description: {desc}")
            logger.info(f"🖼️ Source image: {image_url}")

            # 1) Use Professional Cinematography AI to analyze image and refine prompt
            try:
                professional_prompt = self._refine_video_prompt_with_image(
                    desc or "Professional cinematic video scene", image_url, num
                )
                logger.info(
                    f"✨ Professional cinematography prompt: {professional_prompt[:150]}..."
                )
            except Exception as e:
                logger.error(f"❌ Error refining prompt for scene {num}: {e}")
                return {
                    "success": False,
                    "error": f"Failed to refine video prompt: {e}",
                    "video_url": None,
                }

            # 2) Create video task without waiting for completion
            input_data = {
                "image_url": image_url.strip(),
                "prompt": professional_prompt.strip(),
                **self.professional_settings,
            }

            logger.info(f"🎬 Creating video task for scene {num}...")
            # Create the task using task-based approach (not subscribe)
            task = self.create_video_task(input_data)
            if not task.get("success"):
                logger.error(f"❌ Failed to create video task for scene {num}: {task.get('error')}")
                return {
                    "success": False,
                    "error": task.get("error", "Failed to create video generation task"),
                    "video_url": None,
                }

            task_id = task["task_id"]
            logger.info(f"🎬 Video generation task created for scene {num}: {task_id}")

            # 3) Poll for completion in background (non-blocking)
            # This will be handled by a separate background task or polling mechanism
            # For now, we'll use a simple approach with shorter timeouts

            # Wait for a reasonable time for the task to complete
            max_wait_time = 300  # 5 minutes
            check_interval = 10  # Check every 10 seconds
            start_time = time.time()

            logger.info(f"⏳ Starting polling for scene {num} task {task_id}...")

            while time.time() - start_time < max_wait_time:
                try:
                    status_result = self.get_task_status(task_id)
                    if not status_result.get("success"):
                        logger.error(
                            f"❌ Failed to get status for scene {num} task {task_id}: {status_result.get('error')}"
                        )
                        return {
                            "success": False,
                            "error": status_result.get("error", "Failed to get task status"),
                            "video_url": None,
                        }

                    state = status_result["data"].get("status", "unknown")
                    logger.info(f"📊 Video Status for Scene {num}: {state}")

                    if state == "COMPLETED":
                        logger.info(f"✅ Task completed for scene {num}, fetching result...")
                        # Get the result
                        result = self.get_task_result(task_id)
                        if result.get("success"):
                            data = result.get("data", {})
                            # Extract video URL
                            video_url = None
                            if isinstance(data.get("video"), dict):
                                video_url = data["video"].get("url")
                            video_url = video_url or data.get("video_url")

                            if video_url:
                                logger.info(
                                    f"✅ Video generated successfully for Scene {num}: {video_url}"
                                )
                                return {
                                    "success": True,
                                    "video_url": video_url,
                                    "task_id": task_id,
                                    "status": "completed",
                                }
                            else:
                                logger.error(
                                    f"❌ Video URL not found in completed task for scene {num}"
                                )
                                return {
                                    "success": False,
                                    "error": "Video URL not found in completed task",
                                    "video_url": None,
                                }
                        else:
                            logger.error(
                                f"❌ Task completed but failed for scene {num}: {result.get('error')}"
                            )
                            return {
                                "success": False,
                                "error": result.get("error", "Task completed but failed"),
                                "video_url": None,
                            }

                    elif state == "FAILED":
                        error_msg = status_result["data"].get("error", "Video generation failed")
                        logger.error(f"❌ Task failed for scene {num}: {error_msg}")
                        return {
                            "success": False,
                            "error": error_msg,
                            "video_url": None,
                        }

                    # Wait before next check
                    time.sleep(check_interval)

                except Exception as e:
                    logger.error(f"❌ Error during polling for scene {num}: {e}")
                    time.sleep(check_interval)
                    continue

            # Timeout reached
            logger.error(
                f"❌ Video generation timed out for scene {num} after {max_wait_time} seconds"
            )
            return {
                "success": False,
                "error": f"Video generation timed out after {max_wait_time} seconds",
                "video_url": None,
            }

        except Exception as e:
            error_msg = f"Exception during non-blocking video generation: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "video_url": None,
            }

    def generate_videos_from_scene_images(
        self, scene_results: List[Dict], use_subscribe: bool = True, **kwargs
    ) -> List[Dict]:
        """
        Professional workflow: For each scene, analyze image + refine description via Cinematography AI, then generate professional video
        Enhanced workflow: Script -> Professional Image -> (Cinematography AI analyzes Image + Scene) -> Professional Video
        """
        logger.info("🎬 STARTING PROFESSIONAL VIDEO GENERATION WORKFLOW")
        logger.info(f"📊 Processing {len(scene_results)} scenes with cinematography AI")

        # Extract location context for consistency
        if not self.location_groups:
            self._extract_location_context(scene_results)

        video_results = []

        for scene in scene_results:
            num = scene.get("scene_number", "Unknown")
            desc = scene.get("scene_description", "").strip()

            # Get the first image URL from the scene
            imgs = scene.get("images", [])
            image_url = imgs[0].get("url", "") if imgs else ""

            if not image_url:
                video_results.append(
                    {
                        "scene_number": num,
                        "success": False,
                        "error": "No high-quality image URL available for professional video analysis",
                        "video_url": None,
                    }
                )
                continue

            logger.info(f"🎬 PROFESSIONAL SCENE {num} PROCESSING")
            logger.info(f"📝 Original description: {desc}")
            logger.info(f"🖼️ Source image: {image_url}")

            # 1) Use Professional Cinematography AI to analyze image and refine prompt
            professional_prompt = self._refine_video_prompt_with_image(
                desc or "Professional cinematic video scene", image_url, num
            )
            logger.info(f"✨ Professional cinematography prompt: {professional_prompt[:150]}...")

            # 2) Generate professional video with enhanced prompt and image
            if use_subscribe:
                video_result = self.generate_video_with_subscribe(
                    image_url=image_url, prompt=professional_prompt, **kwargs
                )
            else:
                video_result = self.generate_video_from_image(
                    image_url=image_url, prompt=professional_prompt, **kwargs
                )

            # 3) Collect professional results with enhanced error info
            result = {
                "scene_number": num,
                "scene_description": desc,
                "image_url": image_url,
                "professional_prompt": professional_prompt,
                "success": video_result.get("success", False),
                "status": video_result.get("status", "unknown"),
                "video_url": None,
                "error": video_result.get("error"),
                "task_id": video_result.get("task_id"),
                "quality": "professional" if video_result.get("success") else "failed",
            }

            # Extract video URL if successful
            if video_result.get("success") and video_result.get("status") == "completed":
                data = video_result.get("data", {})
                # New LTX returns video at data["video"]["url"]
                video_url = None
                if isinstance(data.get("video"), dict):
                    video_url = data["video"].get("url")
                video_url = video_url or data.get("video_url")
                result["video_url"] = video_url
                logger.info(f"✅ Professional video generated for Scene {num}")
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(
                    f"❌ Professional video generation failed for Scene {num}: {error_msg}"
                )

            video_results.append(result)

            # Professional pacing between requests
            time.sleep(3)

        # Professional summary
        success_count = sum(1 for r in video_results if r.get("success"))
        logger.info(f"🎯 PROFESSIONAL VIDEO GENERATION COMPLETE")
        logger.info(
            f"📊 Results: {success_count}/{len(video_results)} professional videos generated"
        )
        logger.info(f"🏆 Success Rate: {success_count/len(video_results)*100:.1f}%")

        return video_results

    def generate_video_from_image(self, image_url: str, prompt: str = "", **kwargs) -> Dict:
        """
        Generate professional video from image + prompt using LTX
        (wrapper that does create_task + wait_for_completion)
        """
        if not image_url or not image_url.strip():
            return {
                "success": False,
                "error": "High-quality image URL is required for professional image-to-video generation",
            }
        if not prompt or not prompt.strip():
            return {
                "success": False,
                "error": "Professional text prompt is required for video generation",
            }

        input_data = {"image_url": image_url.strip(), "prompt": prompt.strip()}
        input_data.update(kwargs)

        logger.info("🎬 Generating professional video with LTX model")

        task = self.create_video_task(input_data)
        if not task.get("success"):
            return task

        # Wait and return professional outcome
        result = self.wait_for_completion(task["task_id"])
        return {
            "task_id": task["task_id"],
            **result,
        }

    def generate_video_with_subscribe(self, image_url: str, prompt: str = "", **kwargs) -> Dict:
        """
        Professional blocking, subscribe-based video generation with LTX
        """
        try:
            if not image_url or not image_url.strip():
                return {
                    "success": False,
                    "error": "High-quality image URL is required for professional image-to-video generation",
                }
            if not prompt or not prompt.strip():
                return {
                    "success": False,
                    "error": "Professional text prompt is required for video generation",
                }

            # Build professional arguments
            arguments = {
                **self.professional_settings,
                "image_url": image_url.strip(),
                "prompt": prompt.strip(),
            }
            arguments.update(self._map_params_to_ltx(kwargs))

            logger.info("🎬 Generating professional video with LTX model (subscribe)")
            logger.info(f"📋 Arguments: {json.dumps(arguments, indent=2)}")

            result = fal_client.subscribe(
                self.model_endpoint,
                arguments=arguments,
                with_logs=True,
                on_queue_update=self.on_queue_update,
            )
            logger.info("✅ Professional video generation completed!")
            return {"success": True, "status": "completed", "data": result}

        except Exception as e:
            error_msg = f"Exception during professional video generation: {e}"
            logger.error(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

    def save_videos_locally(
        self, video_results: List[Dict], output_dir: str = "professional_videos"
    ) -> List[Dict]:
        """
        Download each professional video and save it under professional output directory
        """
        os.makedirs(output_dir, exist_ok=True)
        updated = []
        for r in video_results:
            if not r.get("success") or not r.get("video_url"):
                updated.append(r)
                continue

            try:
                url = r["video_url"]
                scene = r.get("scene_number", "unknown")
                filename = f"scene_{scene}_professional_ltx_video.mp4"
                path = os.path.join(output_dir, filename)

                logger.info(f"💾 Downloading professional video for Scene {scene}...")
                resp = requests.get(url, timeout=120)
                resp.raise_for_status()

                with open(path, "wb") as f:
                    f.write(resp.content)

                r["local_path"] = path
                logger.info(f"✅ Professional video saved: {path}")

            except Exception as e:
                r["save_error"] = str(e)
                logger.error(f"❌ Error saving professional video for Scene {scene}: {e}")

            updated.append(r)
        return updated

    def format_video_results_for_display(self, video_results: List[Dict]) -> str:
        """
        Build professional summary of all scene results with quality assessment
        """
        if not video_results:
            return "No professional video generation results available."

        lines = []
        total = len(video_results)
        success_count = sum(1 for r in video_results if r.get("success"))
        fail_count = total - success_count

        lines.append("=" * 80)
        lines.append("🎬 PROFESSIONAL VIDEO GENERATION SUMMARY")
        lines.append("🎭 Cinematography AI + LTX Video (Latest)")
        lines.append("=" * 80)
        lines.append(f"📊 Total Scenes: {total}")
        lines.append(f"✅ Professional Videos: {success_count}")
        lines.append(f"❌ Failed: {fail_count}")
        lines.append(f"🏆 Professional Success Rate: {success_count/total*100:.1f}%")
        lines.append(f"🎯 Quality Standard: Commercial-grade production")
        lines.append("")

        for r in video_results:
            num = r.get("scene_number", "Unknown")
            lines.append(f"🎬 SCENE {num}")
            lines.append("-" * 40)

            if r.get("success"):
                lines.append("✅ PROFESSIONAL SUCCESS")
                lines.append(f"🖼️ Source Image: {r.get('image_url', 'N/A')}")
                lines.append(f"📝 Original Scene: {r.get('scene_description', 'N/A')[:100]}...")
                lines.append(
                    f"🎭 Cinematography AI Prompt: {r.get('professional_prompt', 'N/A')[:100]}..."
                )
                lines.append(f"🎥 Professional Video: {r.get('video_url')}")
                lines.append(f"🆔 Task ID: {r.get('task_id')}")
                lines.append(f"🏆 Quality: {r.get('quality', 'professional')}")
                if r.get("local_path"):
                    lines.append(f"💾 Saved: {r['local_path']}")
                if r.get("save_error"):
                    lines.append(f"⚠️ Save error: {r['save_error']}")
            else:
                lines.append("❌ PROFESSIONAL GENERATION FAILED")
                lines.append(f"🚫 Error: {r.get('error')}")
            lines.append("")

        lines.append("🎬 Professional video generation workflow complete!")
        return "\n".join(lines)

    def test_api_connection(self) -> Tuple[bool, str]:
        """
        Professional API test: generate a high-quality test video with reliable settings
        """
        try:
            logger.info("🧪 Testing professional LTX Video + Cinematography AI connection...")
            # Use the test image from the documentation
            test_image = "https://fal.media/files/kangaroo/4OePu2ifG7SKxTM__TQrQ_72929fec9fb74790bb8c8b760450c9b9.jpg"
            test_prompt = "Professional cinematic video with smooth camera movement, natural lighting, and commercial-grade production quality"

            result = self.generate_video_with_subscribe(
                image_url=test_image,
                prompt=test_prompt,
                num_inference_steps=30,
                guidance_scale=3.0,
            )
            if result.get("success"):
                return (
                    True,
                    "✅ Professional FAL AI LTX Video + Cinematography AI test successful! Ready for commercial-grade video production.",
                )
            else:
                return False, f"❌ Professional test failed: {result.get('error')}"

        except Exception as e:
            return False, f"❌ Professional test exception: {e}"


# Aliases for backward compatibility
LTXVideoGenerator = KlingVideoGenerator
WanXVideoGenerator = KlingVideoGenerator
