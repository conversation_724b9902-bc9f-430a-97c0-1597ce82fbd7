# Global imports
import os
import uuid
import glob
import shutil
import logging
import tempfile
import subprocess
import numpy as np
from pathlib import Path
from pydub.utils import which
from pydub import AudioSegment
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from moviepy import afx, vfx  # Import effects modules
from moviepy import VideoFile<PERSON>lip, AudioFileClip, CompositeAudioClip, concatenate_audioclips

# Local imports
from src.shared.utils.s3_client import S3Client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Optional librosa import with fallback
try:
    import librosa

    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("⚠️ librosa not available - using fallback audio analysis")


class AudioActivityDetector:
    """
    Audio activity detector that identifies and removes silence gaps for seamless audio looping.
    Designed to handle AI-generated music that often contains silence at the end.
    """

    def __init__(self, silence_threshold: float = 0.01, min_silence_duration: float = 0.1):
        self.silence_threshold = silence_threshold
        self.min_silence_duration = min_silence_duration

    def detect_audio_regions(self, audio_clip) -> list:
        """
        Detect regions in the audio that contain actual audio content.

        Returns:
            List of tuples (start_time, end_time) for audio regions
        """
        try:
            # Convert audio to numpy array
            audio_array = audio_clip.to_soundarray()
            fps = audio_clip.fps

            frame_size = int(fps * 0.01)  # 10ms frames
            audio_regions = []

            # Handle mono and stereo audio
            if len(audio_array.shape) == 1:
                audio_data = audio_array
            else:
                audio_data = np.max(np.abs(audio_array), axis=1)

            # Analyze audio in small frames
            is_audio_active = []
            for i in range(0, len(audio_data), frame_size):
                frame = audio_data[i : i + frame_size]
                if len(frame) > 0:
                    rms = np.sqrt(np.mean(frame**2))
                    is_audio_active.append(rms > self.silence_threshold)
                else:
                    is_audio_active.append(False)

            # Find continuous audio regions
            current_start = None
            frame_duration = frame_size / fps

            for i, is_active in enumerate(is_audio_active):
                time_pos = i * frame_duration

                if is_active and current_start is None:
                    current_start = time_pos
                elif not is_active and current_start is not None:
                    region_duration = time_pos - current_start
                    if region_duration > self.min_silence_duration:
                        audio_regions.append((current_start, time_pos))
                    current_start = None

            if current_start is not None:
                audio_regions.append((current_start, len(audio_data) / fps))

            logger.info(f"Detected {len(audio_regions)} audio regions: {audio_regions}")
            return audio_regions

        except Exception as e:
            logger.error(f"Error detecting audio regions: {e}")
            return [(0, audio_clip.duration)]

    def extract_audio_content(self, audio_clip):
        """
        Extract only the parts of the audio that contain actual content (no silence).

        Returns:
            AudioFileClip with silence removed
        """
        try:
            audio_regions = self.detect_audio_regions(audio_clip)

            if not audio_regions:
                logger.warning("No audio regions detected, using original clip")
                return audio_clip

            if len(audio_regions) == 1:
                start, end = audio_regions[0]
                start = max(0, start - 0.1)
                end = min(audio_clip.duration, end + 0.1)

                trimmed_clip = audio_clip.subclipped(start, end)
                logger.info(
                    f"Trimmed audio from {audio_clip.duration:.2f}s to {trimmed_clip.duration:.2f}s"
                )
                return trimmed_clip

            # Multiple regions - concatenate them
            audio_segments = []
            for start, end in audio_regions:
                start = max(0, start - 0.05)
                end = min(audio_clip.duration, end + 0.05)
                segment = audio_clip.subclipped(start, end)
                audio_segments.append(segment)

            if audio_segments:
                combined_audio = concatenate_audioclips(audio_segments)
                logger.info(
                    f"Combined {len(audio_segments)} audio regions: {audio_clip.duration:.2f}s → {combined_audio.duration:.2f}s"
                )
                return combined_audio
            else:
                return audio_clip

        except Exception as e:
            logger.error(f"Error extracting audio content: {e}")
            return audio_clip

    def create_seamless_loop(self, audio_clip, target_duration: float):
        """
        Create a seamless loop of the audio content for the target duration.

        Returns:
            AudioFileClip looped to target duration
        """
        try:
            if audio_clip.duration >= target_duration:
                return audio_clip.subclipped(0, target_duration)

            loops_needed = int(target_duration / audio_clip.duration) + 1
            loops = [audio_clip] * loops_needed
            looped_audio = concatenate_audioclips(loops)
            final_audio = looped_audio.subclipped(0, target_duration)

            logger.info(
                f"Created seamless loop: {audio_clip.duration:.2f}s × {loops_needed} = {final_audio.duration:.2f}s"
            )
            return final_audio

        except Exception as e:
            logger.error(f"Error creating seamless loop: {e}")
            loops_needed = int(target_duration / audio_clip.duration) + 1
            loops = [audio_clip] * loops_needed
            looped_audio = concatenate_audioclips(loops)
            return looped_audio.subclipped(0, target_duration)


# Optional pydub import with fallback
try:
    from pydub import AudioSegment

    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("⚠️ pydub not available - some features may not work")


class EnhancedVideoAudioMixer:
    """
    Enhanced Video Audio Mixer for VidFlux Pipeline
    Fixed version with improved fade effects and optional AI enhancement
    WITHOUT aggressive normalization that breaks audio quality
    Now includes voiceover combining functionality for TTS files
    """

    def __init__(self, output_dir: str = "output/final_videos"):
        self.output_dir = output_dir
        self.ensure_output_directory()

        # Enhanced audio mixing settings
        self.voiceover_volume = 0.8  # Higher volume for voice-over
        self.background_music_volume = 0.3  # More audible background music
        self.existing_audio_volume = 0.2  # More noticeable existing background sounds

        # IMPROVED: Better fade settings (less aggressive)
        self.fade_in_duration = 0.1  # Shorter fade for less distortion
        self.fade_out_duration = 0.1
        self.crossfade_duration = 0.1  # Minimal crossfade for voiceover

        # S3 client for downloading voiceover files
        self.s3_client = S3Client("us-east-2")

        # Audio output directory for temporary files
        self.temp_audio_dir = Path(tempfile.gettempdir()) / "vidflux_audio"
        self.temp_audio_dir.mkdir(exist_ok=True)
        self.fade_in_duration = 1.0  # Longer fade-in for smoother transitions
        self.fade_out_duration = 1.5  # Longer fade-out for smoother transitions
        self.crossfade_duration = 0.5  # Crossfade between audio segments
        self.ducking_ratio = 0.4  # REDUCED: Less aggressive ducking (was 0.3)

        # Quality analysis thresholds (for display only)
        self.min_speech_clarity_score = 0.7
        self.min_dynamic_range = 10  # dB

        # Ensure ffmpeg is available for pydub and advanced processing
        try:
            if not which("ffmpeg"):
                logger.warning("ffmpeg not found. Some audio operations may fail.")
        except Exception:
            logger.warning("ffmpeg check failed. Some audio operations may fail.")

    def ensure_output_directory(self):
        """Ensure output directory exists"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"Created output directory: {self.output_dir}")

    def analyze_audio_levels_fallback(self, audio_path: str) -> Dict[str, float]:
        """
        Fallback audio analysis using pydub (no aggressive processing)
        """
        try:
            if not PYDUB_AVAILABLE:
                raise ImportError("pydub not available")

            audio = AudioSegment.from_file(audio_path)

            return {
                "rms_db": float(audio.dBFS),
                "peak_db": float(audio.max_dBFS),
                "dynamic_range": 20.0,  # estimated
                "lufs_estimate": float(audio.dBFS - 23),
                "clarity_score": 0.7,  # estimated
                "duration": len(audio) / 1000.0,
            }
        except Exception as e:
            logger.error(f"Error in fallback audio analysis: {str(e)}")
            return {
                "rms_db": -30.0,
                "peak_db": -6.0,
                "dynamic_range": 20.0,
                "lufs_estimate": -16.0,
                "clarity_score": 0.5,
                "duration": 0.0,
            }

    def analyze_audio_levels(self, audio_path: str) -> Dict[str, float]:
        """
        Analyze audio levels - with fallback if librosa not available
        """
        if not LIBROSA_AVAILABLE:
            return self.analyze_audio_levels_fallback(audio_path)

        try:
            # Load audio with librosa
            y, sr = librosa.load(audio_path, sr=None)

            # Calculate RMS energy
            rms = librosa.feature.rms(y=y)[0]
            rms_db = 20 * np.log10(np.mean(rms) + 1e-6)

            # Calculate peak level
            peak_db = 20 * np.log10(np.max(np.abs(y)) + 1e-6)

            # Calculate dynamic range
            dynamic_range = peak_db - rms_db

            # Estimate LUFS (simplified calculation)
            lufs_estimate = rms_db - 23  # Rough conversion

            # Speech clarity estimation (spectral centroid variance)
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            clarity_score = 1 / (1 + np.std(spectral_centroids) / np.mean(spectral_centroids))

            return {
                "rms_db": float(rms_db),
                "peak_db": float(peak_db),
                "dynamic_range": float(dynamic_range),
                "lufs_estimate": float(lufs_estimate),
                "clarity_score": float(clarity_score),
                "duration": len(y) / sr,
            }

        except Exception as e:
            logger.error(f"Error analyzing audio levels: {str(e)}")
            return self.analyze_audio_levels_fallback(audio_path)

    def apply_gentle_volume_adjustment(self, audio_clip, target_volume: float) -> AudioFileClip:
        """
        Apply gentle volume adjustment (NO aggressive normalization)

        Args:
            audio_clip: MoviePy AudioFileClip
            target_volume: Target volume multiplier (0.0 to 1.0)

        Returns:
            Volume-adjusted AudioFileClip
        """
        try:
            # Simple volume scaling - NO normalization, NO complex processing
            return audio_clip.with_effects([afx.MultiplyVolume(target_volume)])
        except Exception as e:
            logger.error(f"Error adjusting volume: {str(e)}")
            return audio_clip

    def apply_advanced_fade(
        self, audio_clip, fade_in: float = 1.0, fade_out: float = 1.5
    ) -> AudioFileClip:
        """
        Apply improved fade effects with smooth curves

        Args:
            audio_clip: MoviePy AudioFileClip
            fade_in: Fade-in duration in seconds
            fade_out: Fade-out duration in seconds

        Returns:
            AudioFileClip with fades applied
        """
        try:
            effects = []

            if fade_in > 0:
                effects.append(afx.AudioFadeIn(fade_in))

            if fade_out > 0:
                effects.append(afx.AudioFadeOut(fade_out))

            if effects:
                return audio_clip.with_effects(effects)
            else:
                return audio_clip

        except Exception as e:
            logger.error(f"Error applying fades: {str(e)}")
            return audio_clip

    def apply_intelligent_ducking(
        self, background_audio, speech_audio, ratio: float = 0.4
    ) -> AudioFileClip:
        """
        Apply GENTLE ducking that slightly reduces background audio during speech

        Args:
            background_audio: Background music/sound clip
            speech_audio: Speech/voiceover clip
            ratio: How much to reduce background (REDUCED to 0.4 for gentler effect)

        Returns:
            Ducked background audio clip
        """
        try:
            if speech_audio and speech_audio.duration > 0:
                # GENTLE ducking - reduce background volume by a smaller ratio
                ducked_volume = 1.0 - ratio
                ducked_background = background_audio.with_effects(
                    [afx.MultiplyVolume(ducked_volume)]
                )
                logger.info(
                    f"Applied gentle ducking: reduced background to {ducked_volume*100:.0f}% volume during speech"
                )
                return ducked_background
            else:
                return background_audio

        except Exception as e:
            logger.error(f"Error applying ducking: {str(e)}")
            return background_audio

    def perform_audio_quality_analysis(self, final_audio_path: str) -> Dict[str, Any]:
        """
        Perform audio quality analysis (for display only - no processing)
        """
        try:
            analysis = self.analyze_audio_levels(final_audio_path)

            # Quality checks (for display only)
            checks = {
                "voice_clarity": {
                    "score": analysis["clarity_score"],
                    "pass": analysis["clarity_score"] >= self.min_speech_clarity_score,
                    "message": (
                        "Good speech clarity"
                        if analysis["clarity_score"] >= self.min_speech_clarity_score
                        else "Speech clarity could be improved"
                    ),
                },
                "loudness_level": {
                    "value": analysis["lufs_estimate"],
                    "pass": -25 <= analysis["lufs_estimate"] <= -10,  # More lenient range
                    "message": (
                        "Good loudness level"
                        if -25 <= analysis["lufs_estimate"] <= -10
                        else "Loudness level outside optimal range"
                    ),
                },
                "dynamic_range": {
                    "value": analysis["dynamic_range"],
                    "pass": analysis["dynamic_range"] >= self.min_dynamic_range,
                    "message": (
                        "Good dynamic range"
                        if analysis["dynamic_range"] >= self.min_dynamic_range
                        else "Limited dynamic range detected"
                    ),
                },
                "peak_levels": {
                    "value": analysis["peak_db"],
                    "pass": analysis["peak_db"] <= -1.0,
                    "message": (
                        "No clipping detected"
                        if analysis["peak_db"] <= -1.0
                        else "Potential clipping detected"
                    ),
                },
            }

            # Overall quality score
            passed_checks = sum(1 for check in checks.values() if check["pass"])
            quality_score = passed_checks / len(checks)

            return {
                "overall_score": quality_score,
                "checks": checks,
                "audio_metrics": analysis,
                "recommendations": self._generate_quality_recommendations(checks),
            }

        except Exception as e:
            logger.error(f"Error in audio quality analysis: {str(e)}")
            return {
                "overall_score": 0.5,
                "checks": {},
                "audio_metrics": {},
                "recommendations": ["Unable to perform quality analysis"],
            }

    def _generate_quality_recommendations(self, checks: Dict[str, Any]) -> List[str]:
        """Generate gentle recommendations based on quality checks"""
        recommendations = []

        if not checks["voice_clarity"]["pass"]:
            recommendations.append(
                "Consider using an AI speech enhancer like DeepFilterNet for clarity"
            )

        if not checks["loudness_level"]["pass"]:
            if checks["loudness_level"]["value"] < -25:
                recommendations.append(
                    "Audio might be a bit quiet - consider gentle volume increase"
                )
            else:
                recommendations.append(
                    "Audio might be a bit loud - consider gentle volume decrease"
                )

        if not checks["dynamic_range"]["pass"]:
            recommendations.append("Consider adjusting volume levels for better dynamic range")

        if not checks["peak_levels"]["pass"]:
            recommendations.append("Consider gentle volume reduction to prevent clipping")

        if not recommendations:
            recommendations.append("Audio quality is good - no improvements needed")

        return recommendations

    def apply_ai_enhancement(self, audio_path: str, enhancement_type: str = "denoising") -> str:
        """
        Apply AI-based audio enhancement using external tools

        Args:
            audio_path: Path to audio file
            enhancement_type: Type of enhancement ("denoising", "speech_enhance")

        Returns:
            Path to enhanced audio file
        """
        try:
            enhanced_path = audio_path.replace(".wav", "_enhanced.wav").replace(
                ".mp3", "_enhanced.wav"
            )

            if enhancement_type == "denoising" and shutil.which("python"):
                # Try to use DeepFilterNet if available
                try:
                    result = subprocess.run(
                        ["python", "-m", "deepfilternet", audio_path, "--output", enhanced_path],
                        capture_output=True,
                        text=True,
                        timeout=30,
                    )

                    if result.returncode == 0 and os.path.exists(enhanced_path):
                        logger.info("✅ Applied DeepFilterNet enhancement")
                        return enhanced_path
                    else:
                        logger.warning("DeepFilterNet not available or failed")

                except (
                    subprocess.TimeoutExpired,
                    FileNotFoundError,
                    subprocess.SubprocessError,
                ) as e:
                    logger.warning(f"DeepFilterNet enhancement failed: {e}")

            elif enhancement_type == "speech_enhance":
                # Placeholder for other AI enhancement tools
                # Could integrate with:
                # - Adobe Speech Enhancer API
                # - Meta Demucs
                # - Other speech enhancement tools
                logger.info(
                    "Speech enhancement placeholder - integrate with Adobe Speech Enhancer API"
                )

            # If AI enhancement fails, try simple noise reduction with ffmpeg
            if shutil.which("ffmpeg"):
                try:
                    result = subprocess.run(
                        [
                            "ffmpeg",
                            "-i",
                            audio_path,
                            "-af",
                            "highpass=f=80,lowpass=f=8000,volume=1.1",
                            enhanced_path,
                            "-y",
                        ],
                        capture_output=True,
                        text=True,
                        timeout=30,
                    )

                    if result.returncode == 0 and os.path.exists(enhanced_path):
                        logger.info("✅ Applied basic ffmpeg audio enhancement")
                        return enhanced_path

                except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                    logger.warning(f"FFmpeg enhancement failed: {e}")

            logger.info("No AI enhancement applied - returning original audio")
            return audio_path  # Return original if enhancement fails

        except Exception as e:
            logger.error(f"Error in AI enhancement: {str(e)}")
            return audio_path

    def download_audio_if_needed(self, audio_url: str, scene_id: str) -> str:
        """Download audio file from S3 if needed, return local path"""
        try:
            if audio_url.startswith("http"):
                # It's a presigned URL, extract the S3 key
                if ".amazonaws.com/" in audio_url:
                    s3_key = audio_url.split(".amazonaws.com/")[-1].split("?")[0]
                else:
                    s3_key = audio_url
            else:
                # It's already an S3 key
                s3_key = audio_url

            # Create temporary file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file = self.temp_audio_dir / f"voiceover_{scene_id}_{timestamp}.mp3"

            # Download from S3
            self.s3_client.download_file("assets-vidflux", s3_key, str(temp_file))
            logger.info(f"Downloaded voiceover audio: {s3_key} -> {temp_file}")

            return str(temp_file)

        except Exception as e:
            logger.error(f"Error downloading audio {audio_url}: {str(e)}")
            raise

    def combine_voiceover_files(
        self, voiceover_data: List[Dict[str, Any]], total_video_duration: float
    ) -> str:
        """
        Combine multiple voiceover files into one time-synchronized audio track.
        Each voiceover will be placed at its scene's start time (equal scene durations).

        Args:
            voiceover_data: List of voiceover info with scene_number, audio_url, duration
            total_video_duration: Total duration of the video in seconds

        Returns:
            Path to combined voiceover file
        """
        downloaded_files = []

        try:
            # Sort by scene number
            voiceover_data_sorted = sorted(voiceover_data, key=lambda x: x.get("scene_number", 0))

            logger.info(
                f"🎤 Combining {len(voiceover_data_sorted)} voiceover files with equal scene timing..."
            )

            # Calculate equal scene duration
            num_scenes = len(voiceover_data_sorted)
            scene_duration = total_video_duration / num_scenes
            logger.info(f"Scene duration: {scene_duration:.2f}s each")

            # Create silent background audio track
            from moviepy import AudioClip

            silent_background = AudioClip(lambda t: 0, duration=total_video_duration)

            # List to hold positioned audio clips
            positioned_clips = [silent_background]

            for i, vo_info in enumerate(voiceover_data_sorted):
                scene_id = vo_info.get("scene_id")
                audio_url = vo_info.get("audio_url")
                scene_number = vo_info.get("scene_number", i + 1)

                if not audio_url:
                    logger.warning(f"No audio URL for scene {scene_number}, skipping")
                    continue

                try:
                    # Download voiceover file
                    local_path = self.download_audio_if_needed(audio_url, scene_id)
                    downloaded_files.append(local_path)

                    # Load audio clip
                    audio_clip = AudioFileClip(local_path)

                    # Calculate start time for this scene
                    start_time = (scene_number - 1) * scene_duration

                    # For the last scene, allow it to use all remaining video time
                    if scene_number == num_scenes:
                        max_duration = total_video_duration - start_time
                        logger.info(
                            f"Last scene {scene_number}: allowing {max_duration:.2f}s for voiceover"
                        )
                    else:
                        max_duration = scene_duration

                    # Ensure audio doesn't exceed available duration
                    if audio_clip.duration > max_duration:
                        logger.warning(
                            f"Scene {scene_number} voiceover ({audio_clip.duration:.2f}s) is longer than available duration ({max_duration:.2f}s), trimming"
                        )
                        audio_clip = audio_clip.subclipped(0, max_duration)

                    # Position the audio clip at the correct start time
                    positioned_clip = audio_clip.with_start(start_time)
                    positioned_clips.append(positioned_clip)

                    logger.info(
                        f"✅ Positioned scene {scene_number}: duration={audio_clip.duration:.2f}s, starts at {start_time:.2f}s"
                    )

                except Exception as e:
                    logger.error(f"Error processing voiceover for scene {scene_number}: {str(e)}")
                    continue

            if len(positioned_clips) <= 1:  # Only silent background
                raise ValueError("No valid voiceover clips found")

            # Combine all positioned clips
            from moviepy import CompositeAudioClip

            combined_voiceover = CompositeAudioClip(positioned_clips)

            # Set voiceover volume
            combined_voiceover = combined_voiceover.with_effects(
                [afx.MultiplyVolume(self.voiceover_volume)]
            )

            # Save combined voiceover
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            combined_file = (
                self.temp_audio_dir / f"combined_voiceover_{timestamp}_{uuid.uuid4().hex[:8]}.mp3"
            )
            combined_voiceover.write_audiofile(str(combined_file), logger=None)

            logger.info(
                f"✅ Combined voiceover saved: {combined_file} (duration: {combined_voiceover.duration:.2f}s)"
            )
            logger.info(f"✅ Each scene voiceover positioned at its correct start time")

            # Clean up clips
            for clip in positioned_clips:
                if hasattr(clip, "close"):
                    clip.close()

            return str(combined_file)

        except Exception as e:
            logger.error(f"Error combining voiceover files: {str(e)}")
            raise
        finally:
            # Clean up downloaded files
            for file_path in downloaded_files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.debug(f"Cleaned up downloaded file: {file_path}")
                except Exception as e:
                    logger.warning(f"Could not clean up file {file_path}: {str(e)}")

    async def get_voiceover_data_for_script(
        self, script_id: str, org_id: str, session
    ) -> List[Dict[str, Any]]:
        """
        Get voiceover data for all scenes in a script.

        Args:
            script_id: Script ID
            org_id: Organization ID
            session: Database session

        Returns:
            List of voiceover data dictionaries
        """
        try:
            from src.shared.models.database_models import Scene, AudioAsset
            from sqlalchemy import select, and_, desc

            # Get all scenes for the script
            scenes_stmt = (
                select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
            )

            result = await session.execute(scenes_stmt)
            scenes = result.scalars().all()

            voiceover_data = []

            for scene in scenes:
                # Get latest voiceover asset for this scene
                voiceover_stmt = (
                    select(AudioAsset)
                    .where(
                        and_(
                            AudioAsset.org_id == org_id,
                            AudioAsset.scene_id == scene.id,
                            AudioAsset.source_type == "voiceover",
                        )
                    )
                    .order_by(desc(AudioAsset.created_at))
                    .limit(1)
                )

                voiceover_result = await session.execute(voiceover_stmt)
                voiceover_asset = voiceover_result.scalar_one_or_none()

                if voiceover_asset and voiceover_asset.s3_url:
                    # Create presigned URL for voiceover
                    s3_key = voiceover_asset.s3_url
                    if s3_key.startswith("http") and ".amazonaws.com/" in s3_key:
                        s3_key = s3_key.split(".amazonaws.com/")[-1]

                    presigned_url = self.s3_client.get_presigned_url(
                        "assets-vidflux", s3_key, timedelta(hours=1)
                    )

                    voiceover_data.append(
                        {
                            "scene_id": str(scene.id),
                            "scene_number": scene.scene_number,
                            "audio_url": presigned_url,
                            "status": "completed",
                        }
                    )

                    logger.info(f"Found voiceover for scene {scene.scene_number}: {s3_key}")
                else:
                    logger.warning(f"No voiceover found for scene {scene.scene_number}")

            logger.info(f"Found {len(voiceover_data)} voiceover files for script {script_id}")
            return voiceover_data

        except Exception as e:
            logger.error(f"Error getting voiceover data for script {script_id}: {str(e)}")
            raise

    def create_final_audio_with_voiceover_and_music(
        self,
        stitched_video_path: str,
        combined_voiceover_path: str,
        background_music_path: Optional[str] = None,
        output_filename: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create final video with combined voiceover and background music, preserving existing background sounds.

        Args:
            stitched_video_path: Path to stitched video (with background sounds)
            combined_voiceover_path: Path to combined voiceover file
            background_music_path: Optional path to background music
            output_filename: Optional output filename

        Returns:
            Dictionary with mixing results
        """
        try:
            # Load the stitched video (already has background sounds)
            video_clip = VideoFileClip(stitched_video_path)
            video_duration = video_clip.duration

            logger.info(f"🎬 Creating final video with voiceover and music")
            logger.info(f"   Video duration: {video_duration:.2f}s")
            logger.info(f"   Video has existing audio: {video_clip.audio is not None}")

            # Load combined voiceover
            voiceover_clip = AudioFileClip(combined_voiceover_path)
            logger.info(f"   Voiceover duration: {voiceover_clip.duration:.2f}s")

            # Ensure voiceover matches video duration
            if voiceover_clip.duration > video_duration:
                voiceover_clip = voiceover_clip.subclipped(0, video_duration)
            elif voiceover_clip.duration < video_duration:
                silence_duration = video_duration - voiceover_clip.duration
                from moviepy import AudioClip

                silence = AudioClip(lambda t: 0, duration=silence_duration)
                voiceover_clip = concatenate_audioclips([voiceover_clip, silence])

            # Set voiceover volume
            voiceover_clip = voiceover_clip.with_effects(
                [afx.MultiplyVolume(self.voiceover_volume)]
            )

            # Prepare audio tracks list
            audio_tracks = [voiceover_clip]

            # Add existing video audio (background sounds) at lower volume
            if video_clip.audio:
                existing_audio = video_clip.audio.with_effects(
                    [afx.MultiplyVolume(self.existing_audio_volume)]
                )
                audio_tracks.append(existing_audio)
                logger.info(
                    f"   Added existing background sounds at {self.existing_audio_volume*100}% volume"
                )

            # Add background music if provided
            if background_music_path and os.path.exists(background_music_path):
                logger.info(f"   Adding background music: {background_music_path}")

                music_clip = AudioFileClip(background_music_path)

                # Adjust music duration to match video
                if music_clip.duration > video_duration:
                    music_clip = music_clip.subclipped(0, video_duration)
                elif music_clip.duration < video_duration:
                    # Loop music if shorter
                    loops_needed = int(np.ceil(video_duration / music_clip.duration))
                    music_clips = [music_clip] * loops_needed
                    music_clip = concatenate_audioclips(music_clips).subclipped(0, video_duration)

                # Set music volume
                music_clip = music_clip.with_effects(
                    [afx.MultiplyVolume(self.background_music_volume)]
                )
                audio_tracks.append(music_clip)

                logger.info(
                    f"   Background music duration: {music_clip.duration}s at {self.background_music_volume*100}% volume"
                )

            # Create composite audio with all tracks
            final_audio = CompositeAudioClip(audio_tracks)

            # Create final video with new audio
            final_video = video_clip.with_audio(final_audio)

            # Generate output filename if not provided
            if not output_filename:
                base_name = os.path.splitext(os.path.basename(stitched_video_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"{base_name}_with_voiceover_{timestamp}.mp4"

            output_path = os.path.join(self.output_dir, output_filename)

            # Write final video
            logger.info(f"🎥 Writing final video to: {output_path}")
            final_video.write_videofile(
                output_path,
                fps=video_clip.fps,
                codec="libx264",
                audio_codec="aac",
                temp_audiofile="temp-audio.m4a",
                remove_temp=True,
                logger=None,
            )

            # Clean up clips
            video_clip.close()
            voiceover_clip.close()
            final_audio.close()
            final_video.close()
            if len(audio_tracks) > 2:  # If we have music
                audio_tracks[2].close()

            logger.info(f"✅ Final video created successfully: {output_path}")

            return {
                "success": True,
                "output_path": output_path,
                "output_filename": output_filename,
                "duration": video_duration,
                "audio_tracks_count": len(audio_tracks),
                "has_voiceover": True,
                "has_background_music": background_music_path is not None,
                "has_background_sounds": video_clip.audio is not None,
            }

        except Exception as e:
            logger.error(f"Error creating final video with voiceover and music: {str(e)}")
            return {"success": False, "error": str(e)}

    def mix_final_video_audio_enhanced(
        self,
        stitched_video_path: str,
        background_music_paths: Optional[list] = None,
        voiceover_paths: Optional[list] = None,
        output_filename: Optional[str] = None,
        fade_duration: float = 1.0,
        enable_ducking: bool = True,
        enable_quality_analysis: bool = True,
        enable_ai_enhancement: bool = False,
    ) -> Dict[str, Any]:
        """
        Enhanced video audio mixing with overlay for all provided background music and voiceover files.
        """
        try:
            if not os.path.exists(stitched_video_path):
                return {
                    "success": False,
                    "error": f"Stitched video not found: {stitched_video_path}",
                }

            # Generate output filename if not provided
            if not output_filename:
                base_name = os.path.splitext(os.path.basename(stitched_video_path))[0]
                output_filename = f"{base_name}_enhanced_mixed.mp4"

            output_path = os.path.join(self.output_dir, output_filename)

            # Load the video with existing audio
            video_clip = VideoFileClip(stitched_video_path)
            video_duration = video_clip.duration

            logger.info(f"Processing video: {stitched_video_path}")
            logger.info(f"Video duration: {video_duration:.2f} seconds")

            # Prepare audio tracks list
            audio_tracks = []
            audio_info = {
                "existing_audio": False,
                "background_music": False,
                "voiceover": False,
                "ai_enhanced": False,
            }

            # Process existing audio from video (GENTLE processing)
            if video_clip.audio is not None:
                existing_audio = video_clip.audio

                # GENTLE volume adjustment (NO aggressive normalization)
                existing_audio = self.apply_gentle_volume_adjustment(
                    existing_audio, self.existing_audio_volume
                )
                existing_audio = self.apply_advanced_fade(
                    existing_audio, fade_duration, fade_duration
                )

                audio_tracks.append(existing_audio)
                audio_info["existing_audio"] = True
                logger.info("Processed existing video audio track (gentle processing)")

            # Create continuous background music
            if background_music_paths:
                bg_music_clips = []
                for bg_path in background_music_paths:
                    if bg_path and os.path.exists(bg_path):
                        bg_music_clip = AudioFileClip(bg_path)
                        bg_music_clips.append(bg_music_clip)

                if bg_music_clips:
                    # Concatenate all background music files first
                    if len(bg_music_clips) == 1:
                        combined_bg_music = bg_music_clips[0]
                    else:
                        combined_bg_music = concatenate_audioclips(bg_music_clips)

                    original_duration = combined_bg_music.duration
                    logger.info(
                        f"Processing background music: {original_duration:.2f}s → {video_duration:.2f}s needed"
                    )

                    # Remove silence and create seamless loops
                    activity_detector = AudioActivityDetector(
                        silence_threshold=0.005, min_silence_duration=0.2
                    )
                    audio_only_clip = activity_detector.extract_audio_content(combined_bg_music)
                    combined_bg_music = activity_detector.create_seamless_loop(
                        audio_only_clip, video_duration
                    )

                    combined_bg_music = self.apply_gentle_volume_adjustment(
                        combined_bg_music, self.background_music_volume
                    )
                    audio_tracks.append(combined_bg_music)
                    audio_info["background_music"] = True

            # Process voiceover files (concatenate, don't overlay)
            voiceover_clip = None
            if voiceover_paths:
                voiceover_clips = []
                for vo_path in voiceover_paths:
                    if vo_path and os.path.exists(vo_path):
                        processed_voiceover_path = vo_path
                        if enable_ai_enhancement:
                            processed_voiceover_path = self.apply_ai_enhancement(
                                vo_path, "speech_enhance"
                            )
                            if processed_voiceover_path != vo_path:
                                audio_info["ai_enhanced"] = True
                        vo_clip = AudioFileClip(processed_voiceover_path)
                        vo_clip = self.apply_gentle_volume_adjustment(
                            vo_clip, self.voiceover_volume
                        )
                        voiceover_clips.append(vo_clip)
                        logger.info(f"Processed voiceover: {vo_path}")

                if voiceover_clips:
                    if len(voiceover_clips) == 1:
                        combined_voiceover = voiceover_clips[0]
                    else:
                        combined_voiceover = concatenate_audioclips(voiceover_clips)

                    # Adjust voiceover duration to match video
                    if combined_voiceover.duration < video_duration:
                        from moviepy import AudioClip

                        silence_duration = video_duration - combined_voiceover.duration
                        silence = AudioClip(lambda t: 0, duration=silence_duration)
                        combined_voiceover = concatenate_audioclips([combined_voiceover, silence])
                    elif combined_voiceover.duration > video_duration:
                        combined_voiceover = combined_voiceover.subclipped(0, video_duration)

                    combined_voiceover = self.apply_advanced_fade(combined_voiceover, 0.1, 0.1)
                    audio_tracks.append(combined_voiceover)
                    voiceover_clip = combined_voiceover
                    audio_info["voiceover"] = True

            # Apply GENTLE ducking if enabled and we have both background and speech
            if enable_ducking and voiceover_clip and len(audio_tracks) > 1:
                # Find background audio tracks (not the voiceover)
                for i, track in enumerate(audio_tracks[:-1]):  # Exclude last track (voiceover)
                    audio_tracks[i] = self.apply_intelligent_ducking(
                        track, voiceover_clip, self.ducking_ratio
                    )
                logger.info("Applied gentle ducking to background audio")

            # Composite all audio tracks
            final_audio = None  # Initialize final_audio
            if audio_tracks:
                final_audio = CompositeAudioClip(audio_tracks)
                final_video = video_clip.with_audio(final_audio)
            else:
                final_video = video_clip
                logger.warning("No audio tracks to mix")

            # Export final video
            logger.info(f"Exporting enhanced video to: {output_path}")
            final_video.write_videofile(
                output_path,
                codec="libx264",
                audio_codec="aac",
                temp_audiofile="temp-audio.m4a",
                remove_temp=True,
                logger=None,
            )

            # Perform quality analysis if enabled (for display only)
            quality_analysis = None
            if enable_quality_analysis and final_audio is not None:
                # Extract audio for analysis
                temp_audio_path = output_path.replace(".mp4", "_temp_audio.wav")
                final_audio.write_audiofile(temp_audio_path, logger=None)
                quality_analysis = self.perform_audio_quality_analysis(temp_audio_path)
                os.unlink(temp_audio_path)  # Clean up temporary audio file

            # Clean up
            video_clip.close()
            for track in audio_tracks:
                track.close()
            final_video.close()

            file_size = os.path.getsize(output_path)
            file_size_mb = file_size / (1024 * 1024)

            return {
                "success": True,
                "input_video": stitched_video_path,
                "output_path": output_path,
                "filename": output_filename,
                "duration": video_duration,
                "file_size_mb": round(file_size_mb, 2),
                "audio_tracks_mixed": len(audio_tracks),
                "audio_info": audio_info,
                "enhancement_applied": {
                    "normalization": False,  # DISABLED
                    "ducking": enable_ducking and voiceover_clip is not None,
                    "advanced_fades": True,
                    "ai_enhancement": audio_info.get("ai_enhanced", False),
                },
                "quality_analysis": quality_analysis,
                "ducking_applied": enable_ducking and voiceover_clip is not None,
            }

        except Exception as e:
            logger.error(f"Error mixing video audio: {str(e)}")
            return {"success": False, "error": str(e)}

    def format_enhanced_results_for_display(self, results: Dict[str, Any]) -> str:
        """
        Format enhanced mixing results for display
        """
        if not results.get("success", False):
            return f"❌ Enhanced Audio Mixing Failed:\n{results.get('error', 'Unknown error')}"

        output = [
            "✅ Enhanced Audio Mixing Successful!",
            f"🎬 Output File: {results.get('filename', 'Unknown')}",
            f"⏱️ Duration: {results.get('duration', 0):.2f} seconds",
            f"📁 File Size: {results.get('file_size_mb', 0):.2f} MB",
            f"🎵 Audio Tracks Mixed: {results.get('audio_tracks_mixed', 0)}",
            "",
        ]

        # Audio components
        audio_info = results.get("audio_info", {})
        output.append("🎧 Audio Components:")
        if audio_info.get("existing_audio"):
            output.append("   ✅ Existing Video Audio")
        if audio_info.get("background_music"):
            output.append("   ✅ Background Music")
        if audio_info.get("voiceover"):
            output.append("   ✅ Voiceover")
        if audio_info.get("ai_enhanced"):
            output.append("   ✅ AI Enhanced Audio")
        output.append("")

        # Enhancements applied
        enhancements = results.get("enhancement_applied", {})
        output.append("🔧 Enhancements Applied:")
        if enhancements.get("ducking"):
            output.append("   ✅ Gentle Intelligent Ducking")
        if enhancements.get("advanced_fades"):
            output.append("   ✅ Advanced Fade Effects")
        if enhancements.get("ai_enhancement"):
            output.append("   ✅ AI Audio Enhancement")
        output.append("   ✅ Gentle Volume Processing (No Aggressive Normalization)")
        output.append("")

        # Quality analysis
        quality = results.get("quality_analysis")
        if quality:
            output.append("📊 Audio Quality Analysis:")
            output.append(f"   Overall Score: {quality['overall_score']*100:.0f}%")

            for check_name, check_data in quality.get("checks", {}).items():
                status = "✅" if check_data.get("pass") else "⚠️"
                output.append(
                    f"   {status} {check_name.replace('_', ' ').title()}: {check_data.get('message')}"
                )

            output.append("")
            output.append("💡 Recommendations:")
            for rec in quality.get("recommendations", []):
                output.append(f"   • {rec}")

        output.append(f"\n💾 Saved to: {results.get('output_path', 'Unknown')}")
        return "\n".join(output)

    def test_dependencies_enhanced(self) -> str:
        """Enhanced test if all required dependencies are available"""
        issues = []
        try:
            from moviepy import VideoFileClip
        except ImportError:
            issues.append("❌ moviepy not installed")

        try:
            from pydub import AudioSegment
        except ImportError:
            issues.append("❌ pydub not installed")

        if not LIBROSA_AVAILABLE:
            issues.append("⚠️ librosa not available (using fallback analysis)")

        try:
            if not which("ffmpeg"):
                issues.append("❌ ffmpeg not found in PATH")
        except Exception:
            issues.append("⚠️ ffmpeg check failed")

        # Check for AI enhancement tools
        ai_tools = []
        if shutil.which("python"):
            try:
                import subprocess

                result = subprocess.run(
                    ["python", "-c", "import deepfilternet"],
                    capture_output=True,
                    text=True,
                    timeout=5,
                )
                if result.returncode == 0:
                    ai_tools.append("✅ DeepFilterNet available")
                else:
                    ai_tools.append("⚠️ DeepFilterNet not installed (pip install deepfilternet)")
            except:
                ai_tools.append("⚠️ DeepFilterNet not available")

        if not issues:
            base_msg = "✅ All core dependencies available for enhanced audio mixing!"
            if ai_tools:
                return f"{base_msg}\n\n🤖 AI Enhancement Status:\n" + "\n".join(ai_tools)
            return base_msg
        elif len(issues) <= 2 and all("⚠️" in issue for issue in issues):
            base_msg = f"✅ Core dependencies available:\n" + "\n".join(issues)
            if ai_tools:
                return f"{base_msg}\n\n🤖 AI Enhancement Status:\n" + "\n".join(ai_tools)
            return base_msg
        else:
            return "❌ Dependency Issues Found:\n" + "\n".join(issues)


# Legacy class for backward compatibility
class VideoAudioMixer(EnhancedVideoAudioMixer):
    """Backward compatible wrapper for the enhanced mixer"""

    def mix_final_video_audio(self, *args, **kwargs):
        """Legacy method that calls the enhanced version with gentle settings"""
        return self.mix_final_video_audio_enhanced(*args, enable_normalization=False, **kwargs)
