"""
Video Stitching Service for VidFlux Backend
Combines multiple video clips with transitions and background audio.
"""

# Global imports
import os
import cv2
import uuid
import json
import numpy as np
from pathlib import Path
from loguru import logger
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional

# Local imports
from src.shared.utils.s3_client import S3Client

# MoviePy imports - updated for newer versions
try:
    from moviepy import (
        VideoFileClip, 
        AudioFileClip, 
        CompositeAudioClip,
        concatenate_videoclips,
        concatenate_audioclips,
        vfx,
    )
except ImportError as e:
    raise ImportError(f"MoviePy import failed: {e}. Please install with: pip install moviepy") from e

class VideoStitcher:
    """
    Video stitching class that combines multiple video clips with transitions,
    background music, and coherence validation.
    Now works with videos that already have background audio from the sound generator.
    MINIMAL VERSION - NO AUDIO CHANGES
    """
    
    def __init__(self, output_dir: str = "output/stitched_videos"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Default settings
        self.default_transition_duration = 0.5  # seconds
        self.default_fade_duration = 0.3  # seconds
        self.target_fps = 30
        self.target_resolution = (1920, 1080)  # HD resolution
        
        # Set S3 bucket and region from env, with fallback and error if missing
        self.s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
        s3_region = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
        self.s3_client = S3Client(s3_region)
        
    def validate_video_clips(self, background_sound_results: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Validate video clips for stitching compatibility.
        Works with video files that already have background audio included.
        
        Args:
            background_sound_results: List of background sound result dictionaries
            
        Returns:
            Tuple of (valid_clips, validation_errors)
        """
        valid_clips = []
        validation_errors = []
        
        for i, result in enumerate(background_sound_results):
            scene_num = result.get("scene_number", i + 1)
            
            if not result.get("success", False):
                validation_errors.append(f"Scene {scene_num}: Background sound generation failed")
                continue
            
            # Look for video with background audio already included
            video_with_audio_url = result.get("video_with_audio_url")
            
            if not video_with_audio_url:
                validation_errors.append(f"Scene {scene_num}: No video with background audio found")
                continue
                
            # Check if file exists (for local paths) or assume URLs are valid
            if video_with_audio_url.startswith(('http://', 'https://')):
                # For URLs, we'll assume they're valid for now
                valid_clips.append(result)
            else:
                if not os.path.exists(video_with_audio_url):
                    validation_errors.append(f"Scene {scene_num}: Video file not found at {video_with_audio_url}")
                    continue
                    
                # Check video properties
                try:
                    with VideoFileClip(video_with_audio_url) as clip:
                        if clip.duration < 0.1:
                            validation_errors.append(f"Scene {scene_num}: Video too short ({clip.duration:.2f}s)")
                            continue
                        if clip.fps is None or clip.fps < 1:
                            validation_errors.append(f"Scene {scene_num}: Invalid frame rate")
                            continue
                            
                    valid_clips.append(result)
                except Exception as e:
                    validation_errors.append(f"Scene {scene_num}: Error reading video - {str(e)}")
                    continue
                    
        return valid_clips, validation_errors
    
    def analyze_video_coherence(self, video_path: str) -> Dict[str, Any]:
        """
        Analyze video for coherence issues like flicker, blur, etc.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with coherence analysis results
        """
        try:
            # Read video with OpenCV
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                return {"error": "Could not open video file"}
            
            frames = []
            frame_count = 0
            brightness_values = []
            blur_values = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                frame_count += 1
                
                # Convert to grayscale for analysis
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Calculate brightness
                brightness = np.mean(gray)
                brightness_values.append(brightness)
                
                # Calculate blur (Laplacian variance)
                blur = cv2.Laplacian(gray, cv2.CV_64F).var()
                blur_values.append(blur)
                
                # Store frame for further analysis (sample every 10th frame)
                if frame_count % 10 == 0:
                    frames.append(gray)
            
            cap.release()
            
            if not brightness_values:
                return {"error": "No frames to analyze"}
            
            # Analyze flicker (brightness variation)
            brightness_std = np.std(brightness_values)
            brightness_mean = np.mean(brightness_values)
            flicker_score = brightness_std / brightness_mean if brightness_mean > 0 else 0
            
            # Analyze blur
            blur_mean = np.mean(blur_values)
            blur_std = np.std(blur_values)
            
            # Determine quality scores (0-100)
            flicker_quality = max(0, 100 - (flicker_score * 1000))  # Lower flicker = higher quality
            blur_quality = min(100, blur_mean / 100)  # Higher blur variance = higher quality
            
            # Overall coherence score
            coherence_score = (flicker_quality + blur_quality) / 2
            
            return {
                "frame_count": frame_count,
                "brightness_mean": brightness_mean,
                "brightness_std": brightness_std,
                "flicker_score": flicker_score,
                "flicker_quality": flicker_quality,
                "blur_mean": blur_mean,
                "blur_std": blur_std,
                "blur_quality": blur_quality,
                "coherence_score": coherence_score,
                "quality_rating": self._get_quality_rating(coherence_score)
            }
            
        except Exception as e:
            return {"error": f"Error analyzing video coherence: {str(e)}"}
    
    def _get_quality_rating(self, score: float) -> str:
        """Convert coherence score to quality rating"""
        if score >= 80:
            return "Excellent"
        elif score >= 60:
            return "Good"
        elif score >= 40:
            return "Fair"
        else:
            return "Poor"
    
    def create_transition_effect(self, clip1: VideoFileClip, clip2: VideoFileClip, 
                               transition_type: str = "crossfade", 
                               duration: float = 0.5) -> VideoFileClip:
        """
        Create transition effect between two clips.
        Updated with correct MoviePy method names.
        
        Args:
            clip1: First video clip
            clip2: Second video clip
            transition_type: Type of transition ("crossfade", "slide", "fade")
            duration: Duration of transition in seconds
            
        Returns:
            Combined clip with transition
        """
        try:
            if transition_type == "crossfade":
                # Crossfade transition - updated with correct MoviePy syntax
                clip1_fade = clip1.with_effects([vfx.CrossFadeOut(duration)])
                clip2_fade = clip2.with_effects([vfx.CrossFadeIn(duration)])
                return concatenate_videoclips([clip1_fade, clip2_fade])
                
            elif transition_type == "fade":
                # Fade to black transition
                clip1_fade = clip1.with_effects([vfx.FadeOut(duration)])
                clip2_fade = clip2.with_effects([vfx.FadeIn(duration)])
                return concatenate_videoclips([clip1_fade, clip2_fade])
                
            elif transition_type == "slide":
                # Simple concatenation (no special effect for now)
                return concatenate_videoclips([clip1, clip2])
                
            else:
                # Default: simple concatenation
                return concatenate_videoclips([clip1, clip2])
                
        except Exception as e:
            logger.error(f"Error creating transition: {str(e)}")
            # Fallback to simple concatenation
            return concatenate_videoclips([clip1, clip2])
    
    def normalize_video_properties(self, clip: VideoFileClip) -> VideoFileClip:
        """
        Normalize video properties (resolution, fps) for consistent stitching.
        
        Args:
            clip: Input video clip
            
        Returns:
            Normalized video clip
        """
        try:
            # Resize to target resolution
            if clip.size != self.target_resolution:
                clip = clip.resized(self.target_resolution)
            
            # Set target fps if different
            if abs(clip.fps - self.target_fps) > 0.1:  # Use abs for float comparison
                clip = clip.with_fps(self.target_fps)
                
            return clip
            
        except Exception as e:
            logger.error(f"Error normalizing video: {str(e)}")
            return clip
    
    def download_video_if_needed(self, video_path_or_url: str, scene_num: int) -> str:
        """
        Download video from URL if needed, return local path.
        
        Args:
            video_path_or_url: Local path or URL to video
            scene_num: Scene number for temporary filename
            
        Returns:
            Local path to video file
        """
        if video_path_or_url.startswith(('http://', 'https://')):
            import requests
            import time
            
            temp_filename = f"temp_video_scene_{scene_num}_{int(time.time())}.mp4"
            temp_path = self.output_dir / temp_filename
            
            try:
                response = requests.get(video_path_or_url, timeout=120)
                response.raise_for_status()
                
                with open(temp_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"Downloaded video for scene {scene_num} to {temp_path}")
                return str(temp_path)
                
            except Exception as e:
                logger.error(f"Failed to download video for scene {scene_num}: {str(e)}")
                raise e
        else:
            return video_path_or_url
    
    def download_audio_if_needed(self, audio_path_or_url: str, scene_num: int) -> str:
        """
        Download audio from URL if needed, return local path.
        
        Args:
            audio_path_or_url: Local path or URL to audio
            scene_num: Scene number for temporary filename
            
        Returns:
            Local path to audio file
        """
        if audio_path_or_url.startswith(('http://', 'https://')):
            import requests
            import time
            
            temp_filename = f"temp_audio_scene_{scene_num}_{int(time.time())}.mp3"
            temp_path = self.output_dir / temp_filename
            
            try:
                response = requests.get(audio_path_or_url, timeout=60)
                response.raise_for_status()
                
                with open(temp_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"Downloaded audio for scene {scene_num} to {temp_path}")
                return str(temp_path)
                
            except Exception as e:
                logger.error(f"Failed to download audio for scene {scene_num}: {str(e)}")
                raise e
        else:
            return audio_path_or_url
    
    def stitch_videos(self, 
                     background_sound_results: List[Dict[str, Any]], 
                     script_id: str,
                     org_id: str = None,
                     additional_background_music: Optional[str] = None,
                     transition_type: str = "crossfade",
                     transition_duration: float = 0.5,
                     include_coherence_check: bool = True,
                     music_volume: float = 0.2,
                     session=None) -> Dict[str, Any]:
        """
        Stitch multiple videos with background audio together with transitions.
        Updated to work with background sound generator results.
        MINIMAL VERSION - NO AUDIO MANIPULATION
        
        Args:
            background_sound_results: List of background sound generation results
            script_id: ID of the script for naming the output file
            org_id: Organization ID for DB record
            session: SQLAlchemy session for DB record
            additional_background_music: Optional additional background music file
            transition_type: Type of transition between clips
            transition_duration: Duration of transitions in seconds
            include_coherence_check: Whether to run coherence analysis
            music_volume: Volume level for additional background music (0.0-1.0)
            
        Returns:
            Dictionary with stitching results
        """
        downloaded_files = []  # Track downloaded files for cleanup
        
        # --- EXTRA LOGGING FOR DEBUGGING ---
        logger.info(f"[VideoStitcher] Received background_sound_results: {json.dumps(background_sound_results, indent=2, default=str)}")
        
        try:
            # Validate input videos
            valid_clips, validation_errors = self.validate_video_clips(background_sound_results)
            logger.info(f"[VideoStitcher] valid_clips: {json.dumps(valid_clips, indent=2, default=str)}")
            logger.info(f"[VideoStitcher] validation_errors: {validation_errors}")
            
            if not valid_clips:
                logger.error(f"[VideoStitcher] No valid video clips found. Validation errors: {validation_errors}")
                return {
                    "success": False,
                    "error": "No valid video clips with audio found",
                    "validation_errors": validation_errors
                }
            
            if len(valid_clips) < 2:
                logger.error(f"[VideoStitcher] Less than 2 valid clips. Validation errors: {validation_errors}")
                return {
                    "success": False,
                    "error": "Need at least 2 valid clips for stitching",
                    "validation_errors": validation_errors
                }
            
            logger.info(f"Stitching {len(valid_clips)} video clips with background audio...")
            
            # Load video clips that already have background audio
            clips = []
            coherence_results = []
            
            for i, result in enumerate(valid_clips):
                scene_num = result.get("scene_number", i + 1)
                
                # Get video file that already includes background audio
                video_with_audio_url = result.get("video_with_audio_url")
                
                logger.info(f"[VideoStitcher] Scene {scene_num}: video_with_audio_url: {video_with_audio_url}")
                
                try:
                    # Download video if needed
                    local_video_path = self.download_video_if_needed(video_with_audio_url, scene_num)
                    logger.info(f"[VideoStitcher] Scene {scene_num}: local_video_path: {local_video_path}")
                    if local_video_path != video_with_audio_url:
                        downloaded_files.append(local_video_path)
                    
                    # Load video clip (already contains background audio)
                    video_clip = VideoFileClip(local_video_path)
                    logger.info(f"[VideoStitcher] Scene {scene_num}: Loaded video: duration={video_clip.duration}, size={video_clip.size}, fps={video_clip.fps}, audio={'Yes' if video_clip.audio else 'No'}")
                    
                    # Normalize video properties
                    video_clip = self.normalize_video_properties(video_clip)
                    
                    # Run coherence check if requested
                    if include_coherence_check:
                        coherence = self.analyze_video_coherence(local_video_path)
                        coherence["scene_number"] = scene_num
                        coherence_results.append(coherence)
                    
                    clips.append(video_clip)
                    
                except Exception as e:
                    logger.error(f"[VideoStitcher] Error processing scene {scene_num}: {str(e)}")
                    continue
            
            if not clips:
                return {
                    "success": False,
                    "error": "No clips could be loaded",
                    "validation_errors": validation_errors
                }
            
            # Create transitions between clips
            if len(clips) == 1:
                final_video = clips[0]
            else:
                # Start with first clip
                final_video = clips[0]
                
                # Add remaining clips with transitions
                for i in range(1, len(clips)):
                    logger.info(f"Adding transition between clip {i} and {i+1}")
                    final_video = self.create_transition_effect(
                        final_video, clips[i], 
                        transition_type, transition_duration
                    )
            
            # MINIMAL AUDIO HANDLING - only if additional music is provided
            if additional_background_music and os.path.exists(additional_background_music):
                try:
                    music_clip = AudioFileClip(additional_background_music)
                    
                    # Adjust music duration to match video
                    if music_clip.duration > final_video.duration:
                        music_clip = music_clip.subclipped(0, final_video.duration)
                    elif music_clip.duration < final_video.duration:
                        # Loop music if it's shorter than video
                        loops_needed = int(np.ceil(final_video.duration / music_clip.duration))
                        music_clips = [music_clip] * loops_needed
                        music_clip = concatenate_audioclips(music_clips).subclipped(0, final_video.duration)
                    
                    # Set music volume using with_volume_scaled
                    music_clip = music_clip.with_volume_scaled(music_volume)
                    
                    # Combine with existing audio (videos already have background sounds)
                    if final_video.audio:
                        final_audio = CompositeAudioClip([final_video.audio, music_clip])
                    else:
                        final_audio = music_clip
                    
                    final_video = final_video.with_audio(final_audio)
                    logger.info(f"Added additional background music at {music_volume*100}% volume")
                    
                except Exception as e:
                    logger.error(f"Error adding additional background music: {str(e)}")
            
            # Generate output filename with script_id
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            output_filename = f"stitched_video_script_{script_id}_{timestamp}_{unique_id}.mp4"
            output_path = self.output_dir / output_filename
            
            # Write final video
            logger.info(f"Writing final video to {output_path}")
            final_video.write_videofile(
                str(output_path),
                fps=self.target_fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                logger=None
            )
            
            # Upload to S3
            s3_key = f"Vidflux-Assets/video-stitching-assets/script_{script_id}/video_{uuid.uuid4()}.mp4"
            self.s3_client.upload_file(
                bucket=self.s3_bucket,
                key=s3_key,
                file_path=str(output_path)
            )
            logger.info(f"✅ Uploaded stitched video to S3: {s3_key}")
            # Get final video info BEFORE deleting the file
            final_duration = 0
            try:
                with VideoFileClip(str(output_path)) as final_clip:
                    final_duration = final_clip.duration
            except Exception as e:
                logger.warning(f"Could not get final video duration: {e}")
            # Clean up local file
            if os.path.exists(output_path):
                os.remove(output_path)
            
            # Clean up downloaded files
            for temp_file in downloaded_files:
                try:
                    os.remove(temp_file)
                    logger.info(f"Cleaned up temporary file: {temp_file}")
                except Exception as e:
                    logger.warning(f"Could not clean up {temp_file}: {e}")
            
            # Get final video info
            final_coherence = None
            if include_coherence_check:
                final_coherence = self.analyze_video_coherence(str(output_path))
                final_coherence["scene_number"] = "Final Stitched Video"
            
            # Defensive: if s3_url is a full URL, extract the key part before returning
            s3_key_result = s3_key
            if s3_key_result.startswith("http") and ".amazonaws.com/" in s3_key_result:
                s3_key_result = s3_key_result.split(".amazonaws.com/")[-1]
            # Return S3 key (not URL)
            return {
                "success": True,
                "s3_url": s3_key_result,
                "final_duration": final_duration,
                "final_coherence": final_coherence,
                "validation_errors": validation_errors,
                "coherence_results": coherence_results
            }
            
        except Exception as e:
            # Clean up downloaded files even on error
            for temp_file in downloaded_files:
                try:
                    os.remove(temp_file)
                except:
                    pass
                    
            logger.error(f"Error stitching videos: {str(e)}")
            return {
                "success": False,
                "error": f"Error stitching videos: {str(e)}",
                "validation_errors": validation_errors if 'validation_errors' in locals() else []
            }
    
    def format_stitching_results_for_display(self, result: Dict[str, Any]) -> str:
        """
        Format stitching results for display in the UI.
        Updated for videos with background audio.
        
        Args:
            result: Stitching result dictionary
            
        Returns:
            Formatted string for display
        """
        if not result.get("success", False):
            error_msg = f"❌ Video Stitching Failed\n\nError: {result.get('error', 'Unknown error')}"
            
            if result.get("validation_errors"):
                error_msg += "\n\nValidation Errors:\n"
                for error in result["validation_errors"]:
                    error_msg += f"• {error}\n"
            
            return error_msg
        
        # Success message
        output_msg = f"✅ Video Stitching with Background Audio Completed!\n\n"
        output_msg += f"📁 Output File: {result['output_filename']}\n"
        output_msg += f"📍 Full Path: {result['output_path']}\n"
        output_msg += f"⏱️ Duration: {result['duration']:.2f} seconds\n"
        output_msg += f"🎬 Clips Used: {result['clips_used']}/{result['total_clips']}\n"
        output_msg += f"🔄 Transition: {result['transition_type']} ({result['transition_duration']}s)\n"
        output_msg += f"🎵 Background Audio: {'✅ Generated sounds included' if result.get('has_background_audio') else '❌ No audio'}\n"
        output_msg += f"🎼 Additional Music: {'✅ Added' if result.get('additional_music_added') else '❌ Not Added'}"
        
        if result.get('additional_music_added'):
            output_msg += f" (Volume: {result.get('music_volume', 0.2)*100:.0f}%)"
        output_msg += "\n\n"
        
        # Validation errors (if any)
        if result.get("validation_errors"):
            output_msg += "⚠️ Validation Issues:\n"
            for error in result["validation_errors"]:
                output_msg += f"• {error}\n"
            output_msg += "\n"
        
        # Coherence results for individual clips
        if result.get("coherence_results"):
            output_msg += "📊 Individual Clip Coherence Analysis:\n"
            for coherence in result["coherence_results"]:
                if "error" not in coherence:
                    output_msg += f"Scene {coherence['scene_number']}: {coherence['quality_rating']} "
                    output_msg += f"(Score: {coherence['coherence_score']:.1f}/100)\n"
                else:
                    output_msg += f"Scene {coherence['scene_number']}: Analysis failed - {coherence['error']}\n"
            output_msg += "\n"
        
        # Final coherence result
        if result.get("final_coherence") and "error" not in result["final_coherence"]:
            final_coherence = result["final_coherence"]
            output_msg += f"🎯 Final Stitched Video Coherence:\n"
            output_msg += f"Overall Quality: {final_coherence['quality_rating']} (Score: {final_coherence['coherence_score']:.1f}/100)\n"
            output_msg += f"Flicker Quality: {final_coherence['flicker_quality']:.1f}/100\n"
            output_msg += f"Blur Quality: {final_coherence['blur_quality']:.1f}/100\n"
            output_msg += f"Frame Count: {final_coherence['frame_count']}\n"
        
        return output_msg 
