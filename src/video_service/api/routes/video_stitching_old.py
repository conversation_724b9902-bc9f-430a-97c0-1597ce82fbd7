# Global imports
import os
import glob
import uuid
import requests
from loguru import logger
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy import and_, select, desc, update
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from moviepy import VideoFileClip

# Local imports
from src.shared.core.dependencies import require_auth, get_database_session
from src.shared.models.database_models import VideoAsset, Script, Scene, AudioAsset, TaskQueue
from src.video_service.services.video_stitcher import VideoStitcher
from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer
from src.shared.utils.s3_client import S3Client
from src.shared.schemas.script import TaskQueueResponse

router = APIRouter(prefix="/stitching", tags=["Video Stitching"])

# Initialize video stitcher
video_stitcher = VideoStitcher()

# Initialize audio mixer for automatic audio stitching
audio_mixer = EnhancedVideoAudioMixer(output_dir="output/final_videos")

# Initialize S3 client
S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)


@router.get("/status")
async def get_stitching_status():
    """Get video stitching service status"""
    return {"service": "Video Stitching", "status": "running", "version": "1.0.0"}


@router.get("/status/{script_id}")
async def get_script_stitching_status(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """Get stitching status for a specific script"""
    try:
        org_id, user_id, email = user_info

        # Check if stitched video exists for this script
        stitched_video_query = (
            select(VideoAsset)
            .where(
                and_(
                    VideoAsset.script_id == script_id,
                    VideoAsset.generation_method
                    == "rendered",  # Stitched videos are marked as 'rendered'
                    VideoAsset.org_id == org_id,
                    VideoAsset.deleted_at.is_(None),
                )
            )
            .order_by(desc(VideoAsset.created_at))
        )

        result = await session.execute(stitched_video_query)
        stitched_video = result.scalar_one_or_none()

        if stitched_video:
            return {
                "script_id": script_id,
                "status": "completed",
                "stitched_video_url": stitched_video.s3_url,
                "asset_id": stitched_video.asset_id,
                "created_at": stitched_video.created_at.isoformat(),
            }
        else:
            return {
                "script_id": script_id,
                "status": "not_found",
                "message": "No stitched video found for this script",
            }

    except Exception as e:
        logger.error(f"Error checking stitching status for script {script_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check stitching status: {str(e)}")


@router.post("/stitch-async", response_model=TaskQueueResponse, status_code=202)
async def stitch_videos_async(
    script_id: str,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Start async video stitching with audio mixing process
    Returns immediately with task ID for status polling

    Args:
        script_id: The script ID to stitch videos for
        enable_ducking: Whether to enable audio ducking during audio stitching
        enable_ai_enhancement: Whether to enable AI audio enhancement

    Returns:
        TaskQueueResponse with task details for status polling
    """
    try:
        org_id, user_id, email = user_info

        # Validate script exists and user has access
        script_query = select(Script).where(and_(Script.id == script_id, Script.org_id == org_id))
        script_result = await session.execute(script_query)
        script = script_result.scalar_one_or_none()

        if not script:
            raise HTTPException(status_code=404, detail="Script not found or access denied")

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Create task queue entry
        task_queue = TaskQueue(
            task_id=task_id,
            task_type="video_stitching_with_audio",
            org_id=org_id,
            user_id=user_id,
            related_script_id=script_id,
            input_data={
                "script_id": script_id,
                "enable_ducking": enable_ducking,
                "enable_ai_enhancement": enable_ai_enhancement,
            },
            status="pending",
            progress=0,
            queue_name="video_stitching",
        )

        session.add(task_queue)
        await session.commit()

        # Start the async task
        from src.video_service.tasks import video_stitching_with_audio_task

        video_stitching_with_audio_task.delay(
            task_id=task_id,
            script_id=script_id,
            org_id=org_id,
            user_id=str(user_id),
            enable_ducking=enable_ducking,
            enable_ai_enhancement=enable_ai_enhancement,
        )

        logger.info(
            f"Started async video stitching with audio task: {task_id} for script: {script_id}"
        )

        return TaskQueueResponse(
            id=task_queue.id,
            task_id=task_id,
            task_type="video_stitching_with_audio",
            org_id=org_id,
            user_id=user_id,
            status="pending",
            progress=0,
            result=None,
            error_message=None,
            created_at=task_queue.created_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting async video stitching: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start video stitching: {str(e)}")


@router.get("/status/{task_id}")
async def get_task_status(
    task_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Get the status of a video stitching with audio task

    Args:
        task_id: The task ID returned from stitch-async endpoint

    Returns:
        Task status with progress and results
    """
    try:
        org_id, user_id, email = user_info

        # Get task from database
        stmt = select(TaskQueue).where(
            and_(
                TaskQueue.task_id == task_id,
                TaskQueue.org_id == org_id,
                TaskQueue.user_id == user_id,
            )
        )
        result = await session.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="Task not found or access denied")

        logger.info(f"Retrieved task status for {task_id}: {task.status} ({task.progress}%)")

        return {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "status": task.status,
            "progress": task.progress,
            "result": task.result,
            "error_message": task.error_message,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {str(e)}")


@router.get("/status/script/{script_id}")
async def get_script_stitching_status(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Get the latest stitching task status for a specific script

    Args:
        script_id: The script ID to check status for

    Returns:
        Latest task status for the script or completed video info
    """
    try:
        org_id, user_id, email = user_info

        # First check if there's an active/recent task for this script
        task_stmt = (
            select(TaskQueue)
            .where(
                and_(
                    TaskQueue.related_script_id == script_id,
                    TaskQueue.task_type == "video_stitching_with_audio",
                    TaskQueue.org_id == org_id,
                    TaskQueue.user_id == user_id,
                )
            )
            .order_by(TaskQueue.created_at.desc())
            .limit(1)
        )

        task_result = await session.execute(task_stmt)
        latest_task = task_result.scalar_one_or_none()

        if latest_task:
            return {
                "script_id": script_id,
                "has_active_task": True,
                "task_id": latest_task.task_id,
                "task_status": latest_task.status,
                "progress": latest_task.progress,
                "result": latest_task.result,
                "error_message": latest_task.error_message,
                "created_at": (
                    latest_task.created_at.isoformat() if latest_task.created_at else None
                ),
                "started_at": (
                    latest_task.started_at.isoformat() if latest_task.started_at else None
                ),
                "completed_at": (
                    latest_task.completed_at.isoformat() if latest_task.completed_at else None
                ),
            }

        # If no task, check if there's a completed stitched video
        stitched_video_query = (
            select(VideoAsset)
            .where(
                and_(
                    VideoAsset.script_id == script_id,
                    VideoAsset.generation_method == "rendered",
                    VideoAsset.org_id == org_id,
                    VideoAsset.deleted_at.is_(None),
                )
            )
            .order_by(desc(VideoAsset.created_at))
        )

        result = await session.execute(stitched_video_query)
        stitched_video = result.scalar_one_or_none()

        if stitched_video:
            return {
                "script_id": script_id,
                "has_active_task": False,
                "status": "completed",
                "stitched_video_url": stitched_video.s3_url,
                "asset_id": stitched_video.asset_id,
                "created_at": stitched_video.created_at.isoformat(),
            }
        else:
            return {
                "script_id": script_id,
                "has_active_task": False,
                "status": "not_found",
                "message": "No stitching task or completed video found for this script",
            }

    except Exception as e:
        logger.error(f"Error checking stitching status for script {script_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check stitching status: {str(e)}")


@router.post("/stitch-video-only")
async def stitch_videos_only(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Stitch multiple scene videos into one final video WITHOUT automatic audio mixing
    This endpoint maintains the old behavior for backwards compatibility

    Args:
        script_id: The script ID to stitch videos for

    Returns:
        Dict with stitching status and result
    """
    # Call the main stitch function with auto_audio_stitch=False
    return await stitch_videos(
        script_id=script_id, auto_audio_stitch=False, user_info=user_info, session=session
    )


@router.post("/stitch")
async def stitch_videos(
    script_id: str,
    auto_audio_stitch: bool = True,  # New parameter to control automatic audio stitching
    enable_ducking: bool = True,  # Audio stitching parameters
    enable_ai_enhancement: bool = False,  # Audio stitching parameters
    use_async: bool = True,  # New parameter to control async processing
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Stitch multiple scene videos into one final video with optional automatic audio mixing

    Args:
        script_id: The script ID to stitch videos for
        auto_audio_stitch: Whether to automatically perform audio stitching after video stitching (default: True)
        enable_ducking: Whether to enable audio ducking during audio stitching (default: True)
        enable_ai_enhancement: Whether to enable AI audio enhancement during audio stitching (default: False)
        use_async: Whether to use async processing (default: True for better UX)

    Returns:
        Dict with stitching status, video results, and optional audio stitching results
        If use_async=True, returns task info for polling
        If use_async=False, returns complete results (may take long time)
    """

    # If auto_audio_stitch is True and use_async is True, redirect to async endpoint
    if auto_audio_stitch and use_async:
        logger.info(f"Redirecting to async processing for script: {script_id}")
        return await stitch_videos_async(
            script_id=script_id,
            enable_ducking=enable_ducking,
            enable_ai_enhancement=enable_ai_enhancement,
            user_info=user_info,
            session=session,
        )

    # Original synchronous processing (for backwards compatibility)
    return await _stitch_videos_sync(
        script_id=script_id,
        auto_audio_stitch=auto_audio_stitch,
        enable_ducking=enable_ducking,
        enable_ai_enhancement=enable_ai_enhancement,
        user_info=user_info,
        session=session,
    )


async def _stitch_videos_sync(
    script_id: str,
    auto_audio_stitch: bool = True,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    user_info: tuple = None,
    session: AsyncSession = None,
):
    """
    Synchronous implementation of video stitching with optional audio mixing
    This is the original implementation moved to a separate function
    """
    try:
        org_id, user_id, email = user_info

        # Validate script exists and user has access
        script_query = select(Script).where(and_(Script.id == script_id, Script.org_id == org_id))
        script_result = await session.execute(script_query)
        script = script_result.scalar_one_or_none()

        if not script:
            raise HTTPException(status_code=404, detail="Script not found or access denied")

        logger.info(f"Video stitching requested for script: {script_id}")

        # Get all scenes for this script
        scenes_query = (
            select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
        )

        scenes_result = await session.execute(scenes_query)
        scenes = scenes_result.scalars().all()

        if not scenes:
            raise HTTPException(status_code=404, detail="No scenes found for this script")

        # Get video assets for each scene (preferring videos with audio)
        background_sound_results = []

        for scene in scenes:
            # Try to find video with background audio first
            video_query = (
                select(VideoAsset)
                .where(and_(VideoAsset.scene_id == scene.id, VideoAsset.deleted_at.is_(None)))
                .order_by(desc(VideoAsset.created_at))
            )

            video_result = await session.execute(video_query)
            video_asset = video_result.scalar_one_or_none()

            if video_asset:
                # Convert S3 key to presigned URL for VideoStitcher
                video_url = video_asset.s3_url
                logger.info(
                    f"[DEBUG] Original video URL for scene {scene.scene_number}: {video_url}"
                )

                if not video_url.startswith(("http://", "https://")):
                    # S3 key - convert to presigned URL
                    try:
                        video_url = s3_client.get_presigned_url(
                            bucket=S3_BUCKET,
                            key=video_asset.s3_url,
                            ttl=timedelta(hours=2),  # 2 hours should be enough for processing
                        )
                        logger.info(
                            f"Generated presigned URL for scene {scene.scene_number}: {video_asset.s3_url} -> {video_url[:100]}..."
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to generate presigned URL for {video_asset.s3_url}: {e}"
                        )
                        video_url = video_asset.s3_url  # Fallback to original
                else:
                    logger.info(
                        f"[DEBUG] Video URL already a full URL for scene {scene.scene_number}"
                    )

                # Create background sound result format expected by VideoStitcher
                scene_data = {
                    "scene_id": str(scene.id),
                    "scene_number": scene.scene_number,
                    "success": True,
                    "video_with_audio_url": video_url,
                    "local_path": video_asset.local_path,
                    "asset_id": video_asset.asset_id,
                }
                logger.info(
                    f"[DEBUG] Final scene_data for scene {scene.scene_number}: video_with_audio_url = {scene_data['video_with_audio_url'][:100]}..."
                )
                background_sound_results.append(scene_data)
            else:
                logger.warning(f"No video asset found for scene {scene.scene_number}")
                background_sound_results.append(
                    {
                        "scene_id": str(scene.id),
                        "scene_number": scene.scene_number,
                        "success": False,
                        "error": "No video asset found",
                    }
                )

        if not any(result["success"] for result in background_sound_results):
            raise HTTPException(
                status_code=404, detail="No video assets found for any scenes in this script"
            )

        # Get background music for the script if available
        music_query = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.script_id == script_id,
                    AudioAsset.source_type.in_(["background_music", "user_uploaded"]),
                    AudioAsset.deleted_at.is_(None),
                )
            )
            .order_by(desc(AudioAsset.created_at))
        )

        music_result = await session.execute(music_query)
        background_music = music_result.scalar_one_or_none()

        additional_background_music = None
        if background_music:
            music_url = background_music.s3_url
            if not music_url.startswith(("http://", "https://")):
                # S3 key - convert to presigned URL
                try:
                    additional_background_music = s3_client.get_presigned_url(
                        bucket=S3_BUCKET, key=background_music.s3_url, ttl=timedelta(hours=2)
                    )
                    logger.info(
                        f"Generated presigned URL for background music: {background_music.s3_url}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to generate presigned URL for background music {background_music.s3_url}: {e}"
                    )
                    additional_background_music = music_url  # Fallback to original
            else:
                additional_background_music = music_url

        # Call the video stitcher service
        stitch_result = video_stitcher.stitch_videos(
            background_sound_results=background_sound_results,
            script_id=script_id,
            org_id=org_id,
            additional_background_music=additional_background_music,
            session=session,
        )

        if stitch_result.get("success"):
            # Save the stitched video as a VideoAsset in the database
            try:
                asset_id = str(uuid.uuid4())
                s3_url = stitch_result.get("s3_url")

                video_asset = VideoAsset(
                    asset_id=asset_id,
                    org_id=org_id,
                    s3_url=s3_url,
                    generation_method="rendered",  # Stitched videos are marked as 'rendered'
                    script_id=script_id,
                    local_path=None,  # No local path since it's uploaded to S3
                )
                session.add(video_asset)
                await session.commit()
                logger.info(f"Successfully saved stitched video asset to database: {asset_id}")

            except Exception as e:
                logger.error(f"Error saving stitched video to database: {e}")
                # Don't fail the request if database save fails
                asset_id = None

            # Automatic Audio Stitching (if enabled)
            audio_stitch_result = None
            final_video_info = None

            if auto_audio_stitch:
                logger.info(f"Auto audio stitching enabled for script {script_id}")
                try:
                    # Perform automatic audio stitching
                    audio_stitch_result = await perform_audio_stitching(
                        script_id=script_id,
                        org_id=org_id,
                        enable_ducking=enable_ducking,
                        enable_ai_enhancement=enable_ai_enhancement,
                        session=session,
                    )

                    if audio_stitch_result and audio_stitch_result.get("success"):
                        logger.info(
                            f"Auto audio stitching completed successfully for script {script_id}"
                        )
                        final_video_info = {
                            "success": True,
                            "s3_url": audio_stitch_result.get("s3_url"),
                            "filename": audio_stitch_result.get("filename"),
                            "duration": audio_stitch_result.get("duration"),
                            "file_size_mb": audio_stitch_result.get("file_size_mb"),
                            "audio_tracks_mixed": audio_stitch_result.get("audio_tracks_mixed"),
                            "audio_info": audio_stitch_result.get("audio_info"),
                            "enhancement_applied": audio_stitch_result.get("enhancement_applied"),
                        }
                    else:
                        logger.warning(
                            f"Auto audio stitching failed for script {script_id}: {audio_stitch_result.get('error') if audio_stitch_result else 'Unknown error'}"
                        )
                        final_video_info = {
                            "success": False,
                            "error": (
                                audio_stitch_result.get("error")
                                if audio_stitch_result
                                else "Audio stitching failed"
                            ),
                        }

                except Exception as e:
                    logger.error(f"Error in auto audio stitching for script {script_id}: {str(e)}")
                    final_video_info = {
                        "success": False,
                        "error": f"Audio stitching error: {str(e)}",
                    }

            logger.info(f"Video stitching completed successfully for script: {script_id}")

            response = {
                "success": True,
                "message": "Video stitching completed successfully",
                "script_id": script_id,
                "video_output_path": stitch_result.get("output_path"),
                "video_s3_url": stitch_result.get("s3_url"),
                "video_asset_id": asset_id,
                "video_details": stitch_result,
                "auto_audio_stitch_enabled": auto_audio_stitch,
            }

            if auto_audio_stitch and final_video_info:
                response["audio_stitching"] = final_video_info
                if final_video_info.get("success"):
                    response["final_video_s3_url"] = final_video_info.get("s3_url")
                    response["message"] = "Video stitching and audio mixing completed successfully"
                else:
                    response["message"] = (
                        "Video stitching completed successfully, but audio mixing failed"
                    )

            return response
        else:
            logger.error(
                f"Video stitching failed for script {script_id}: {stitch_result.get('error')}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Video stitching failed: {stitch_result.get('error', 'Unknown error')}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in video stitching: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Video stitching failed: {str(e)}")

        if stitch_result.get("success"):
            # Save the stitched video as a VideoAsset in the database
            try:
                asset_id = str(uuid.uuid4())
                s3_url = stitch_result.get("s3_url")

                video_asset = VideoAsset(
                    asset_id=asset_id,
                    org_id=org_id,
                    s3_url=s3_url,
                    generation_method="rendered",  # Stitched videos are marked as 'rendered'
                    script_id=script_id,
                    local_path=None,  # No local path since it's uploaded to S3
                )
                session.add(video_asset)
                await session.commit()
                logger.info(f"Successfully saved stitched video asset to database: {asset_id}")

            except Exception as e:
                logger.error(f"Error saving stitched video to database: {e}")
                # Don't fail the request if database save fails
                asset_id = None

            # Automatic Audio Stitching (if enabled)
            audio_stitch_result = None
            final_video_info = None

            if auto_audio_stitch:
                logger.info(f"Auto audio stitching enabled for script {script_id}")
                try:
                    # Perform automatic audio stitching
                    audio_stitch_result = await perform_audio_stitching(
                        script_id=script_id,
                        org_id=org_id,
                        enable_ducking=enable_ducking,
                        enable_ai_enhancement=enable_ai_enhancement,
                        session=session,
                    )

                    if audio_stitch_result and audio_stitch_result.get("success"):
                        logger.info(
                            f"Auto audio stitching completed successfully for script {script_id}"
                        )
                        final_video_info = {
                            "success": True,
                            "s3_url": audio_stitch_result.get("s3_url"),
                            "filename": audio_stitch_result.get("filename"),
                            "duration": audio_stitch_result.get("duration"),
                            "file_size_mb": audio_stitch_result.get("file_size_mb"),
                            "audio_tracks_mixed": audio_stitch_result.get("audio_tracks_mixed"),
                            "audio_info": audio_stitch_result.get("audio_info"),
                            "enhancement_applied": audio_stitch_result.get("enhancement_applied"),
                        }
                    else:
                        logger.warning(
                            f"Auto audio stitching failed for script {script_id}: {audio_stitch_result.get('error') if audio_stitch_result else 'Unknown error'}"
                        )
                        final_video_info = {
                            "success": False,
                            "error": (
                                audio_stitch_result.get("error")
                                if audio_stitch_result
                                else "Audio stitching failed"
                            ),
                        }

                except Exception as e:
                    logger.error(f"Error in auto audio stitching for script {script_id}: {str(e)}")
                    final_video_info = {
                        "success": False,
                        "error": f"Audio stitching error: {str(e)}",
                    }

            logger.info(f"Video stitching completed successfully for script: {script_id}")

            response = {
                "success": True,
                "message": "Video stitching completed successfully",
                "script_id": script_id,
                "video_output_path": stitch_result.get("output_path"),
                "video_s3_url": stitch_result.get("s3_url"),
                "video_asset_id": asset_id,
                "video_details": stitch_result,
                "auto_audio_stitch_enabled": auto_audio_stitch,
            }

            if auto_audio_stitch and final_video_info:
                response["audio_stitching"] = final_video_info
                if final_video_info.get("success"):
                    response["final_video_s3_url"] = final_video_info.get("s3_url")
                    response["message"] = "Video stitching and audio mixing completed successfully"
                else:
                    response["message"] = (
                        "Video stitching completed successfully, but audio mixing failed"
                    )

            return response
        else:
            logger.error(
                f"Video stitching failed for script {script_id}: {stitch_result.get('error')}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Video stitching failed: {stitch_result.get('error', 'Unknown error')}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in video stitching: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Video stitching failed: {str(e)}")


async def perform_audio_stitching(
    script_id: str,
    org_id: str,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    session: AsyncSession = None,
) -> Dict[str, Any]:
    """
    Perform automatic audio stitching after video stitching
    This function replicates the audio stitching logic from the audio_stitching.py route
    """
    try:
        logger.info(f"Starting automatic audio stitching for script: {script_id}")

        # Find the stitched video for this script
        stitched_video_asset = None
        stitched_stmt = (
            select(VideoAsset)
            .where(
                VideoAsset.script_id == script_id,
                VideoAsset.generation_method
                == "rendered",  # Stitched videos are marked as 'rendered'
                VideoAsset.deleted_at.is_(None),
            )
            .order_by(VideoAsset.created_at.desc())
            .limit(1)
        )

        stitched_result = await session.execute(stitched_stmt)
        stitched_video_asset = stitched_result.scalar_one_or_none()

        if not stitched_video_asset:
            return {"success": False, "error": f"No stitched video found for script {script_id}"}

        # Download from S3 if not present locally
        s3_key = stitched_video_asset.s3_url
        local_dir = "output/stitched_videos"
        os.makedirs(local_dir, exist_ok=True)
        local_path = os.path.join(local_dir, os.path.basename(s3_key))

        logger.info(
            f"[Auto AudioStitching] Looking for stitched video: s3_key={s3_key}, local_path={local_path}"
        )

        if not os.path.exists(local_path):
            logger.info(
                f"[Auto AudioStitching] Local file not found. Attempting to download from S3..."
            )
            try:
                s3_client.download_file(S3_BUCKET, s3_key, local_path=local_path)
                logger.info(f"[Auto AudioStitching] Downloaded from S3: {local_path}")
            except Exception as e:
                logger.error(
                    f"[Auto AudioStitching] Failed to download from S3: bucket={S3_BUCKET}, key={s3_key}, error={e}"
                )
                return {
                    "success": False,
                    "error": f"Failed to download stitched video from S3: {str(e)}",
                }

        if not os.path.exists(local_path):
            return {"success": False, "error": f"Stitched video file not found: {local_path}"}

        stitched_video_path = local_path

        # Fetch ALL background music assets for this script
        bg_music_stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.script_id == script_id,
                    AudioAsset.source_type.in_(["background_music", "user_uploaded"]),
                    AudioAsset.deleted_at.is_(None),
                )
            )
            .order_by(AudioAsset.created_at.desc())
        )

        bg_music_result = await session.execute(bg_music_stmt)
        bg_music_assets = bg_music_result.scalars().all()

        logger.info(
            f"[Auto AudioStitching] Found {len(bg_music_assets)} background music assets for script {script_id}"
        )

        background_music_paths = []
        for asset in bg_music_assets:
            path = asset.local_path or asset.s3_url
            if path and not os.path.exists(path):
                # Handle different URL formats
                s3_key = None
                if path.startswith("http") and ".amazonaws.com/" in path:
                    s3_key = path.split(".amazonaws.com/")[-1].split("?")[0]
                elif not path.startswith("http"):
                    s3_key = path
                else:
                    logger.warning(
                        f"[Auto AudioStitching] Presigned URL may have expired, attempting direct S3 download"
                    )
                    continue

                if s3_key:
                    local_dir = "output/background_music"
                    os.makedirs(local_dir, exist_ok=True)
                    local_path = os.path.join(local_dir, os.path.basename(s3_key))
                    try:
                        if asset.source_type == "user_uploaded":
                            download_url = s3_client.get_presigned_url(
                                bucket=S3_BUCKET, key=s3_key, ttl=timedelta(hours=1)
                            )
                            response = requests.get(download_url, timeout=120)
                            response.raise_for_status()
                            with open(local_path, "wb") as f:
                                f.write(response.content)
                            logger.info(
                                f"[Auto AudioStitching] Downloaded user-uploaded music: {local_path}"
                            )
                        else:
                            s3_client.download_file(S3_BUCKET, s3_key, local_path=local_path)
                            logger.info(
                                f"[Auto AudioStitching] Downloaded AI-generated music: {local_path}"
                            )
                        path = local_path
                    except Exception as e:
                        logger.error(
                            f"Failed to download background music from S3: {s3_key}, error={e}"
                        )
                        continue

            if path and os.path.exists(path):
                background_music_paths.append(path)

        logger.info(f"[Auto AudioStitching] Using background music files: {background_music_paths}")

        # Handle voiceover files
        logger.info(
            f"[Auto AudioStitching] Creating fresh combined voiceover from individual files..."
        )

        # Get all individual voiceover files
        individual_voiceover_stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.script_id == script_id,
                    AudioAsset.source_type == "voiceover",
                    AudioAsset.deleted_at.is_(None),
                )
            )
            .order_by(AudioAsset.created_at.asc())
        )

        individual_voiceover_result = await session.execute(individual_voiceover_stmt)
        individual_voiceover_assets = individual_voiceover_result.scalars().all()

        voiceover_paths = []

        if individual_voiceover_assets:
            logger.info(
                f"[Auto AudioStitching] Found {len(individual_voiceover_assets)} individual voiceover files to combine"
            )

            # Get voiceover data
            voiceover_data = await audio_mixer.get_voiceover_data_for_script(
                script_id=script_id, org_id=org_id, session=session
            )

            if voiceover_data:
                # Get video duration for proper timing
                total_video_duration = 60.0  # Default
                try:
                    with VideoFileClip(stitched_video_path) as video_clip:
                        total_video_duration = video_clip.duration
                    logger.info(
                        f"[Auto AudioStitching] Video duration: {total_video_duration:.2f}s"
                    )
                except Exception as e:
                    logger.warning(f"Could not get video duration, using default: {e}")

                # Combine voiceover files
                combined_voiceover_path = audio_mixer.combine_voiceover_files(
                    voiceover_data=voiceover_data, total_video_duration=total_video_duration
                )

                # Save combined voiceover to S3 and database
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                combined_s3_key = f"audio/voiceover/combined_voiceover_{script_id}_{timestamp}.mp3"

                s3_client.upload_file(
                    bucket=S3_BUCKET, key=combined_s3_key, file_path=combined_voiceover_path
                )
                logger.info(
                    f"[Auto AudioStitching] Uploaded fresh combined voiceover to S3: {combined_s3_key}"
                )

                # Mark previous combined voiceovers as deleted (soft delete)
                try:
                    delete_stmt = (
                        update(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.script_id == script_id,
                                AudioAsset.source_type == "voiceover_combined",
                                AudioAsset.deleted_at.is_(None),
                            )
                        )
                        .values(deleted_at=datetime.utcnow())
                    )
                    await session.execute(delete_stmt)
                    logger.info(
                        f"[Auto AudioStitching] Marked previous combined voiceovers as deleted"
                    )
                except Exception as e:
                    logger.warning(f"Could not delete previous combined voiceovers: {e}")

                # Save new combined voiceover to database
                combined_audio_asset = AudioAsset(
                    org_id=org_id,
                    asset_id=str(uuid.uuid4()),
                    s3_url=combined_s3_key,
                    source_type="voiceover_combined",
                    script_id=script_id,
                    scene_id=None,
                    local_path=None,
                    generation_status="completed",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    deleted_at=None,
                )

                session.add(combined_audio_asset)
                await session.commit()

                voiceover_paths.append(combined_voiceover_path)
                logger.info(
                    f"[Auto AudioStitching] Created and using fresh combined voiceover: {combined_voiceover_path}"
                )
            else:
                logger.warning(f"[Auto AudioStitching] Could not get voiceover data for combining")
        else:
            logger.info(f"[Auto AudioStitching] No individual voiceover files found")

        logger.info(f"[Auto AudioStitching] Using voiceover files: {voiceover_paths}")

        # Generate unique output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"script_{script_id}_final_mixed_{timestamp}.mp4"

        # Log what we found
        logger.info(f"Auto audio stitching for script {script_id}:")
        logger.info(f"  - Stitched video: {stitched_video_path}")
        logger.info(f"  - Background music: {background_music_paths}")
        logger.info(f"  - Voiceover: {voiceover_paths}")
        logger.info(f"  - Output: {output_filename}")

        # Mix the audio
        result = audio_mixer.mix_final_video_audio_enhanced(
            stitched_video_path=stitched_video_path,
            background_music_paths=background_music_paths,
            voiceover_paths=voiceover_paths,
            output_filename=output_filename,
            enable_ducking=enable_ducking,
            enable_ai_enhancement=enable_ai_enhancement,
        )

        if not result.get("success", False):
            return {
                "success": False,
                "error": f"Audio mixing failed: {result.get('error', 'Unknown error')}",
            }

        # Save to database
        try:
            # Upload to S3
            s3_key = (
                f"Vidflux-Assets/audio-stitching-assets/script_{script_id}/audio_{uuid.uuid4()}.mp4"
            )
            s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=result["output_path"])

            video_asset = VideoAsset(
                asset_id=str(uuid.uuid4()),
                org_id=org_id,
                s3_url=s3_key,
                generation_method="audio_mixed",
                script_id=script_id,
                local_path=None,
            )
            session.add(video_asset)
            await session.commit()
            logger.info(f"[Auto AudioStitching] Successfully saved final video asset to database")
            logger.info(f"[Auto AudioStitching] S3 URL: {s3_key}")

        except Exception as e:
            logger.error(f"Error saving final video to database: {e}")
            # Don't fail the request if database save fails

        # Generate presigned URL for response
        presigned_url = None
        if s3_key:
            try:
                presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
            except Exception as e:
                logger.error(f"Failed to generate presigned URL: {e}")
                presigned_url = s3_key

        return {
            "success": True,
            "s3_url": presigned_url,
            "message": "Auto audio mixing completed successfully",
            "filename": result["filename"],
            "duration": result["duration"],
            "file_size_mb": result["file_size_mb"],
            "audio_tracks_mixed": result["audio_tracks_mixed"],
            "audio_info": result["audio_info"],
            "enhancement_applied": result["enhancement_applied"],
            "quality_analysis": result.get("quality_analysis"),
        }

    except Exception as e:
        logger.error(f"Error in automatic audio stitching for script {script_id}: {str(e)}")
        return {"success": False, "error": f"Automatic audio stitching failed: {str(e)}"}
