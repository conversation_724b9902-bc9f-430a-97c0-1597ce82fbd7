# Global imports
import os
import uuid
import time
import asyncio
import logging
import requests
import tempfile
from typing import List
from datetime import datetime, timedelta
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from concurrent.futures import ThreadPoolExecutor
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks

# Local imports
from src.shared.utils.s3_client import S3Client
from src.shared.core.dependencies import require_auth
from src.shared.config.database import get_database_session, get_sync_db
from src.video_service.services.video_generator import KlingVideoGenerator
from src.shared.utils.prompt_cleaner import format_video_prompt_for_display
from src.shared.models.database_models import Scene, ImageAsset, Script, VideoAsset
from src.video_service.schemas.requests import (
    GenerateVideoByScenesRequest,
    UpdateVideoPromptAndRegenerateRequest,
)
from src.video_service.schemas.responses import (
    VideoGenerationInitResponse,
    VideoGenerationStatusResponse,
    SceneVideoResponse,
    VideoGenerationScriptStatusResponse,
    SceneStatusResponse,
    VideoRegenerationResponse,
)

logger = logging.getLogger(__name__)

# Initialize S3 client
S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)

# Thread pool for video generation tasks
executor = ThreadPoolExecutor(max_workers=4)

router = APIRouter(prefix="/videos", tags=["Video Generation"])


def save_video_from_url(video_url, scene_id):
    """Download video from URL and save locally"""
    try:
        dir_path = f"output/videos/scene_{scene_id}"
        os.makedirs(dir_path, exist_ok=True)
        file_name = f"video_{uuid.uuid4()}.mp4"
        file_path = os.path.join(dir_path, file_name)

        # Download video with timeout and error handling
        response = requests.get(video_url, timeout=120)
        response.raise_for_status()  # Raise exception for bad status codes

        # Write video content to file
        with open(file_path, "wb") as f:
            f.write(response.content)

        # Verify file was written successfully
        if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
            logger.info(f"✅ Video saved successfully: {file_path}")
            return file_path
        else:
            logger.error(f"❌ Video file not written properly: {file_path}")
            return None

    except requests.RequestException as e:
        logger.error(f"❌ Failed to download video from {video_url}: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ Error saving video for scene {scene_id}: {e}")
        return None


def download_video_to_tempfile(video_url):
    try:
        response = requests.get(video_url, timeout=120, stream=True)
        response.raise_for_status()
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as tmp_file:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    tmp_file.write(chunk)
            temp_path = tmp_file.name
        return temp_path
    except Exception as e:
        logger.error(f"❌ Failed to download video from {video_url}: {e}")
        return None


def delete_previous_videos_for_scene(scene_id: str, org_id: str, db_session):
    """
    Delete previous videos for a scene from S3 and mark them as inactive in database
    """
    try:
        logger.info(f"🎬 Deleting previous videos for scene {scene_id}")

        # Find all existing video assets for this scene
        existing_videos = (
            db_session.query(VideoAsset)
            .filter(
                and_(
                    VideoAsset.scene_id == scene_id,
                    VideoAsset.org_id == org_id,
                    VideoAsset.deleted_at.is_(None),
                )
            )
            .all()
        )

        if not existing_videos:
            logger.info(f"ℹ️ No previous videos found for scene {scene_id}")
            return 0

        deleted_count = 0

        for video in existing_videos:
            try:
                # Delete from S3 if s3_url exists
                if video.s3_url:
                    # Handle both full URLs and just S3 keys
                    s3_key = video.s3_url
                    if s3_key.startswith("http") and ".amazonaws.com/" in s3_key:
                        # Extract S3 key from full URL
                        from urllib.parse import urlparse

                        parsed = urlparse(s3_key)
                        s3_key = parsed.path.lstrip("/")

                    # Delete from S3
                    try:
                        if s3_client.delete_file(S3_BUCKET, s3_key):
                            logger.info(f"🗑️ Deleted video from S3: {s3_key}")
                            deleted_count += 1
                        else:
                            logger.warning(f"⚠️ Video not found in S3: {s3_key}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not delete video from S3 {s3_key}: {e}")

                # Delete the database record completely (not just mark as deleted)
                db_session.delete(video)
                logger.info(f"🗑️ Deleted VideoAsset record: {video.asset_id}")

            except Exception as e:
                logger.error(f"❌ Error processing video {video.id}: {e}")

        # Commit database changes
        db_session.commit()
        logger.info(f"✅ Deleted {deleted_count} previous videos for scene {scene_id}")
        return deleted_count

    except Exception as e:
        logger.error(f"❌ Error deleting previous videos for scene {scene_id}: {e}")
        return 0


async def run_in_threadpool(func, *args, **kwargs):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(executor, lambda: func(*args, **kwargs))


async def generate_video_background_async(scene_id: str, org_id: str, user_id: str):
    await run_in_threadpool(_generate_video_background, scene_id, org_id, user_id)


async def generate_multiple_videos_concurrent(scene_ids: list, org_id: str, user_id: str):
    """
    Generate videos for multiple scenes concurrently using asyncio.gather
    This allows all scenes to be processed simultaneously instead of sequentially
    """
    logger.info(f"🚀 Starting concurrent video generation for {len(scene_ids)} scenes")

    # Create tasks for all scenes
    tasks = []
    for scene_id in scene_ids:
        task = asyncio.create_task(generate_video_background_async(scene_id, org_id, user_id))
        tasks.append(task)
        logger.info(f"✅ Created concurrent task for scene {scene_id}")

    # Execute all tasks concurrently and wait for completion
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Log results
        successful_scenes = []
        failed_scenes = []

        for i, result in enumerate(results):
            scene_id = scene_ids[i]
            if isinstance(result, Exception):
                logger.error(f"❌ Scene {scene_id} failed: {str(result)}")
                failed_scenes.append(scene_id)
            else:
                logger.info(f"✅ Scene {scene_id} completed successfully")
                successful_scenes.append(scene_id)

        logger.info(
            f"🎬 Concurrent generation completed: {len(successful_scenes)} successful, {len(failed_scenes)} failed"
        )
        return {"successful": successful_scenes, "failed": failed_scenes}

    except Exception as e:
        logger.error(f"❌ Error in concurrent video generation: {str(e)}")
        return {"successful": [], "failed": scene_ids}


@router.post("/generate", response_model=VideoGenerationInitResponse, status_code=201)
async def generate_video(
    request: GenerateVideoByScenesRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(require_auth),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Initiate video generation for scenes. Returns immediately with scene IDs.
    """
    org_id, user_id, email = user_info

    if not request.scene_ids:
        raise HTTPException(status_code=400, detail="No scene_ids provided")

    # Validate all scenes exist and belong to user
    valid_scene_ids = []
    for scene_id in request.scene_ids:
        scene_stmt = select(Scene).where(and_(Scene.id == scene_id, Scene.status == "active"))
        scene_result = await db.execute(scene_stmt)
        scene = scene_result.scalar_one_or_none()

        if not scene:
            logger.warning(f"Scene {scene_id} not found or inactive")
            continue

        # Check if scene belongs to user's script
        script_stmt = select(Script).where(
            and_(Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id)
        )
        script_result = await db.execute(script_stmt)
        script = script_result.scalar_one_or_none()

        if not script:
            logger.warning(
                f"Script {scene.script_id} not found or access denied for scene {scene_id}"
            )
            continue

        valid_scene_ids.append(scene_id)

    if not valid_scene_ids:
        raise HTTPException(status_code=400, detail="No valid scene_ids provided")

    # Set all scenes in the script to 'pending' if they are not already queued/in_progress/completed/failed
    script_stmt = select(Scene).where(Scene.script_id == script.id)
    script_scenes_result = await db.execute(script_stmt)
    script_scenes = script_scenes_result.scalars().all()
    for scene in script_scenes:
        if scene.id not in valid_scene_ids and scene.generation_status not in [
            "queued",
            "in_progress",
            "completed",
            "failed",
        ]:
            scene.generation_status = "pending"
    # Set only the requested scenes to 'queued'
    for scene_id in valid_scene_ids:
        scene_obj = await db.get(Scene, scene_id)
        if scene_obj:
            scene_obj.generation_status = "queued"
    await db.commit()

    # Start concurrent video generation for all scenes using a single background task
    logger.info(f"🎬 Starting concurrent video generation for {len(valid_scene_ids)} scenes")
    background_tasks.add_task(generate_multiple_videos_concurrent, valid_scene_ids, org_id, user_id)

    logger.info(f"✅ Concurrent video generation task added successfully. Returning 201 response.")

    return VideoGenerationInitResponse(
        scene_ids=valid_scene_ids,
        message=f"Concurrent video generation started for {len(valid_scene_ids)} scenes",
        estimated_completion_time=480,  # 8 minutes (same time but all scenes process simultaneously!)
        status="processing",
    )


def _generate_video_background(scene_id: str, org_id: str, user_id: str):
    """
    Background task to generate video for a scene.
    """
    logger.info(f"🎬 BACKGROUND TASK STARTED for scene_id: {scene_id}")
    try:
        db = next(get_sync_db())
        try:
            scene = db.query(Scene).filter(Scene.id == scene_id).first()
            if not scene:
                logger.error(f"❌ Scene {scene_id} not found")
                return
            # Check if a video already exists for this scene in the bucket
            existing_video = (
                db.query(VideoAsset)
                .filter(
                    VideoAsset.scene_id == scene_id,
                    VideoAsset.org_id == org_id,
                    VideoAsset.deleted_at.is_(None),
                )
                .order_by(VideoAsset.created_at.desc())
                .first()
            )
            if existing_video and existing_video.s3_url:
                logger.info(f"✅ Video already exists for scene {scene_id}, skipping generation.")
                scene.generation_status = "completed"
                db.commit()
                return
            # Set to in_progress
            scene.generation_status = "in_progress"
            db.commit()

            # Get first active image for the scene
            image = (
                db.query(ImageAsset)
                .filter(
                    and_(
                        ImageAsset.org_id == org_id,
                        ImageAsset.scene_id == scene_id,
                        ImageAsset.status == "active",
                        ImageAsset.s3_url.isnot(None),
                    )
                )
                .order_by(ImageAsset.created_at)
                .first()
            )

            if not image or not image.s3_url:
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ No active image with S3 URL found for scene {scene_id}")
                return

            # Generate video using simple approach
            generator = KlingVideoGenerator()

            # Generate presigned URL for the image S3 key
            from urllib.parse import urlparse

            parsed = urlparse(image.s3_url)
            s3_key = parsed.path.lstrip("/")
            presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))

            # Create video using simple method
            images_payload = [{"url": presigned_url}]
            scene_data = {
                "scene_number": scene.scene_number,
                "scene_description": scene.description or "",
                "images": images_payload,
            }

            # Use simple video generation
            video_result = generator.generate_video_simple(scene_data)

            # Save the video prompt to the scene (extract from the generator)
            try:
                # Get the professional prompt that was generated
                if (
                    hasattr(generator, "_last_generated_prompt")
                    and generator._last_generated_prompt
                ):
                    scene.video_prompt = generator._last_generated_prompt
                    logger.info(
                        f"✅ Stored video prompt in scene {scene_id}: {scene.video_prompt[:100]}..."
                    )
                else:
                    logger.warning(f"⚠️ No video prompt available to store for scene {scene_id}")
            except Exception as prompt_error:
                logger.warning(f"⚠️ Could not store video prompt in scene: {str(prompt_error)}")

            if not video_result.get("success"):
                scene.generation_status = "failed"
                db.commit()
                logger.error(
                    f"❌ Video generation failed for scene {scene_id}: {video_result.get('error')}"
                )
                return

            # Get the video URL from the result
            video_url = video_result.get("video_url")
            if not video_url:
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ No video URL returned for scene {scene_id}")
                return

            # Download video to temp file
            temp_path = download_video_to_tempfile(video_url)
            if not temp_path or not os.path.exists(temp_path):
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ Failed to download video for scene {scene_id}")
                return

            # Upload to S3
            asset_id = str(uuid.uuid4())
            s3_key = f"Vidflux-Assets/video-assets/scene_{scene_id}/video_{asset_id}.mp4"
            upload_success = False
            try:
                s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=temp_path)
                upload_success = True
            finally:
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
            if upload_success:
                # Create video asset and set status to completed
                video_asset = VideoAsset(
                    asset_id=asset_id,
                    org_id=org_id,
                    s3_url=s3_key,
                    local_path=None,
                    generation_method="rendered",
                    scene_id=scene_id,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                )
                db.add(video_asset)
                scene.generation_status = "completed"
            else:
                scene.generation_status = "failed"
            db.commit()
            logger.info(f"✅ Video generation completed for scene {scene_id}")
            logger.info(f"   S3 URL: {s3_key}")

        except Exception as e:
            # On error, set to failed
            scene = db.query(Scene).filter(Scene.id == scene_id).first()
            if scene:
                scene.generation_status = "failed"
                db.commit()
            logger.error(f"❌ Video generation failed for scene {scene_id}: {e}")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"❌ Video generation failed for scene {scene_id}: {e}")


@router.get("/{scene_id}", response_model=SceneVideoResponse)
async def get_scene_video(
    scene_id: str,
    user_info: tuple = Depends(require_auth),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Get video for a specific scene by scene_id
    - **scene_id**: UUID of the scene
    Returns: Video details with presigned URL
    """
    org_id, user_id, email = user_info

    # Validate scene exists and belongs to user
    scene_stmt = select(Scene).where(and_(Scene.id == scene_id, Scene.status == "active"))
    scene_result = await db.execute(scene_stmt)
    scene = scene_result.scalar_one_or_none()

    if not scene:
        raise HTTPException(status_code=404, detail="Scene not found")

    # Check if scene belongs to user's script
    script_stmt = select(Script).where(
        and_(Script.id == scene.script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await db.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        raise HTTPException(status_code=403, detail="Access denied to this scene")

    # Find video asset for this scene
    video_stmt = (
        select(VideoAsset)
        .where(
            and_(
                VideoAsset.scene_id == scene_id,
                VideoAsset.org_id == org_id,
                VideoAsset.deleted_at.is_(None),
            )
        )
        .order_by(VideoAsset.created_at.desc())
    )
    video_result = await db.execute(video_stmt)
    video = video_result.scalar()

    if not video or not video.s3_url:
        raise HTTPException(status_code=404, detail="Video not found for this scene")

    # Generate presigned URL for the video
    from urllib.parse import urlparse

    parsed = urlparse(video.s3_url)
    s3_key = parsed.path.lstrip("/")
    presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))

    return SceneVideoResponse(
        scene_id=scene_id,
        video_id=str(video.asset_id),
        url=presigned_url,
        created_at=video.created_at,
        status="completed",
    )


@router.put("/update-prompt/{scene_id}", response_model=VideoRegenerationResponse)
async def update_video_prompt_and_regenerate(
    scene_id: str,
    request: UpdateVideoPromptAndRegenerateRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(require_auth),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Update the video prompt for a scene and regenerate the video with the new prompt
    """
    from uuid import UUID

    try:
        org_id, user_id, email = user_info

        # Get the scene to validate it exists and belongs to the user
        try:
            scene_uuid = UUID(scene_id)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid scene ID format: {scene_id}")

        scene_query = select(Scene).where(Scene.id == scene_uuid)
        scene_result = await db.execute(scene_query)
        scene = scene_result.scalar_one_or_none()

        if not scene:
            raise HTTPException(status_code=404, detail=f"Scene with ID '{scene_id}' not found")

        # Get the script to validate user access
        script_query = select(Script).where(Script.id == scene.script_id)
        script_result = await db.execute(script_query)
        script = script_result.scalar_one_or_none()

        if not script:
            raise HTTPException(status_code=404, detail="Script not found")

        # Validate user has access to this script
        if script.user_id != UUID(user_id):
            logger.warning(
                f"User {user_id} attempted to access script {script.id} owned by {script.user_id}"
            )
            raise HTTPException(
                status_code=403, detail="You don't have permission to update this scene"
            )

        # Update the scene's video_prompt with the new prompt
        scene.video_prompt = request.updated_prompt
        scene.generation_status = "processing"  # Mark as processing
        await db.commit()

        logger.info(
            f"✅ Updated video prompt for scene {scene_id}: {request.updated_prompt[:100]}..."
        )

        # Start background task to regenerate the video using the updated prompt
        # (The background task will handle deleting old videos and creating new ones)
        background_tasks.add_task(
            _generate_video_with_custom_prompt_background,
            scene_id,
            request.updated_prompt,
            request.aspect_ratio,
            request.duration,
            org_id,
            str(user_id),
        )

        logger.info(f"🚀 Started video regeneration for scene {scene_id} with updated prompt")

        return VideoRegenerationResponse(
            scene_id=scene_id,
            status="processing",
            message="Video regeneration started with updated prompt",
            video_prompt=request.updated_prompt,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"❌ Error updating video prompt and regenerating for scene {scene_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to update video prompt and regenerate: {str(e)}"
        )


def _generate_video_with_custom_prompt_background(
    scene_id: str, custom_prompt: str, aspect_ratio: str, duration: str, org_id: str, user_id: str
):
    """
    Background task to regenerate video using a custom prompt
    """
    try:
        logger.info(f"🎬 Starting video regeneration with custom prompt for scene {scene_id}")

        # Get sync database session
        db = next(get_sync_db())

        try:
            # Get the scene
            scene = db.query(Scene).filter(Scene.id == scene_id).first()

            if not scene:
                logger.error(f"❌ Scene {scene_id} not found")
                return

            # Delete previous videos from S3 and database
            logger.info(f"🗑️ Deleting previous videos for scene {scene_id}...")
            try:
                deleted_count = delete_previous_videos_for_scene(scene_id, org_id, db)
                logger.info(f"✅ Deleted {deleted_count} previous videos for scene {scene_id}")
            except Exception as delete_error:
                logger.warning(f"⚠️ Error deleting previous videos: {str(delete_error)}")
                # Continue with generation even if deletion fails

            # Get the latest image for this scene
            image = (
                db.query(ImageAsset)
                .filter(
                    and_(
                        ImageAsset.scene_id == scene_id,
                        ImageAsset.org_id == org_id,
                        ImageAsset.status == "active",
                        ImageAsset.s3_url.isnot(None),
                    )
                )
                .order_by(ImageAsset.created_at)
                .first()
            )

            if not image or not image.s3_url:
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ No active image with S3 URL found for scene {scene_id}")
                return

            # Generate video using custom prompt
            generator = KlingVideoGenerator()

            # Override the prompt generation method to use our custom prompt
            generator._last_generated_prompt = custom_prompt

            # Generate presigned URL for the image S3 key
            from urllib.parse import urlparse

            parsed = urlparse(image.s3_url)
            s3_key = parsed.path.lstrip("/")
            presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))

            # Create video using custom prompt directly
            video_result = generator.generate_video_with_subscribe(
                image_url=presigned_url, prompt=custom_prompt
            )

            if not video_result.get("success"):
                scene.generation_status = "failed"
                db.commit()
                logger.error(
                    f"❌ Video generation failed for scene {scene_id}: {video_result.get('error')}"
                )
                return

            # Extract video URL from result
            data = video_result.get("data", {})
            video_url = None
            if isinstance(data.get("video"), dict):
                video_url = data["video"].get("url")
            video_url = video_url or data.get("video_url")

            if not video_url:
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ No video URL returned for scene {scene_id}")
                return

            # Download video to temp file
            temp_path = download_video_to_tempfile(video_url)
            if not temp_path or not os.path.exists(temp_path):
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ Failed to download video for scene {scene_id}")
                return

            # Upload to S3
            video_key = f"Vidflux-Assets/video-assets/scene_{scene_id}/video_{uuid.uuid4()}.mp4"
            try:
                s3_full_url = s3_client.upload_file(
                    bucket=S3_BUCKET, key=video_key, file_path=temp_path
                )
                logger.info(f"✅ Uploaded video to S3: {video_key}")
                # Store only the key (relative path), not the full URL
                s3_url = video_key
            except Exception as upload_error:
                scene.generation_status = "failed"
                db.commit()
                logger.error(f"❌ Failed to upload video to S3: {str(upload_error)}")
                return
            finally:
                # Clean up temp file
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)

            # Create video asset record
            video_asset = VideoAsset(
                asset_id=str(uuid.uuid4()),
                org_id=org_id,
                s3_url=s3_url,
                generation_method="rendered",  # Use existing enum value for rendered videos
                scene_id=scene.id,
                script_id=scene.script_id,
                created_at=datetime.utcnow(),
            )

            db.add(video_asset)

            # Update scene status
            scene.generation_status = "completed"

            db.commit()

            logger.info(f"✅ Video regeneration completed for scene {scene_id}")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"❌ Background video regeneration failed for scene {scene_id}: {str(e)}")

        # Update scene status to failed
        try:
            db = next(get_sync_db())
            try:
                scene = db.query(Scene).filter(Scene.id == scene_id).first()
                if scene:
                    scene.generation_status = "failed"
                    db.commit()
            except Exception as db_error:
                logger.warning(f"⚠️ Could not update scene status to failed: {str(db_error)}")

            db.close()
        except:
            pass


@router.get("/status/{script_id}", response_model=VideoGenerationScriptStatusResponse)
async def get_video_generation_status(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Get video generation status for all scenes in a script
    - **script_id**: UUID of the script
    Returns: Current generation status of all scenes (non-blocking, immediate response)
    """
    org_id, user_id, email = user_info

    logger.info(f"🔍 Checking video generation status for script {script_id}")

    # Validate script exists and belongs to user
    script_stmt = select(Script).where(
        and_(Script.id == script_id, Script.org_id == org_id, Script.user_id == user_id)
    )
    script_result = await db.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        logger.warning(f"Script {script_id} not found or access denied")
        raise HTTPException(status_code=404, detail="Script not found")

    # Query all scenes for this script with only required fields
    scenes_stmt = (
        select(
            Scene.id,
            Scene.scene_number,
            Scene.generation_status,
            Scene.video_prompt,  # Include video prompt
        )
        .where(Scene.script_id == script_id)
        .order_by(Scene.scene_number)
    )

    scenes_result = await db.execute(scenes_stmt)
    scenes = scenes_result.all()

    logger.info(f"📊 Found {len(scenes)} scenes for script {script_id}")

    # Format response
    scenes_data = []
    for scene in scenes:
        presigned_url = None
        actual_status = scene.generation_status
        if scene.generation_status == "completed":
            # Find the latest video asset for this scene
            video_stmt = (
                select(VideoAsset)
                .where(
                    and_(
                        VideoAsset.scene_id == scene.id,
                        VideoAsset.org_id == org_id,
                        VideoAsset.deleted_at.is_(None),
                    )
                )
                .order_by(VideoAsset.created_at.desc())
            )
            video_result = await db.execute(video_stmt)
            video = video_result.scalar()
            if video and video.s3_url:
                from urllib.parse import urlparse

                parsed = urlparse(video.s3_url)
                s3_key = parsed.path.lstrip("/")
                presigned_url = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
            else:
                # No video asset found, so status should not be completed
                actual_status = "pending"
        scenes_data.append(
            SceneStatusResponse(
                id=str(scene.id),
                scene_number=scene.scene_number,
                generation_status=actual_status,
                presigned_url=presigned_url,
                video_prompt=format_video_prompt_for_display(
                    scene.video_prompt
                ),  # Format video prompt for frontend
            )
        )

    logger.info(f"✅ Returning status for {len(scenes_data)} scenes (non-blocking)")

    return VideoGenerationScriptStatusResponse(script_id=script_id, scenes=scenes_data)
