# Global imports
import os
import glob
import uuid
import requests
from loguru import logger
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from urllib.parse import urlparse
from sqlalchemy import and_, select, desc, update
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends

# Local imports
from src.shared.core.dependencies import require_auth, get_database_session
from src.shared.models.database_models import VideoAsset, Script, Scene, AudioAsset, TaskQueue
from src.video_service.services.runpod_service import runpod_service
from src.shared.utils.s3_client import S3Client
from src.shared.schemas.script import TaskQueueResponse

router = APIRouter(prefix="/stitching", tags=["Video Stitching"])

# Initialize S3 client
S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)


@router.get("/status")
async def get_stitching_status():
    """Get video stitching service status"""
    runpod_configured = runpod_service.is_configured()
    return {
        "service": "Video Stitching",
        "status": "running",
        "version": "3.0.0 (RunPod)",
        "runpod_configured": runpod_configured,
        "processing_mode": "runpod" if runpod_configured else "local",
    }


@router.post("/stitch")
async def stitch_videos(
    script_id: str,
    auto_audio_stitch: bool = True,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    use_runpod: bool = True,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Main video stitching endpoint - handles both video and audio stitching
    Now uses Enhanced Agentic Video Stitcher for improved quality and features

    Args:
        script_id: The script ID to stitch videos for
        auto_audio_stitch: Whether to automatically perform audio stitching after video stitching
        enable_ducking: Enable audio ducking during mixing
        enable_ai_enhancement: Enable AI audio enhancement
        use_runpod: Use RunPod for processing (recommended)
        use_enhanced_stitcher: Use the enhanced agentic video stitcher (recommended)

    Returns:
        Task information for polling status
    """
    try:
        org_id, user_id, email = user_info

        # Validate script exists and belongs to user
        script_query = select(Script).where(
            and_(Script.id == script_id, Script.org_id == org_id, Script.user_id == user_id)
        )
        result = await session.execute(script_query)
        script = result.scalar_one_or_none()

        if not script:
            raise HTTPException(status_code=404, detail="Script not found")

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Create task queue entry
        task_type = (
            "enhanced_agentic_video_stitching"
            if use_enhanced_stitcher
            else "video_stitching_with_audio"
        )
        task_queue = TaskQueue(
            task_id=task_id,
            task_type="video_stitching_with_audio",
            org_id=org_id,
            user_id=user_id,
            related_script_id=script_id,
            input_data={
                "script_id": script_id,
                "auto_audio_stitch": auto_audio_stitch,
                "enable_ducking": enable_ducking,
                "enable_ai_enhancement": enable_ai_enhancement,
                "use_runpod": use_runpod,
                "use_enhanced_stitcher": use_enhanced_stitcher,
            },
            status="pending",
            progress=0,
            queue_name="video_stitching",
        )

        session.add(task_queue)
        await session.commit()

        if use_runpod and runpod_service.is_configured():
            logger.info(f"Submitting to RunPod for script: {script_id}")

            # Submit to RunPod
            try:
                runpod_result = await runpod_service.submit_video_stitching_job(
                    task_id=task_id,
                    script_id=script_id,
                    org_id=org_id,
                    enable_ducking=enable_ducking,
                    enable_ai_enhancement=enable_ai_enhancement,
                    auto_audio_stitch=auto_audio_stitch,
                    webhook_url=f"{os.getenv('API_BASE_URL', 'http://localhost:8000')}/video/stitching/webhook/{task_id}",
                )

                # Update task with RunPod job ID
                if not task_queue.result:
                    task_queue.result = {}
                task_queue.result["runpod_job_id"] = runpod_result.get("runpod_job_id")
                task_queue.status = "processing"
                await session.commit()

                logger.info(
                    f"Successfully submitted to RunPod: {runpod_result.get('runpod_job_id')} for task: {task_id}"
                )

            except Exception as e:
                logger.error(f"Failed to submit to RunPod: {str(e)}")
                task_queue.status = "failed"
                task_queue.error_message = f"RunPod submission failed: {str(e)}"
                await session.commit()
                raise HTTPException(status_code=500, detail=f"Failed to submit to RunPod: {str(e)}")
        else:
            # Fallback to local processing
            logger.info(f"Using local processing for script: {script_id}")

            try:
                # Use enhanced agentic stitcher for local processing
                from src.video_service.services.enhanced_agentic_video_stitcher import (
                    EnhancedAgenticVideoStitcher,
                )
                from src.shared.utils.s3_client import S3Client

                # Initialize S3 client
                s3_client = S3Client(os.getenv("AWS_DEFAULT_REGION", "us-east-2"))

                # Progress callback
                def progress_callback(message: str, progress: int):
                    logger.info(f"Enhanced stitching progress {progress}%: {message}")

                # Initialize enhanced agentic stitcher
                stitcher = EnhancedAgenticVideoStitcher(s3_client, progress_callback)

                # Get scenes data for enhanced stitcher
                scenes_query = (
                    select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
                )

                scenes_result = await session.execute(scenes_query)
                scenes = scenes_result.scalars().all()

                if not scenes:
                    raise HTTPException(status_code=404, detail="No scenes found for script")

                logger.info(f"Found {len(scenes)} scenes to process")

                # Prepare scenes data
                scenes_data = []
                for scene in scenes:
                    scene_data = {
                        "id": str(scene.id),
                        "scene_number": scene.scene_number,
                        "video_asset": None,
                        "voiceover_asset": None,
                        "background_sound_asset": None,
                    }

                    # Get video asset
                    video_query = (
                        select(VideoAsset)
                        .where(
                            and_(VideoAsset.scene_id == scene.id, VideoAsset.deleted_at.is_(None))
                        )
                        .order_by(desc(VideoAsset.created_at))
                    )

                    video_result = await session.execute(video_query)
                    video_asset = video_result.scalars().first()
                    if video_asset:
                        scene_data["video_asset"] = video_asset
                        logger.info(
                            f"Scene {scene.scene_number}: Found video asset {video_asset.asset_id}"
                        )
                    else:
                        logger.warning(f"Scene {scene.scene_number}: No video asset found")

                    # Get voiceover asset
                    voiceover_query = (
                        select(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.scene_id == scene.id,
                                AudioAsset.source_type == "voiceover",
                                AudioAsset.deleted_at.is_(None),
                            )
                        )
                        .order_by(desc(AudioAsset.created_at))
                    )

                    voiceover_result = await session.execute(voiceover_query)
                    voiceover_asset = voiceover_result.scalars().first()
                    if voiceover_asset:
                        scene_data["voiceover_asset"] = voiceover_asset

                    # Get background sound asset
                    bg_sound_query = (
                        select(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.scene_id == scene.id,
                                AudioAsset.source_type == "background_sound",
                                AudioAsset.deleted_at.is_(None),
                            )
                        )
                        .order_by(desc(AudioAsset.created_at))
                    )

                    bg_sound_result = await session.execute(bg_sound_query)
                    bg_sound_asset = bg_sound_result.scalars().first()
                    if bg_sound_asset:
                        scene_data["background_sound_asset"] = bg_sound_asset

                    scenes_data.append(scene_data)

                # Count scenes with video assets
                scenes_with_videos = sum(1 for scene in scenes_data if scene.get("video_asset"))
                logger.info(f"Scenes with video assets: {scenes_with_videos}/{len(scenes_data)}")

                # Get background music assets
                bg_music_query = (
                    select(AudioAsset)
                    .where(
                        and_(
                            AudioAsset.script_id == script_id,
                            AudioAsset.source_type == "background_music",
                            AudioAsset.deleted_at.is_(None),
                        )
                    )
                    .order_by(desc(AudioAsset.created_at))
                )

                bg_music_result = await session.execute(bg_music_query)
                background_music_assets = bg_music_result.scalars().all()

                # Process complete video
                result = await stitcher.process_complete_video(
                    script_id=script_id,
                    auto_audio_stitch=auto_audio_stitch,
                    enable_ducking=enable_ducking,
                    enable_ai_enhancement=enable_ai_enhancement,
                )

                if result.success:
                    # Update task as completed
                    task_queue.status = "completed"
                    task_queue.progress = 100
                    task_queue.result = {
                        "s3_url": result.s3_url,
                        "output_path": result.output_path,
                        "metadata": result.metadata,
                    }
                    await session.commit()

                    logger.info(f"✅ Enhanced video stitching completed for script {script_id}")
                    return TaskQueueResponse(
                        id=task_queue.id,
                        task_id=task_id,
                        task_type=task_queue.task_type,
                        org_id=org_id,
                        user_id=user_id,
                        status="completed",
                        progress=100,
                        result=result.metadata,
                    )
                else:
                    # Update task as failed
                    task_queue.status = "failed"
                    task_queue.error_message = result.error_message
                    await session.commit()

                    logger.error(f"❌ Enhanced stitching failed: {result.error_message}")
                    raise HTTPException(
                        status_code=500, detail=f"Enhanced stitching failed: {result.error_message}"
                    )

            except Exception as e:
                logger.error(f"Local processing failed: {str(e)}")
                task_queue.status = "failed"
                task_queue.error_message = f"Local processing failed: {str(e)}"
                await session.commit()
                raise HTTPException(status_code=500, detail=f"Local processing failed: {str(e)}")

        return TaskQueueResponse(
            id=task_queue.id,
            task_id=task_id,
            task_type="video_stitching_with_audio",
            org_id=org_id,
            user_id=user_id,
            status=task_queue.status,
            progress=task_queue.progress,
            result=None,
            error_message=task_queue.error_message,
            created_at=task_queue.created_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in stitch_videos: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to process stitching request: {str(e)}"
        )


@router.get("/status/{script_id}")
async def get_stitching_status_for_script(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Get comprehensive stitching status for a script
    Checks both task status and final results
    """
    try:
        org_id, user_id, email = user_info

        # Check for latest task (active or completed - match older behavior exactly)
        task_query = (
            select(TaskQueue)
            .where(
                and_(
                    TaskQueue.related_script_id == script_id,
                    TaskQueue.task_type.in_(
                        ["video_stitching_with_audio", "enhanced_agentic_video_stitching"]
                    ),
                    TaskQueue.org_id == org_id,
                    TaskQueue.user_id == user_id,
                )
            )
            .order_by(desc(TaskQueue.created_at))
            .limit(1)
        )

        result = await session.execute(task_query)

        latest_task = result.scalars().first()

        # Check for completed stitched videos
        # Fetch both rendered (base stitched) and audio_mixed (final mixed) assets
        rendered_video = None
        audio_mixed_video = None
        for method in ["rendered", "audio_mixed"]:
            video_query = (
                select(VideoAsset)
                .where(
                    and_(
                        VideoAsset.script_id == script_id,
                        VideoAsset.generation_method == method,
                        VideoAsset.org_id == org_id,
                        VideoAsset.deleted_at.is_(None),
                    )
                )
                .order_by(desc(VideoAsset.created_at))
                .limit(1)
            )
            result = await session.execute(video_query)
            asset = result.scalars().first()
            if method == "rendered":
                rendered_video = asset
            else:
                audio_mixed_video = asset

        # Build response
        response = {
            "script_id": script_id,
            "has_completed_video": bool(rendered_video or audio_mixed_video),
        }

        if latest_task:
            # Check RunPod status if available
            runpod_status = None
            if (
                latest_task.result
                and latest_task.result.get("runpod_job_id")
                and runpod_service.is_configured()
            ):
                try:
                    runpod_status = await runpod_service.get_job_status(
                        latest_task.result["runpod_job_id"]
                    )
                except Exception as e:
                    logger.warning(f"Failed to get RunPod status: {str(e)}")

            response.update(
                {
                    "task_status": latest_task.status,
                    "progress": latest_task.progress,
                    "task_id": latest_task.task_id,
                    "created_at": latest_task.created_at.isoformat(),
                    "error_message": latest_task.error_message,
                    "runpod_status": runpod_status,
                }
            )

            if latest_task.result:
                # Create a copy and transform any S3 URLs or S3 keys within to presigned URLs
                task_result = latest_task.result.copy()

                def convert_s3_urls_to_presigned(data: Dict[str, Any] | Any):
                    def to_presigned_if_s3(value: str) -> str:
                        try:
                            # Case 1: Full S3 URL
                            if value.startswith("http") and "amazonaws.com" in value:
                                parsed_local = urlparse(value)
                                key_local = parsed_local.path.lstrip("/")
                                return s3_client.get_presigned_url(
                                    S3_BUCKET, key_local, timedelta(hours=1)
                                )
                            # Case 2: Plain S3 key (e.g., Vidflux-Assets/....)
                            if not value.startswith("http") and (
                                value.startswith("Vidflux-Assets/") or "/" in value
                            ):
                                return s3_client.get_presigned_url(
                                    S3_BUCKET, value, timedelta(hours=1)
                                )
                        except Exception as e:
                            logger.warning(f"Failed to presign value '{value}': {e}")
                        return value

                    if isinstance(data, dict):
                        for k, v in data.items():
                            if isinstance(v, str):
                                data[k] = to_presigned_if_s3(v)
                            elif isinstance(v, dict):
                                convert_s3_urls_to_presigned(v)
                            elif isinstance(v, list):
                                for idx, item in enumerate(v):
                                    if isinstance(item, str):
                                        v[idx] = to_presigned_if_s3(item)

                convert_s3_urls_to_presigned(task_result)
                response["task_result"] = task_result
        else:
            response["task_status"] = "no_active_task"

        # Match older behavior: stitched_video_url should refer to the rendered asset
        if rendered_video:
            try:

                s3_value = f"Vidflux-Assets/final-videos/final_video_{str(rendered_video.script_id)}.mp4"
                if s3_value.startswith("http") and "amazonaws.com" in s3_value:
                    parsed = urlparse(s3_value)
                    s3_key = parsed.path.lstrip("/")
                else:
                    s3_key = s3_value
                rendered_presigned = s3_client.get_presigned_url(
                    S3_BUCKET, s3_key, timedelta(hours=1)
                )
            except Exception as e:
                logger.warning(f"Failed to presign rendered video: {e}")
                rendered_presigned = rendered_video.s3_url
            response.update(
                {
                    "stitched_video_url": rendered_presigned,
                    "asset_id": rendered_video.asset_id,
                    "video_created_at": rendered_video.created_at.isoformat(),
                }
            )
        # Additionally expose the final mixed video URL at top-level for convenience
        if audio_mixed_video:
            try:
                s3_value = audio_mixed_video.s3_url
                if s3_value.startswith("http") and "amazonaws.com" in s3_value:
                    parsed = urlparse(s3_value)
                    s3_key = parsed.path.lstrip("/")
                else:
                    s3_key = s3_value
                final_presigned = s3_client.get_presigned_url(S3_BUCKET, s3_key, timedelta(hours=1))
            except Exception as e:
                logger.warning(f"Failed to presign audio_mixed video: {e}")
                final_presigned = audio_mixed_video.s3_url
            response.update({"final_mixed_video_url": final_presigned})

        return response

    except Exception as e:
        logger.error(f"Error checking status for script {script_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check status: {str(e)}")


@router.post("/webhook/{task_id}")
async def handle_runpod_webhook(
    task_id: str,
    webhook_data: Dict[str, Any],
    session: AsyncSession = Depends(get_database_session),
):
    """
    Handle webhook notifications from RunPod when jobs complete
    """
    try:
        logger.info(f"Received RunPod webhook for task {task_id}: {webhook_data}")

        # Find the task
        task_query = select(TaskQueue).where(TaskQueue.task_id == task_id)
        result = await session.execute(task_query)
        task = result.scalar_one_or_none()

        if not task:
            logger.warning(f"Task not found for webhook: {task_id}")
            return {"status": "task_not_found"}

        # Update task with webhook data
        runpod_status = webhook_data.get("status", "UNKNOWN")

        if runpod_status == "COMPLETED":
            task.status = "completed"
            task.progress = 100
            task.completed_at = datetime.utcnow()

            # Store the result if available
            if "output" in webhook_data:
                task.result = webhook_data["output"]

        elif runpod_status == "FAILED":
            task.status = "failed"
            error_info = webhook_data.get("output", {})
            if isinstance(error_info, dict) and "error" in error_info:
                task.error_message = error_info["error"]
            else:
                task.error_message = f"RunPod job failed: {runpod_status}"

        else:
            # Update progress if available
            execution_time = webhook_data.get("executionTime", 0)
            if execution_time > 0:
                # Estimate progress based on execution time (rough heuristic)
                estimated_progress = min(
                    90, int(execution_time / 1000)
                )  # 1 second = 1% progress, max 90%
                task.progress = max(task.progress, estimated_progress)

        await session.commit()

        logger.info(
            f"Updated task {task_id} from webhook: status={task.status}, progress={task.progress}"
        )

        return {"status": "updated"}

    except Exception as e:
        logger.error(f"Error handling webhook for task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Webhook processing failed: {str(e)}")


@router.post("/cancel/{task_id}")
async def cancel_stitching_task(
    task_id: str,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session),
):
    """
    Cancel a running stitching task
    """
    try:
        org_id, user_id, email = user_info

        # Find the task
        task_query = select(TaskQueue).where(
            and_(
                TaskQueue.task_id == task_id,
                TaskQueue.org_id == org_id,
                TaskQueue.user_id == user_id,
            )
        )
        result = await session.execute(task_query)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        if task.status in ["completed", "failed", "cancelled"]:
            return {
                "message": f"Task is already {task.status}",
                "task_id": task_id,
                "status": task.status,
            }

        # Cancel RunPod job if exists
        if task.result and task.result.get("runpod_job_id") and runpod_service.is_configured():
            try:
                await runpod_service.cancel_job(task.result["runpod_job_id"])
                logger.info(f"Cancelled RunPod job for task {task_id}")
            except Exception as e:
                logger.warning(f"Failed to cancel RunPod job: {str(e)}")

        # Update task status
        task.status = "cancelled"
        task.error_message = "Cancelled by user"
        await session.commit()

        return {"message": "Task cancelled successfully", "task_id": task_id, "status": "cancelled"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel task: {str(e)}")
