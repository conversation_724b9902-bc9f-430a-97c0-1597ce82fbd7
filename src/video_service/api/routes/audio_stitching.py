# Global imports
import os
import uuid
import glob
import requests
from loguru import logger
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import and_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends

# Local imports
from src.shared.utils.s3_client import S3Client
from src.shared.core.dependencies import get_database_session
from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer
from src.shared.models.database_models import VideoAsset, AudioAsset, Script, Scene

router = APIRouter(prefix="/audio-stitching", tags=["Audio Stitching"])

# Initialize the audio mixer
audio_mixer = EnhancedVideoAudioMixer(output_dir="output/final_videos")

# Initialize S3 client
S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)

@router.post("/mix-video-audio")
async def mix_video_audio(
    script_id: str,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    session: AsyncSession = Depends(get_database_session),
):
    """
    Mix audio with stitched video to create final video with background music and voiceover
    Automatically fetches background music and voiceover from database using script ID
    """
    # Get org_id from script if available, fallback to "default"
    org_id = "default"
    try:
        script_stmt = select(Script).where(Script.id == script_id)
        script_result = await session.execute(script_stmt)
        script = script_result.scalar_one_or_none()
        if script and script.org_id:
            org_id = script.org_id
    except Exception as e:
        logger.warning(f"Could not get org_id from script, using default: {e}")
    try:
        # Find the stitched video for this script
        # Look up stitched video in VideoAsset table
        stitched_video_asset = None
        from src.shared.models.database_models import VideoAsset

        stitched_stmt = (
            select(VideoAsset)
            .where(
                VideoAsset.script_id == script_id,
                VideoAsset.generation_method
                == "rendered",  # Changed from "audio_mixed" to "rendered"
                VideoAsset.deleted_at.is_(None),
            )
            .order_by(VideoAsset.created_at.desc())
            .limit(1)
        )
        stitched_result = await session.execute(stitched_stmt)
        stitched_video_asset = stitched_result.scalar_one_or_none()
        stitched_video_path = None
        if stitched_video_asset:
            # Download from S3 if not present locally
            s3_key = stitched_video_asset.s3_url
            local_dir = "output/stitched_videos"
            os.makedirs(local_dir, exist_ok=True)
            local_path = os.path.join(local_dir, os.path.basename(s3_key))
            logger.info(
                f"[AudioStitching] Looking for stitched video: s3_key={s3_key}, local_path={local_path}"
            )
            if not os.path.exists(local_path):
                logger.info(
                    f"[AudioStitching] Local file not found. Attempting to download from S3..."
                )
                try:
                    s3_client.download_file(S3_BUCKET, s3_key, local_path=local_path)
                    logger.info(f"[AudioStitching] Downloaded from S3: {local_path}")
                except Exception as e:
                    logger.error(
                        f"[AudioStitching] Failed to download from S3: bucket={S3_BUCKET}, key={s3_key}, error={e}"
                    )
            else:
                logger.info(f"[AudioStitching] Local file already exists: {local_path}")
            stitched_video_path = local_path
            # Extra check: does the file exist now?
            if not os.path.exists(stitched_video_path):
                logger.error(
                    f"[AudioStitching] File still not found after S3 download attempt: {stitched_video_path}"
                )
        if not stitched_video_path or not os.path.exists(stitched_video_path):
            logger.error(
                f"[AudioStitching] Returning 404: No stitched video found for script {script_id}. Path checked: {stitched_video_path}"
            )
            raise HTTPException(
                status_code=404,
                detail=f"No stitched video found for script {script_id}. Please generate a stitched video first.",
            )

        # Fetch ALL background music assets for this script (both AI-generated and user-uploaded)
        bg_music_stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.script_id == script_id,
                    AudioAsset.source_type.in_(
                        ["background_music", "user_uploaded"]
                    ),  # Include both types
                    AudioAsset.deleted_at.is_(None),
                )
            )
            .order_by(AudioAsset.created_at.desc())
        )
        bg_music_result = await session.execute(bg_music_stmt)
        bg_music_assets = bg_music_result.scalars().all()
        logger.info(
            f"[AudioStitching][DEBUG] Found {len(bg_music_assets)} background music assets for script {script_id}"
        )
        background_music_paths = []
        for asset in bg_music_assets:
            logger.info(
                f"[AudioStitching][DEBUG] BG Asset: s3_url={asset.s3_url}, script_id={asset.script_id}, source_type={asset.source_type}"
            )
            path = asset.local_path or asset.s3_url
            if path and not os.path.exists(path):
                # Handle different URL formats
                s3_key = None
                if path.startswith("http") and ".amazonaws.com/" in path:
                    # Full S3 URL or presigned URL - extract the S3 key
                    s3_key = path.split(".amazonaws.com/")[-1].split("?")[0]
                elif not path.startswith("http"):
                    # Already an S3 key
                    s3_key = path
                else:
                    # For user-uploaded files with presigned URLs that may have expired,
                    # we need to generate a new presigned URL for download
                    logger.warning(
                        f"[AudioStitching] Presigned URL may have expired, attempting direct S3 download"
                    )
                    continue

                if s3_key:
                    local_dir = "output/background_music"
                    os.makedirs(local_dir, exist_ok=True)
                    local_path = os.path.join(local_dir, os.path.basename(s3_key))
                    try:
                        # For user-uploaded files, generate a new presigned URL for download
                        if asset.source_type == "user_uploaded":
                            download_url = s3_client.get_presigned_url(
                                bucket=S3_BUCKET, key=s3_key, ttl=timedelta(hours=1)
                            )
                            logger.info(
                                f"[AudioStitching] Generated new presigned URL for user-uploaded music: {s3_key}"
                            )
                            # Download using requests (since it's a presigned URL)
                            response = requests.get(download_url, timeout=120)
                            response.raise_for_status()
                            with open(local_path, "wb") as f:
                                f.write(response.content)
                            logger.info(
                                f"[AudioStitching] Downloaded user-uploaded music: {local_path}"
                            )
                        else:
                            # Regular S3 download for AI-generated music
                            s3_client.download_file(S3_BUCKET, s3_key, local_path=local_path)
                            logger.info(
                                f"[AudioStitching] Downloaded AI-generated music: {local_path}"
                            )
                        path = local_path
                    except Exception as e:
                        logger.error(
                            f"Failed to download background music from S3: {s3_key}, error={e}"
                        )
                        continue
            if path and os.path.exists(path):
                background_music_paths.append(path)
        logger.info(f"[AudioStitching] Using background music files: {background_music_paths}")

        # Always create fresh combined voiceover (override existing ones)
        logger.info(f"[AudioStitching] Creating fresh combined voiceover from individual files...")

        # Get all individual voiceover files
        individual_voiceover_stmt = (
            select(AudioAsset)
            .where(
                and_(
                    AudioAsset.script_id == script_id,
                    AudioAsset.source_type == "voiceover",
                    AudioAsset.deleted_at.is_(None),
                )
            )
            .order_by(AudioAsset.created_at.asc())
        )  # Order by creation time to maintain sequence

        individual_voiceover_result = await session.execute(individual_voiceover_stmt)
        individual_voiceover_assets = individual_voiceover_result.scalars().all()

        voiceover_paths = []

        if individual_voiceover_assets:
            logger.info(
                f"[AudioStitching] Found {len(individual_voiceover_assets)} individual voiceover files to combine"
            )

            # Use the enhanced audio mixer to combine voiceovers
            from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer
            from moviepy import VideoFileClip
            import tempfile

            mixer = EnhancedVideoAudioMixer()

            # Get voiceover data
            voiceover_data = await mixer.get_voiceover_data_for_script(
                script_id=script_id, org_id=org_id, session=session
            )

            if voiceover_data:
                # Get video duration for proper timing
                total_video_duration = 60.0  # Default
                try:
                    with VideoFileClip(stitched_video_path) as video_clip:
                        total_video_duration = video_clip.duration
                    logger.info(f"[AudioStitching] Video duration: {total_video_duration:.2f}s")
                except Exception as e:
                    logger.warning(f"Could not get video duration, using default: {e}")

                # Combine voiceover files
                combined_voiceover_path = mixer.combine_voiceover_files(
                    voiceover_data=voiceover_data, total_video_duration=total_video_duration
                )

                # Save combined voiceover to S3 and database (this will override any existing ones)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                combined_s3_key = f"audio/voiceover/combined_voiceover_{script_id}_{timestamp}.mp3"

                s3_client.upload_file(
                    bucket=S3_BUCKET, key=combined_s3_key, file_path=combined_voiceover_path
                )
                logger.info(
                    f"[AudioStitching] Uploaded fresh combined voiceover to S3: {combined_s3_key}"
                )

                # Mark previous combined voiceovers as deleted (soft delete)
                try:
                    delete_stmt = (
                        update(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.script_id == script_id,
                                AudioAsset.source_type == "voiceover_combined",
                                AudioAsset.deleted_at.is_(None),
                            )
                        )
                        .values(deleted_at=datetime.utcnow())
                    )
                    await session.execute(delete_stmt)
                    logger.info(f"[AudioStitching] Marked previous combined voiceovers as deleted")
                except Exception as e:
                    logger.warning(f"Could not delete previous combined voiceovers: {e}")
                # Save new combined voiceover to database
                combined_audio_asset = AudioAsset(
                    org_id=org_id,
                    asset_id=str(uuid.uuid4()),
                    s3_url=combined_s3_key,
                    source_type="voiceover_combined",
                    script_id=script_id,
                    scene_id=None,
                    local_path=None,
                    generation_status="completed",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    deleted_at=None,
                )

                session.add(combined_audio_asset)
                await session.commit()

                # Use the combined voiceover for audio mixing
                voiceover_paths.append(combined_voiceover_path)
                logger.info(
                    f"[AudioStitching] Created and using fresh combined voiceover: {combined_voiceover_path}"
                )
            else:
                logger.warning(f"[AudioStitching] Could not get voiceover data for combining")
        else:
            logger.info(f"[AudioStitching] No individual voiceover files found")

        logger.info(f"[AudioStitching] Using voiceover files: {voiceover_paths}")

        # Generate unique output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"script_{script_id}_final_mixed_{timestamp}.mp4"
        # Log what we found
        logger.info(f"Audio stitching for script {script_id}:")
        logger.info(f"  - Stitched video: {stitched_video_path}")
        logger.info(f"  - Background music: {background_music_paths}")
        logger.info(f"  - Voiceover: {voiceover_paths}")
        logger.info(f"  - Output: {output_filename}")
        # Mix the audio (overlay strategy for both types)
        result = audio_mixer.mix_final_video_audio_enhanced(
            stitched_video_path=stitched_video_path,
            background_music_paths=background_music_paths,
            voiceover_paths=voiceover_paths,
            output_filename=output_filename,
            enable_ducking=enable_ducking,
            enable_ai_enhancement=enable_ai_enhancement,
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Audio mixing failed: {result.get('error', 'Unknown error')}",
            )

        # Save to database
        try:
            # Upload to S3: save final video under final-videos with deterministic name
            # s3_key = f"Vidflux-Assets/audio-stitching-assets/script_{script_id}/audio_{uuid.uuid4()}.mp4"
            s3_key = f"Vidflux-Assets/final-videos/final_video_{script_id}.mp4"
            s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=result["output_path"])
            video_asset = VideoAsset(
                asset_id=str(uuid.uuid4()),
                org_id=org_id,  # Persist resolved org_id
                s3_url=s3_key,  # Store the S3 key
                generation_method="audio_mixed",
                local_path=None,  # No local path since it's uploaded to S3
            )
            session.add(video_asset)
            await session.commit()
            logger.info("Successfully saved video asset to database")
            logger.info(f"   S3 URL: {s3_key}")

        except Exception as e:
            logger.error(f"Error saving to database: {e}")
            # Don't fail the request if database save fails

        if s3_key:
            # Ensure we have a raw key (not URL) to generate a presigned URL
            s3_key_for_presign = (
                s3_key.split(".amazonaws.com/")[-1]
                if (s3_key.startswith("http") and ".amazonaws.com/" in s3_key)
                else s3_key
            )
            presigned_url = s3_client.get_presigned_url(
                S3_BUCKET, s3_key_for_presign, timedelta(hours=1)
            )
        else:
            presigned_url = s3_key
        return {
            "final_video_key": s3_key,
            "presigned_final_video_url": presigned_url,
            "s3_url": presigned_url,  # backward compat
            "success": True,
            "message": "Audio mixing completed successfully",
            "filename": result["filename"],
            "duration": result["duration"],
            "file_size_mb": result["file_size_mb"],
            "audio_tracks_mixed": result["audio_tracks_mixed"],
            "audio_info": result["audio_info"],
            "enhancement_applied": result["enhancement_applied"],
            "quality_analysis": result["quality_analysis"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in audio mixing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Audio mixing failed: {str(e)}")
