#!/usr/bin/env python3
"""
Demo script to understand the agentic image flow
This shows exactly how the agents work together step by step
"""

import sys
sys.path.append('.')

def demo_traditional_vs_agentic():
    """Compare traditional vs agentic approaches"""
    print("🔄 TRADITIONAL APPROACH vs AGENTIC APPROACH")
    print("=" * 80)
    
    # Sample scenes
    scenes = [
        {"id": "1", "description": "Person waking up tired in bedroom"},
        {"id": "2", "description": "Coffee product on kitchen counter"},
        {"id": "3", "description": "Person drinking coffee happily"},
        {"id": "4", "description": "Coffee benefits and features"},
        {"id": "5", "description": "Energized person starting their day"}
    ]
    
    print("📝 INPUT SCENES:")
    for i, scene in enumerate(scenes, 1):
        print(f"   Scene {i}: {scene['description']}")
    
    print("\n" + "=" * 40 + " TRADITIONAL " + "=" * 40)
    print("❌ OLD WAY: One big prompt for everything")
    print("""
    prompt = f'''
    Generate image prompts for these 5 scenes: {scenes}
    Make them good for advertisements.
    '''
    
    Problems:
    - No character consistency across scenes
    - No intelligent balancing of image types  
    - Same generic approach for all scenes
    - Hard to debug or improve
    - No brand integration logic
    """)
    
    print("\n" + "=" * 40 + " AGENTIC " + "=" * 40)
    print("✅ NEW WAY: Specialized agents working together")
    
    # Simulate the agentic flow
    print("\n🎭 STEP 1: Character Profile Agent")
    print("   Analyzes ALL scenes to extract character info...")
    character_profile = "Professional adult, 30s, relatable appearance, business casual attire"
    print(f"   ✅ Result: '{character_profile}'")
    
    print("\n🎯 STEP 2: Flow Decider Agent (for each scene)")
    decisions = [
        {"scene": 1, "type": "CHARACTER", "weight": 0.8, "rationale": "Focus on person's tired state"},
        {"scene": 2, "type": "OBJECT", "weight": 0.7, "rationale": "Product showcase needed"},
        {"scene": 3, "type": "OBJECT_CHARACTER", "weight": 0.9, "rationale": "Person-product interaction"},
        {"scene": 4, "type": "OBJECT", "weight": 0.6, "rationale": "Product benefits focus"},
        {"scene": 5, "type": "CHARACTER", "weight": 0.8, "rationale": "Transformation result"}
    ]
    
    for decision in decisions:
        print(f"   Scene {decision['scene']}: {decision['type']} (weight: {decision['weight']})")
        print(f"      Rationale: {decision['rationale']}")
    
    print("\n✍️ STEP 3: Image Prompt Agent (for each scene)")
    print("   Generates specialized prompts using character profile + scene plan...")
    
    sample_prompts = [
        "Professional adult in bedroom, tired expression, morning lighting, lifestyle photography",
        "Premium coffee product on modern kitchen counter, commercial photography, brand colors",
        "Professional adult enjoying coffee, satisfied expression, warm lighting, lifestyle commercial",
        "Coffee product with benefit callouts, clean commercial photography, informative layout",
        "Energized professional adult, confident posture, bright lighting, transformation story"
    ]
    
    for i, prompt in enumerate(sample_prompts, 1):
        print(f"   Scene {i}: {prompt[:60]}...")
    
    print("\n📊 FINAL DISTRIBUTION:")
    distribution = {"CHARACTER": 2, "OBJECT": 2, "OBJECT_CHARACTER": 1}
    total = sum(distribution.values())
    for img_type, count in distribution.items():
        percentage = (count / total) * 100
        print(f"   {img_type}: {count}/{total} ({percentage:.1f}%)")

def demo_state_management():
    """Show how state management works"""
    print("\n\n🧠 STATE MANAGEMENT DEMO")
    print("=" * 80)
    
    print("State is like a shared notebook that all agents can read and write:")
    
    # Initial state
    print("\n📋 INITIAL STATE:")
    state = {
        "scenes": ["Scene 1", "Scene 2", "Scene 3"],
        "character_profile": "",
        "scene_plans": [],
        "image_prompts": [],
        "current_scene_index": 0,
        "type_distribution": {"character": 0, "object": 0, "mixed": 0}
    }
    
    for key, value in state.items():
        print(f"   {key}: {value}")
    
    print("\n🎭 After Character Agent:")
    state["character_profile"] = "Professional adult, authentic appearance"
    print(f"   character_profile: '{state['character_profile']}'")
    
    print("\n🎯 After Flow Decider (Scene 1):")
    state["scene_plans"].append({"scene": 1, "type": "CHARACTER"})
    state["type_distribution"]["character"] += 1
    print(f"   scene_plans: {state['scene_plans']}")
    print(f"   type_distribution: {state['type_distribution']}")
    
    print("\n✍️ After Prompt Agent (Scene 1):")
    state["image_prompts"].append({"scene": 1, "prompt": "Professional adult in scene..."})
    state["current_scene_index"] += 1
    print(f"   image_prompts: {state['image_prompts']}")
    print(f"   current_scene_index: {state['current_scene_index']}")
    
    print("\n💡 Key Benefits:")
    print("   ✅ Each agent builds on previous work")
    print("   ✅ Progress is tracked automatically") 
    print("   ✅ Easy to debug - can see state at any point")
    print("   ✅ Agents can make intelligent decisions based on context")

def demo_balancing_algorithm():
    """Show how the balancing algorithm works"""
    print("\n\n⚖️ INTELLIGENT BALANCING DEMO")
    print("=" * 80)
    
    # Target weights
    target_weights = {
        "character": 0.4,      # 40%
        "object": 0.3,         # 30%
        "mixed": 0.25,         # 25%
        "montage": 0.05        # 5%
    }
    
    print("🎯 TARGET DISTRIBUTION:")
    for img_type, weight in target_weights.items():
        print(f"   {img_type.title()}: {weight*100:.0f}%")
    
    print("\n🧮 BALANCING ALGORITHM IN ACTION:")
    
    # Simulate processing 5 scenes
    current_distribution = {"character": 0, "object": 0, "mixed": 0, "montage": 0}
    
    for scene_num in range(1, 6):
        print(f"\n   Scene {scene_num}:")
        total_processed = sum(current_distribution.values())
        
        # Calculate balance scores
        balance_scores = {}
        for img_type in target_weights:
            if total_processed == 0:
                score = 1.0
            else:
                current_ratio = current_distribution[img_type] / total_processed
                target_ratio = target_weights[img_type]
                
                if current_ratio < target_ratio:
                    score = 1.0 + (target_ratio - current_ratio)
                else:
                    score = max(0.1, 1.0 - (current_ratio - target_ratio))
            
            balance_scores[img_type] = score
        
        # Show scores
        print("      Balance scores:")
        for img_type, score in balance_scores.items():
            print(f"        {img_type}: {score:.2f}")
        
        # Pick highest scoring type (simplified)
        chosen_type = max(balance_scores, key=balance_scores.get)
        current_distribution[chosen_type] += 1
        
        print(f"      ✅ Chosen: {chosen_type}")
        print(f"      Current distribution: {current_distribution}")
    
    print("\n📊 FINAL RESULT:")
    total = sum(current_distribution.values())
    for img_type, count in current_distribution.items():
        actual_pct = (count / total) * 100
        target_pct = target_weights[img_type] * 100
        print(f"   {img_type.title()}: {count}/{total} ({actual_pct:.0f}%) - Target: {target_pct:.0f}%")

def main():
    """Run all demos"""
    print("🤖 AGENTIC IMAGE FLOW - INTERACTIVE DEMO")
    print("=" * 80)
    print("This demo shows how our agentic system works step by step")
    print("Perfect for understanding and explaining the concepts!")
    
    demo_traditional_vs_agentic()
    demo_state_management()
    demo_balancing_algorithm()
    
    print("\n\n🎉 DEMO COMPLETE!")
    print("=" * 80)
    print("Key Concepts Demonstrated:")
    print("✅ Specialized agents vs monolithic prompting")
    print("✅ State management for agent collaboration") 
    print("✅ Intelligent balancing algorithms")
    print("✅ Scalable and maintainable architecture")
    print("\nYou're now ready to explain agentic workflows! 🚀")

if __name__ == "__main__":
    main()
