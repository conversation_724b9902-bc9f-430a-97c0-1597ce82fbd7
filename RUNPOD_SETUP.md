# VidFlux RunPod Integration

This document explains how to set up and use RunPod for video stitching to offload heavy computational work from your local server.

## Overview

The VidFlux backend now supports two processing modes:

1. **Local Processing** - Video stitching happens on your server (original method)
2. **RunPod Processing** - Video stitching happens on RunPod's GPU infrastructure (new method)

## Why RunPod?

- **Performance**: RunPod provides high-performance GPU instances for faster video processing
- **Scalability**: Automatically scales based on demand
- **Cost-effective**: Pay only for actual processing time
- **Reliability**: Professional infrastructure with better uptime

## Setup Instructions

### Step 1: Prepare Your Docker Image

1. **Build the Docker image**:
   ```bash
   ./deploy-runpod.sh
   ```
   Or manually:
   ```bash
   docker build --platform linux/amd64 --tag your-username/vidflux-runpod --file Dockerfile.runpod .
   docker push your-username/vidflux-runpod
   ```

### Step 2: Create RunPod Endpoint

1. Go to [RunPod Console](https://www.runpod.io/console/serverless)
2. Click "New Endpoint"
3. Select "Docker Image" as source
4. Enter your image URL: `docker.io/your-username/vidflux-runpod:latest`
5. Choose worker configuration:
   - **GPU**: 16GB+ recommended (RTX 4090, A100, etc.)
   - **CPU**: 4+ cores
   - **RAM**: 16GB+
   - **Storage**: 20GB+ for temporary files

### Step 3: Configure Environment Variables in RunPod

Set these environment variables in your RunPod endpoint:

```bash
# Database connection
DATABASE_URL=postgresql+asyncpg://username:password@host:port/database

# AWS S3 Configuration
AWS_S3_BUCKET=your-bucket-name
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-2

# Optional: Logging level
LOG_LEVEL=INFO
```

### Step 4: Configure Your FastAPI Backend

Add these environment variables to your FastAPI backend:

```bash
# RunPod Configuration
RUNPOD_API_KEY=your-runpod-api-key
RUNPOD_ENDPOINT_ID=your-runpod-endpoint-id

# Your API base URL (for webhooks)
API_BASE_URL=https://your-api-domain.com
```

## Usage

### API Endpoints

#### 1. Start Video Stitching

```http
POST /video/stitching/stitch
```

Parameters:
- `script_id` (required): Script ID to process
- `auto_audio_stitch` (default: true): Include audio mixing
- `enable_ducking` (default: true): Enable audio ducking
- `enable_ai_enhancement` (default: false): AI audio enhancement
- `use_runpod` (default: true): Use RunPod for processing

Example:
```bash
curl -X POST "https://your-api.com/video/stitching/stitch" \
  -H "Authorization: Bearer your-token" \
  -d "script_id=your-script-id&auto_audio_stitch=true&use_runpod=true"
```

Response:
```json
{
  "id": "task-uuid",
  "task_id": "task-uuid",
  "task_type": "video_stitching_with_audio",
  "org_id": "your-org",
  "user_id": "your-user-id",
  "status": "processing",
  "progress": 0,
  "created_at": "2025-08-12T20:00:00Z"
}
```

#### 2. Check Status

```http
GET /video/stitching/status/{script_id}
```

Example:
```bash
curl "https://your-api.com/video/stitching/status/your-script-id" \
  -H "Authorization: Bearer your-token"
```

Response:
```json
{
  "script_id": "your-script-id",
  "has_completed_video": false,
  "task_status": "processing",
  "progress": 45,
  "task_id": "task-uuid",
  "created_at": "2025-08-12T20:00:00Z",
  "runpod_status": {
    "id": "runpod-job-id",
    "status": "IN_PROGRESS",
    "executionTime": 120000
  }
}
```

#### 3. Cancel Task

```http
POST /video/stitching/cancel/{task_id}
```

### Processing Flow

1. **Submit Request**: Client calls `/stitch` endpoint
2. **Create Task**: Backend creates task in database
3. **Submit to RunPod**: Task is sent to RunPod endpoint
4. **Processing**: RunPod processes video stitching and audio mixing
5. **Webhook Update**: RunPod sends completion webhook
6. **Status Polling**: Client polls for status updates
7. **Download Result**: Final video is available in S3

## Monitoring and Debugging

### Check Service Status

```http
GET /video/stitching/status
```

This returns information about RunPod configuration:
```json
{
  "service": "Video Stitching",
  "status": "running",
  "version": "3.0.0 (RunPod)",
  "runpod_configured": true,
  "processing_mode": "runpod"
}
```

### Task Status Values

- `pending`: Task created, waiting to be processed
- `processing`: Currently being processed by RunPod
- `completed`: Successfully completed
- `failed`: Processing failed
- `cancelled`: Cancelled by user

### Common Issues

#### 1. RunPod Not Configured
**Error**: "RunPod processing not configured"
**Solution**: Set `RUNPOD_API_KEY` and `RUNPOD_ENDPOINT_ID` environment variables

#### 2. RunPod Job Fails
**Check**: Task error message in status response
**Common causes**:
- Database connection issues
- S3 access problems
- Missing video files
- Insufficient GPU memory

#### 3. Webhook Not Received
**Check**: 
- `API_BASE_URL` is correct and accessible
- Firewall allows incoming webhooks
- Endpoint `/video/stitching/webhook/{task_id}` is accessible

## Cost Estimation

RunPod pricing example (varies by GPU type):
- **RTX 4090**: ~$0.50/hour
- **A100 80GB**: ~$2.00/hour

Typical video stitching jobs:
- **Short videos** (5 scenes): 2-5 minutes = $0.02-0.10
- **Medium videos** (10 scenes): 5-10 minutes = $0.05-0.20
- **Long videos** (20+ scenes): 10-20 minutes = $0.10-0.40

## Fallback Behavior

If RunPod is not configured or fails:
1. Backend returns error with explanation
2. Local processing is NOT automatically used (for safety)
3. User must explicitly disable `use_runpod=false` for local processing

## Testing

### Test RunPod Handler Locally

```bash
# Set environment variables
export DATABASE_URL="postgresql+asyncpg://..."
export AWS_S3_BUCKET="your-bucket"
# ... other AWS vars

# Run handler
python runpod_handler.py
```

### Test with Sample Input

The handler will use `test_input.json` for local testing:

```json
{
  "input": {
    "task_id": "test-task-12345",
    "script_id": "your-script-id",
    "org_id": "your-org-id",
    "enable_ducking": true,
    "enable_ai_enhancement": false,
    "auto_audio_stitch": true
  }
}
```

## Migration from Local Processing

1. **Deploy RunPod endpoint** (following setup instructions)
2. **Test with sample scripts** to ensure everything works
3. **Update environment variables** in production
4. **Monitor first few jobs** to ensure smooth operation
5. **Optional**: Keep local processing as fallback

## Support

For issues with:
- **VidFlux integration**: Check your backend logs
- **RunPod platform**: Check RunPod console logs
- **Docker build**: Ensure all dependencies are included
- **Performance**: Consider upgrading GPU tier or optimizing video sizes
