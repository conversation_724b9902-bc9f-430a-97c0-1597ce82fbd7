# Global imports
import os
import asyncio
from fastapi import FastAPI
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from starlette.requests import Request
from starlette.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

# Local imports
from src.shared.config.settings import settings
from src.auth_service.api.routes.auth import router as auth_router
from src.audio_service.api.routes.audio import router as audio_router
from src.image_service.api.routes.images import router as images_router
from src.video_service.api.routes.videos import router as videos_router
from src.script_service.api.routes.generation import router as generation_router

# from src.script_service.api.routes.scenes import router as scenes_router
from src.shared.core.middleware import RequestLoggingMiddleware, ErrorHandlingMiddleware
from src.video_service.api.routes.video_stitching import router as video_stitching_router
from src.video_service.api.routes.audio_stitching import router as audio_stitching_router
from src.text_overlay_service.api.routes.text_overlay import router as text_overlay_router
from src.image_overlay_service.api.routes.image_overlay import router as image_overlay_router
from dotenv import load_dotenv

load_dotenv()

# Ensure output directories exist
os.makedirs("output/audio", exist_ok=True)
os.makedirs("output/background_music", exist_ok=True)
os.makedirs("output/background_sounds", exist_ok=True)
os.makedirs("output/images", exist_ok=True)
os.makedirs("output/stitched_videos", exist_ok=True)
os.makedirs("output/final_videos", exist_ok=True)


class TimeoutMiddleware(BaseHTTPMiddleware):
    """Middleware to handle long-running requests with extended timeout"""

    async def dispatch(self, request: Request, call_next):
        # Set timeout to 17 minutes (1020 seconds) for video generation
        timeout_seconds = 1020

        # Check if this is a video generation request
        if request.url.path.startswith("/video/generate"):
            # Use asyncio.wait_for with extended timeout
            try:
                response = await asyncio.wait_for(call_next(request), timeout=timeout_seconds)
                return response
            except asyncio.TimeoutError:
                return Response(
                    content="Request timed out after 17 minutes",
                    status_code=408,
                    media_type="text/plain",
                )
        else:
            # For other requests, use normal timeout
            return await call_next(request)


app = FastAPI(
    title="VidFlux API",
    version="2.0.0",
    description="Complete video generation platform API with authentication, script management, image generation, video generation, audio generation, and stitching capabilities.",
    docs_url="/docs",
    redoc_url="/redoc",
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global middleware
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(ErrorHandlingMiddleware)
app.add_middleware(TimeoutMiddleware)


@app.get("/images/{scene_id}/{image_name}")
def serve_image(scene_id: str, image_name: str):
    import glob

    image_path = f"output/images/scene_{scene_id}/{image_name}"
    if os.path.exists(image_path):
        return FileResponse(image_path, media_type="image/jpeg")
    # Fallback: serve the first available image in the folder
    folder = f"output/images/scene_{scene_id}"
    images = (
        glob.glob(os.path.join(folder, "*.jpg"))
        + glob.glob(os.path.join(folder, "*.jpeg"))
        + glob.glob(os.path.join(folder, "*.png"))
    )
    if images:
        return FileResponse(images[0], media_type="image/jpeg")
    return {"error": f"No images found for scene {scene_id}"}


# Mount static files
app.mount("/static/audio", StaticFiles(directory="output/audio"), name="audio")
app.mount(
    "/static/background_music",
    StaticFiles(directory="output/background_music"),
    name="background_music",
)
app.mount(
    "/static/background_sounds",
    StaticFiles(directory="output/background_sounds"),
    name="background_sounds",
)
app.mount(
    "/static/stitched_videos",
    StaticFiles(directory="output/stitched_videos"),
    name="stitched_videos",
)
app.mount("/static/final_videos", StaticFiles(directory="output/final_videos"), name="final_videos")
app.mount("/images", StaticFiles(directory="output/images"), name="images")

# Include routers in logical order
# 1. Authentication
app.include_router(auth_router, prefix="/auth")

# 2. Script and Scene Generation Generator (Unified)
app.include_router(generation_router, prefix="/generation")

# # 2b. Scene Management
# app.include_router(scenes_router, prefix="/scenes")

# 3. Image Generation
app.include_router(images_router, prefix="/image")

# 4. Video Generation
app.include_router(videos_router, prefix="/video")

# 5. Audio Generation
app.include_router(audio_router, prefix="/audio")

# 6. Video Stitching
app.include_router(video_stitching_router, prefix="/video")

# 7. Audio Stitching
app.include_router(audio_stitching_router, prefix="/video")

# 8. Image Overlay
app.include_router(image_overlay_router, prefix="/image-overlay")

# 9. Text Overlay
app.include_router(text_overlay_router, prefix="/text-overlay")


@app.get("/")
def root():
    return {"service": "VidFlux Unified API", "status": "running"}
