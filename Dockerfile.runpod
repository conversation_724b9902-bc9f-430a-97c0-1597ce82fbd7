# Use Python 3.10 slim as base image
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies required for video processing
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libavcodec-extra \
    git \
    curl \
    build-essential \
    pkg-config \
    libhdf5-dev \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire source code
COPY src/ ./src/
COPY runpod_handler.py .

# Create necessary directories
RUN mkdir -p /tmp/videos /tmp/audio /tmp/stitched_videos /tmp/final_videos

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Start the container
CMD ["python3", "-u", "runpod_handler.py"]
