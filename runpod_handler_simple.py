"""
Simplified RunPod Serverless Handler for VidFlux Video Stitching
Handles both video stitching and audio mixing operations
"""

import runpod
import os
import sys
import asyncio
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional

# Add the src directory to Python path for imports
sys.path.append("/app/src")

# Set minimal environment defaults
os.environ.setdefault("DEBUG", "False")
os.environ.setdefault("APP_NAME", "VidFlux RunPod Worker")
os.environ.setdefault("APP_VERSION", "1.0.0")

try:
    # Import required modules
    from src.video_service.services.video_stitcher import VideoStitcher
    from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer
    from src.shared.utils.s3_client import S3Client
    from src.shared.models.database_models import VideoAsset, Scene, AudioAsset, TaskQueue, Script
    from src.shared.config.database import db_manager
    from sqlalchemy import and_, select, desc

    print("✅ All imports successful")

except Exception as e:
    print(f"❌ Import error: {str(e)}")
    print(f"Traceback: {traceback.format_exc()}")
    raise

# Initialize services
video_stitcher = VideoStitcher()
audio_mixer = EnhancedVideoAudioMixer(output_dir="/tmp/final_videos")
s3_client = S3Client(os.getenv("AWS_DEFAULT_REGION", "us-east-2"))


async def update_task_in_db(
    task_id: str, progress: int, status: str = None, error_message: str = None, result: dict = None
):
    """Update task progress in database"""
    try:
        async for session in db_manager.get_session():
            query = select(TaskQueue).where(TaskQueue.task_id == task_id)
            db_result = await session.execute(query)
            task = db_result.scalar_one_or_none()

            if task:
                task.progress = progress
                if status:
                    task.status = status
                if error_message:
                    task.error_message = error_message
                if result:
                    task.result = result
                if status == "completed":
                    task.completed_at = datetime.utcnow()
                elif status == "processing" and not task.started_at:
                    task.started_at = datetime.utcnow()

                await session.commit()
                print(f"✅ Updated task {task_id}: progress={progress}, status={status}")
            break
    except Exception as e:
        print(f"❌ Failed to update task progress: {str(e)}")


async def process_video_stitching(
    script_id: str,
    org_id: str,
    task_id: str,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    auto_audio_stitch: bool = True,
) -> Dict[str, Any]:
    """Main video stitching process"""

    try:
        print(f"🎬 Starting video stitching for script: {script_id}")

        # Update task status
        await update_task_in_db(task_id, 10, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Initializing video stitching...")

        video_paths = []
        scenes_data = []
        s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")

        # Get data from database
        async for session in db_manager.get_session():
            # Get script
            script_query = select(Script).where(
                and_(Script.id == script_id, Script.org_id == org_id)
            )
            script_result = await session.execute(script_query)
            script = script_result.scalar_one_or_none()

            if not script:
                raise Exception(f"Script {script_id} not found")

            # Get scenes
            scenes_query = (
                select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
            )
            scenes_result = await session.execute(scenes_query)
            scenes = scenes_result.scalars().all()

            if not scenes:
                raise Exception("No scenes found for script")

            print(f"📋 Found {len(scenes)} scenes to process")
            await update_task_in_db(task_id, 20, "processing")
            runpod.serverless.progress_update({"id": task_id}, f"Found {len(scenes)} scenes")

            # Download video files
            for i, scene in enumerate(scenes):
                video_query = (
                    select(VideoAsset)
                    .where(
                        and_(
                            VideoAsset.scene_id == scene.id,
                            VideoAsset.org_id == org_id,
                            VideoAsset.deleted_at.is_(None),
                        )
                    )
                    .order_by(desc(VideoAsset.created_at))
                )

                video_result = await session.execute(video_query)
                video_asset = video_result.scalar_one_or_none()

                if not video_asset or not video_asset.s3_url:
                    raise Exception(f"No video found for scene {scene.scene_number}")

                # Download video file
                local_path = f"/tmp/videos/scene_{scene.scene_number}_{scene.id}.mp4"
                os.makedirs(os.path.dirname(local_path), exist_ok=True)

                # Extract S3 key
                key = video_asset.s3_url.split("amazonaws.com/")[-1]

                print(f"📥 Downloading video {i+1}/{len(scenes)}: {key}")
                s3_client.download_file(s3_bucket, key, local_path)
                video_paths.append(local_path)
                scenes_data.append(scene)

                # Update progress
                progress = 20 + (i + 1) * 30 // len(scenes)
                await update_task_in_db(task_id, progress, "processing")
                runpod.serverless.progress_update(
                    {"id": task_id}, f"Downloaded {i+1}/{len(scenes)} videos"
                )

            break  # Exit session

        # Step 2: Video stitching
        print("🎞️ Starting video stitching...")
        await update_task_in_db(task_id, 50, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Stitching videos together...")

        output_filename = f"stitched_video_{script_id}_{int(datetime.now().timestamp())}.mp4"
        output_path = f"/tmp/stitched_videos/{output_filename}"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        stitched_path = video_stitcher.stitch_videos(video_paths, output_path)
        print(f"✅ Video stitching completed: {stitched_path}")

        # Upload stitched video
        s3_key = f"Vidflux-Assets/stitched-videos/{output_filename}"
        s3_url = s3_client.upload_file(stitched_path, s3_bucket, s3_key)

        await update_task_in_db(task_id, 60, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Uploaded stitched video")

        # Save to database
        async for session in db_manager.get_session():
            video_asset = VideoAsset(
                org_id=org_id,
                s3_url=s3_url,
                generation_method="rendered",
                script_id=script_id,
                local_path=None,
            )
            session.add(video_asset)
            await session.commit()
            break

        result_data = {
            "video_stitching": {
                "status": "completed",
                "video_url": s3_url,
                "scenes_count": len(scenes_data),
            }
        }

        # Step 3: Audio stitching (if enabled)
        if auto_audio_stitch:
            print("🎵 Starting audio stitching...")
            await update_task_in_db(task_id, 70, "processing")
            runpod.serverless.progress_update({"id": task_id}, "Processing audio...")

            # Get voiceover files
            voiceover_files = []
            async for session in db_manager.get_session():
                for scene in scenes_data:
                    audio_query = (
                        select(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.org_id == org_id,
                                AudioAsset.scene_id == scene.id,
                                AudioAsset.source_type == "voiceover",
                            )
                        )
                        .order_by(desc(AudioAsset.created_at))
                    )

                    audio_result = await session.execute(audio_query)
                    audio_asset = audio_result.scalar_one_or_none()

                    if audio_asset and audio_asset.s3_url:
                        local_audio_path = f"/tmp/audio/voiceover_{scene.id}.mp3"
                        os.makedirs(os.path.dirname(local_audio_path), exist_ok=True)

                        audio_key = audio_asset.s3_url.split("amazonaws.com/")[-1]
                        s3_client.download_file(s3_bucket, audio_key, local_audio_path)
                        voiceover_files.append(local_audio_path)
                break

            print(f"🎤 Found {len(voiceover_files)} voiceover files")
            await update_task_in_db(task_id, 80, "processing")

            # Mix audio
            final_filename = f"final_video_{script_id}_{int(datetime.now().timestamp())}.mp4"
            final_path = f"/tmp/final_videos/{final_filename}"
            os.makedirs(os.path.dirname(final_path), exist_ok=True)

            if voiceover_files:
                final_video_path = audio_mixer.mix_video_with_audio(
                    video_path=stitched_path,
                    voiceover_files=voiceover_files,
                    output_path=final_path,
                    enable_ducking=enable_ducking,
                    enable_ai_enhancement=enable_ai_enhancement,
                )
            else:
                import shutil

                shutil.copy2(stitched_path, final_path)
                final_video_path = final_path

            # Upload final video
            final_s3_key = f"Vidflux-Assets/final-videos/{final_filename}"
            final_s3_url = s3_client.upload_file(final_video_path, s3_bucket, final_s3_key)

            # Update database
            async for session in db_manager.get_session():
                video_query = (
                    select(VideoAsset)
                    .where(
                        and_(
                            VideoAsset.script_id == script_id,
                            VideoAsset.generation_method == "rendered",
                            VideoAsset.org_id == org_id,
                        )
                    )
                    .order_by(desc(VideoAsset.created_at))
                )

                video_result = await session.execute(video_query)
                video_asset = video_result.scalar_one_or_none()

                if video_asset:
                    video_asset.s3_url = final_s3_url
                    await session.commit()
                break

            result_data["audio_stitching"] = {
                "status": "completed",
                "final_video_url": final_s3_url,
                "voiceover_count": len(voiceover_files),
            }

            print("✅ Audio stitching completed")
        else:
            result_data["audio_stitching"] = {"status": "skipped"}

        # Final update
        await update_task_in_db(task_id, 100, "completed", result=result_data)

        # Cleanup
        cleanup_files = video_paths + [stitched_path]
        if auto_audio_stitch:
            cleanup_files.extend(voiceover_files)
            if "final_video_path" in locals():
                cleanup_files.append(final_video_path)

        for temp_file in cleanup_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass

        print("🎉 Processing completed successfully!")
        return {"status": "completed", "script_id": script_id, "task_id": task_id, **result_data}

    except Exception as e:
        error_msg = f"Processing failed: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")

        await update_task_in_db(task_id, 0, "failed", error_message=error_msg)
        raise Exception(error_msg)


def handler(job):
    """RunPod serverless handler"""
    try:
        print("🚀 VidFlux RunPod Handler Started")

        job_input = job["input"]

        # Extract parameters
        task_id = job_input.get("task_id")
        script_id = job_input.get("script_id")
        org_id = job_input.get("org_id")
        enable_ducking = job_input.get("enable_ducking", True)
        enable_ai_enhancement = job_input.get("enable_ai_enhancement", False)
        auto_audio_stitch = job_input.get("auto_audio_stitch", True)

        # Validate inputs
        if not all([task_id, script_id, org_id]):
            raise ValueError("Missing required parameters: task_id, script_id, org_id")

        print(f"📋 Processing - Task: {task_id}, Script: {script_id}")

        # Run the processing
        result = asyncio.run(
            process_video_stitching(
                script_id=script_id,
                org_id=org_id,
                task_id=task_id,
                enable_ducking=enable_ducking,
                enable_ai_enhancement=enable_ai_enhancement,
                auto_audio_stitch=auto_audio_stitch,
            )
        )

        print("✅ Handler completed successfully")
        return result

    except Exception as e:
        error_msg = f"Handler error: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")

        return {"error": error_msg, "status": "failed"}


# Start the serverless function
if __name__ == "__main__":
    print("🎬 Starting VidFlux RunPod Serverless Worker...")
    runpod.serverless.start({"handler": handler})
