-- VidFlux Backend: PostgreSQL Schema Initialization
-- Idempotent: Drops and recreates all types and tables

-- 1. <PERSON><PERSON><PERSON> Types
DROP TYPE IF EXISTS asset_type_enum CASCADE;
DROP TYPE IF EXISTS audio_source_enum CASCADE;
DROP TYPE IF EXISTS text_type_enum CASCADE;
DROP TYPE IF EXISTS video_gen_method_enum CASCADE;
DROP TYPE IF EXISTS image_gen_status_enum CASCADE;
DROP TYPE IF EXISTS video_gen_status_enum CASCADE;

CREATE TYPE asset_type_enum AS ENUM ('audio', 'image', 'text', 'video');
CREATE TYPE audio_source_enum AS ENUM ('background_music', 'user_uploaded', 'background_sound', 'voiceover');
CREATE TYPE text_type_enum AS ENUM ('caption', 'prompt', 'script', 'scene');
CREATE TYPE video_gen_method_enum AS ENUM ('ttv', 'rendered', 'audio_mixed');
CREATE TYPE image_gen_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed');
CREATE TYPE video_gen_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed');

-- 2. Tables
-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS refresh_tokens CASCADE;
DROP TABLE IF EXISTS video_generations CASCADE;
DROP TABLE IF EXISTS image_generations CASCADE;
DROP TABLE IF EXISTS task_queue CASCADE;
DROP TABLE IF EXISTS scenes CASCADE;
DROP TABLE IF EXISTS scripts CASCADE;
DROP TABLE IF EXISTS video_assets CASCADE;
DROP TABLE IF EXISTS text_assets CASCADE;
DROP TABLE IF EXISTS image_assets CASCADE;
DROP TABLE IF EXISTS audio_assets CASCADE;
DROP TABLE IF EXISTS project_assets CASCADE;
DROP TABLE IF EXISTS projects CASCADE;
DROP TABLE IF EXISTS campaigns CASCADE;
DROP TABLE IF EXISTS user_signins CASCADE;
DROP TABLE IF EXISTS pending_users CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;

-- Create tables in dependency order
-- 1. organizations
CREATE TABLE organizations (
    org_id VARCHAR PRIMARY KEY,
    org_name VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 2. users
CREATE TABLE users (
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    user_email VARCHAR UNIQUE NOT NULL,
    username VARCHAR,
    first_name VARCHAR,
    last_name VARCHAR,
    password_hash VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    PRIMARY KEY (org_id, user_id),
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE
);

-- 3. pending_users
CREATE TABLE pending_users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    password_hash VARCHAR NOT NULL,
    first_name VARCHAR NOT NULL,
    last_name VARCHAR NOT NULL,
    org_name VARCHAR NOT NULL,
    otp_code VARCHAR,
    otp_expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. user_signins
CREATE TABLE user_signins (
    id UUID PRIMARY KEY,
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (org_id, user_id) REFERENCES users(org_id, user_id) ON DELETE CASCADE
);

-- 5. campaigns
CREATE TABLE campaigns (
    org_id VARCHAR NOT NULL,
    campaign_id UUID NOT NULL,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    PRIMARY KEY (org_id, campaign_id),
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE
);

-- 6. projects
CREATE TABLE projects (
    org_id VARCHAR NOT NULL,
    campaign_id UUID NOT NULL,
    project_id UUID NOT NULL,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    PRIMARY KEY (org_id, campaign_id, project_id),
    FOREIGN KEY (org_id, campaign_id) REFERENCES campaigns(org_id, campaign_id) ON DELETE CASCADE
);

-- 7. project_assets
CREATE TABLE project_assets (
    project_id UUID NOT NULL,
    asset_id VARCHAR NOT NULL,
    asset_type asset_type_enum NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (project_id, asset_id)
);

-- 8. text_assets
CREATE TABLE text_assets (
    org_id VARCHAR NOT NULL,
    asset_id VARCHAR PRIMARY KEY,
    content TEXT,
    s3_url VARCHAR,
    type text_type_enum NOT NULL,
    extra_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE
);

-- 9. scripts
CREATE TABLE scripts (
    id UUID PRIMARY KEY,
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    project_id UUID,
    text_asset_id VARCHAR,
    title VARCHAR NOT NULL,
    video_style VARCHAR NOT NULL,
    duration VARCHAR NOT NULL,
    aspect_ratio VARCHAR NOT NULL DEFAULT '16:9',
    narration_type VARCHAR NOT NULL DEFAULT 'voice-based',
    brand_name VARCHAR,
    product_name VARCHAR,
    brand_description TEXT,
    status VARCHAR DEFAULT 'pending',
    generation_status VARCHAR DEFAULT 'queued',
    error_message TEXT,
    extra_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (org_id, user_id) REFERENCES users(org_id, user_id),
    FOREIGN KEY (text_asset_id) REFERENCES text_assets(asset_id)
);

-- 10. scenes
CREATE TABLE scenes (
    id UUID PRIMARY KEY,
    script_id UUID NOT NULL,
    text_asset_id VARCHAR,
    scene_number INTEGER NOT NULL,
    title VARCHAR,
    description TEXT,
    visual_description TEXT NOT NULL,
    narration TEXT NOT NULL,
    duration VARCHAR DEFAULT '5s',
    location_group INTEGER,
    status VARCHAR DEFAULT 'active',
    generation_status VARCHAR DEFAULT 'completed',
    error_message TEXT,
    character_info JSONB,
    extra_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    regenerated_at TIMESTAMP,
    FOREIGN KEY (script_id) REFERENCES scripts(id) ON DELETE CASCADE,
    FOREIGN KEY (text_asset_id) REFERENCES text_assets(asset_id)
);

-- 11. audio_assets
CREATE TABLE audio_assets (
    asset_id VARCHAR PRIMARY KEY,
    org_id VARCHAR NOT NULL,
    s3_url VARCHAR NOT NULL,
    source_type audio_source_enum NOT NULL,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    deleted_at TIMESTAMP,
    version INTEGER DEFAULT 1,
    local_path VARCHAR,
    scene_id UUID,
    script_id UUID,
    is_latest BOOLEAN DEFAULT true,
    generation_metadata JSONB,
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE,
    FOREIGN KEY (scene_id) REFERENCES scenes(id),
    FOREIGN KEY (script_id) REFERENCES scripts(id)
);

-- 12. image_assets
CREATE TABLE image_assets (
    asset_id VARCHAR PRIMARY KEY,
    org_id VARCHAR NOT NULL,
    s3_url VARCHAR NOT NULL,
    generation_id UUID,
    scene_id UUID,
    script_id UUID,
    is_selected BOOLEAN DEFAULT false,
    status VARCHAR DEFAULT 'active',
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    deleted_at TIMESTAMP,
    local_path VARCHAR,
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE,
    FOREIGN KEY (scene_id) REFERENCES scenes(id),
    FOREIGN KEY (script_id) REFERENCES scripts(id)
);

-- 13. video_assets
CREATE TABLE video_assets (
    org_id VARCHAR NOT NULL,
    asset_id VARCHAR PRIMARY KEY,
    s3_url VARCHAR NOT NULL,
    generation_method video_gen_method_enum NOT NULL,
    scene_id UUID,
    local_path VARCHAR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    FOREIGN KEY (org_id) REFERENCES organizations(org_id) ON DELETE CASCADE,
    FOREIGN KEY (scene_id) REFERENCES scenes(id)
);

-- 14. task_queue
CREATE TABLE task_queue (
    id UUID PRIMARY KEY,
    task_id VARCHAR UNIQUE NOT NULL,
    task_type VARCHAR NOT NULL,
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    related_script_id UUID,
    related_scene_id UUID,
    input_data JSONB NOT NULL,
    status VARCHAR DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    result JSONB,
    error_message TEXT,
    queue_name VARCHAR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (org_id, user_id) REFERENCES users(org_id, user_id),
    FOREIGN KEY (related_script_id) REFERENCES scripts(id),
    FOREIGN KEY (related_scene_id) REFERENCES scenes(id)
);

-- 15. image_generations
CREATE TABLE image_generations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
    script_id UUID NOT NULL REFERENCES scripts(id) ON DELETE CASCADE,
    aspect_ratio VARCHAR NOT NULL DEFAULT '16:9',
    include_brand BOOLEAN DEFAULT TRUE,
    status image_gen_status_enum DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    error_message TEXT,
    result JSONB,
    is_regeneration BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    CONSTRAINT fk_image_generations_user FOREIGN KEY (org_id, user_id) REFERENCES users(org_id, user_id)
);

-- 16. video_generations
CREATE TABLE video_generations (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
    script_id UUID NOT NULL REFERENCES scripts(id) ON DELETE CASCADE,
    aspect_ratio VARCHAR NOT NULL DEFAULT '16:9',
    duration VARCHAR NOT NULL DEFAULT '5',
    status VARCHAR DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    video_url TEXT,
    local_path TEXT,
    error_message TEXT,
    generation_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    CONSTRAINT fk_video_generations_user FOREIGN KEY (org_id, user_id) REFERENCES users(org_id, user_id)
);

-- 17. refresh_tokens
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY,
    org_id VARCHAR NOT NULL,
    user_id UUID NOT NULL,
    token VARCHAR NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    revoked BOOLEAN DEFAULT FALSE,
    last_used_at TIMESTAMP,
    FOREIGN KEY (org_id, user_id) REFERENCES users(org_id, user_id) ON DELETE CASCADE
);

-- 3. Indexes for performance (from migrations)
CREATE INDEX IF NOT EXISTS idx_image_generations_org_user ON image_generations(org_id, user_id);
CREATE INDEX IF NOT EXISTS idx_image_generations_scene ON image_generations(scene_id);
CREATE INDEX IF NOT EXISTS idx_image_generations_status ON image_generations(status);
CREATE INDEX IF NOT EXISTS idx_image_assets_generation ON image_assets(generation_id);
CREATE INDEX IF NOT EXISTS idx_image_assets_scene ON image_assets(scene_id);
CREATE INDEX IF NOT EXISTS idx_image_assets_selected ON image_assets(is_selected) WHERE is_selected = TRUE;
CREATE INDEX IF NOT EXISTS idx_video_generations_org_user ON video_generations(org_id, user_id);
CREATE INDEX IF NOT EXISTS idx_video_generations_scene ON video_generations(scene_id);
CREATE INDEX IF NOT EXISTS idx_video_generations_script ON video_generations(script_id);
CREATE INDEX IF NOT EXISTS idx_video_generations_status ON video_generations(status);
CREATE INDEX IF NOT EXISTS idx_video_generations_created ON video_generations(created_at);
CREATE INDEX IF NOT EXISTS idx_audio_assets_latest ON audio_assets (org_id, scene_id, script_id, is_latest) WHERE is_latest = true;
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens (token);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user ON refresh_tokens (org_id, user_id); 