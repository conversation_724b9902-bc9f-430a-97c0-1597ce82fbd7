# Text Overlay Service API Documentation

## Overview

The Text Overlay Service handles the application of text overlays, captions, and textual elements to video content. It provides customizable text styling, animation effects, and positioning for creating engaging video content with professional text presentations.

## API Endpoints

### 1. POST /text-overlay/process

**Purpose**: Apply text overlays to video content with custom styling and animations

**Description**: Processes video content by adding text overlays with customizable styling, positioning, and animation effects. Creates engaging video content by overlaying scene narration, captions, or custom text elements with professional typography and timing.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JW<PERSON> token
- Verifies script exists and belongs to authenticated user
- Retrieves script content including scene narration and timing information
- Validates text content and ensures proper formatting for overlay application
- Analyzes video duration and scene transitions for optimal text timing
- Applies text overlay system with specified styling and animation parameters
- Processes video with enhanced text rendering engine including font optimization
- Synchronizes text appearance with scene timing and narration flow
- Uploads processed video to S3 storage with text overlay metadata
- Creates video asset record for the text-overlaid video
- Returns S3 presigned URL for accessing the processed video content

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - script_id: Script identifier for text overlay processing (required)
  - style: Text overlay style option ("modern", "classic", "minimal", "bold") (optional, default: "modern")
  - animation: Text animation type ("fade_in_out", "slide_up", "typewriter", "zoom", "none") (optional, default: "fade_in_out")
  - font_name: Custom font name for text rendering (optional, uses system default if not specified)

**Response Format**:
- **Success (200)**:
  - success: Boolean indicating successful text overlay processing
  - s3_url: S3 key for the text-overlaid video file
  - presigned_url: S3 presigned URL for video access with 2-hour expiration
  - script_id: Script identifier that was processed
  - overlays_count: Number of text overlays applied to the video
  - style: Text overlay style that was applied
  - animation: Animation type that was used
  - font_used: Font name that was applied for text rendering
  - message: Success message with processing details
- **Error (400)**: Invalid processing parameters or missing script content

**Error Handling**:
- **400 Bad Request**: Invalid style or animation parameters, missing script content, or unsupported font
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist, user lacks access, or video content not available
- **422 Validation Error**: Request body validation failed or text processing configuration errors
- **500 Internal Server Error**: Text overlay engine errors, video processing failures, or S3 upload issues

**Dependencies**:
- Enhanced text overlay rendering system with animation capabilities
- PostgreSQL database for script content retrieval and video_asset management
- AWS S3 for video file download, processing, and upload operations
- Font management system for custom typography support
- Video timing analysis for synchronized text appearance

**Business Rules**:
- Users can only apply text overlays to scripts they own within their organization
- Text overlay timing is automatically synchronized with scene transitions
- Font selection is validated against available system fonts
- Text overlay processing maintains original video quality and resolution
- Animation effects are optimized for readability and visual appeal
- Processing time scales with video length and text complexity
- Text content is extracted from scene narration and script metadata

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCRIPT_NOT_FOUND**: Requested script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **PERMISSION_DENIED**: User does not have permission to access resource

### Service-Specific Error Codes
- **TEXT_OVERLAY_PROCESSING_FAILED**: Text overlay processing encountered errors
- **SCRIPT_CONTENT_INSUFFICIENT**: Script lacks sufficient text content for overlay
- **FONT_NOT_AVAILABLE**: Requested font is not available on the system
- **ANIMATION_RENDERING_FAILED**: Text animation processing failed
- **VIDEO_TIMING_ANALYSIS_FAILED**: Video timing synchronization failed
- **TEXT_RENDERING_ERROR**: Text rendering engine encountered errors
- **S3_UPLOAD_FAILED**: Processed video upload to S3 failed
- **VIDEO_PROCESSING_ERROR**: Video processing or conversion failed

## Integration Details

### Text Overlay Rendering System
- Advanced text rendering engine supports multiple fonts and styles
- Typography optimization ensures readability across different video resolutions
- Text positioning system automatically adapts to video dimensions
- Animation engine provides smooth text transitions and effects
- Color management ensures text visibility against video backgrounds

### Video Timing Synchronization
- Scene timing analysis provides optimal text overlay placement
- Narration synchronization aligns text appearance with audio content
- Transition detection prevents text overlap during scene changes
- Duration optimization ensures text is displayed for appropriate reading time
- Frame-accurate timing provides precise text overlay control

### Font and Style Management
- Font validation system checks availability of requested typography
- Style templates provide consistent text appearance across overlays
- Dynamic font sizing adapts to video resolution and content length
- Color scheme management ensures text visibility and brand consistency
- Fallback font system handles unavailable custom fonts gracefully

### AWS S3 Integration
- Processed videos with text overlays are stored in S3 with metadata
- Presigned URLs provide secure access to text-overlaid video content
- Storage organization maintains hierarchy for easy asset management
- Metadata includes text overlay parameters and timing information
- Cleanup processes manage storage quotas and file retention

## Performance Considerations

### Processing Times
- Text overlay processing: 45-180 seconds depending on video length and text complexity
- Animation rendering adds 20-60 seconds for complex text effects
- Font rendering optimization reduces processing time for standard fonts
- Processing time scales with number of text overlays and animation complexity

### Resource Management
- Text overlay processing requires CPU resources for text rendering
- Memory management handles large video files during text processing
- Font caching optimizes repeated text rendering operations
- Processing queue manages concurrent text overlay requests

### Quality Optimization
- Text rendering maintains high resolution for crisp typography
- Anti-aliasing ensures smooth text appearance at all video resolutions
- Color optimization provides maximum text readability
- Animation smoothing prevents flickering or jerky text movements

### Scalability Factors
- Text overlay processing scales with available CPU resources
- Background task prioritization ensures responsive user experience
- Monitoring systems track processing performance and queue status
- Optimization algorithms balance processing speed with text quality
