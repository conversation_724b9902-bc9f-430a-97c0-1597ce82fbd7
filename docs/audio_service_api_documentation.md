# Audio Service API Documentation

## Overview

The Audio Service handles all audio generation tasks including background music creation, background sound generation, and text-to-speech voiceover generation. It integrates with Stable Audio and TTS services for high-quality audio content creation.

## API Endpoints

### 1. POST /audio/background-sound/generate

**Purpose**: Generate background sounds for multiple scenes

**Description**: Initiates the generation of ambient background sounds for specified scenes using AI-powered audio generation. Creates immersive soundscapes that match scene descriptions and visual content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JW<PERSON> token
- Verifies that all specified scene IDs exist and belong to the authenticated user
- Checks scene descriptions and visual content for sound generation context
- Queues background tasks for AI-powered sound generation for each scene
- Creates audio asset records with pending status in database
- Returns estimated completion time and scene processing information
- Background process uses Stable Audio to generate contextual ambient sounds
- Generated audio files are uploaded to S3 storage with presigned URLs
- Scene records are updated with audio asset references and generation status

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - scene_ids: Array of scene UUIDs to generate sounds for (required)
  - extract_audio_only: Boolean flag for audio-only extraction (optional, default: false)

**Response Format**:
- **Success (200)**:
  - scene_ids: Array of scene IDs being processed
  - message: Confirmation message about generation initiation
  - estimated_completion_time: Estimated time in seconds for completion
- **Error (404)**: One or more scenes not found or access denied

**Error Handling**:
- **400 Bad Request**: Invalid scene IDs or empty scene list
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: One or more scenes do not exist or user lacks access
- **422 Validation Error**: Request body validation failed
- **500 Internal Server Error**: Audio generation service errors, S3 upload failures, or database errors

**Dependencies**:
- Stable Audio service for ambient sound generation
- PostgreSQL database for scene and audio_asset operations
- AWS S3 for audio file storage and presigned URL generation
- Background task queue for asynchronous processing
- Scene content analysis for contextual audio generation

**Business Rules**:
- Users can only generate sounds for scenes within their own scripts and organization
- Each scene can have multiple background sound versions
- Generation process runs asynchronously to prevent API timeouts
- Generated audio files are stored in S3 with 1-hour presigned URL expiration
- Failed generation attempts are logged with detailed error messages
- Maximum of 10 scenes can be processed in a single request

---

### 2. GET /audio/background-sound/status/{script_id}

**Purpose**: Check background sound generation status for all scenes in a script

**Description**: Retrieves the current status of background sound generation for all scenes within a specified script. Provides comprehensive overview of audio generation progress and access to generated files.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves all scenes associated with the script
- Checks audio generation status for each scene
- Fetches S3 presigned URLs for successfully generated audio files
- Compiles comprehensive status report with scene-by-scene details
- Returns ordered list of scenes with their audio generation status

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to check status for (required)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - scenes: Array of scene audio status objects containing:
    - scene_id: Scene unique identifier
    - scene_number: Sequential scene number within script
    - audio_url: S3 presigned URL for generated audio (if available)
    - status: Generation status ("pending", "generating", "completed", "failed")
    - message: Status message or error description
    - success: Boolean indicating successful generation
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user does not have access
- **500 Internal Server Error**: Database errors or S3 access failures

**Dependencies**:
- PostgreSQL database for script, scene, and audio_asset lookup
- AWS S3 for presigned URL generation
- Audio generation status tracking system
- Script ownership verification

**Business Rules**:
- Users can only check status for scripts within their own organization
- Presigned URLs are generated with 1-hour expiration for security
- Status includes both current and historical generation attempts
- Scenes without audio generation attempts show as "not_started"
- Failed generations include specific error messages for debugging

---

### 3. PUT /audio/background-sound/update-prompt/{scene_id}

**Purpose**: Update background sound generation prompt and regenerate audio

**Description**: Updates the prompt used for background sound generation for a specific scene and triggers immediate regeneration with the new parameters. Allows fine-tuning of audio content to match creative vision.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership
- Updates scene metadata with new audio generation prompt
- Marks previous audio generation as superseded
- Queues new background task for AI-powered sound regeneration
- Creates new audio asset record with updated parameters
- Returns task information for tracking regeneration progress
- Background process uses updated prompt to generate new audio content
- New audio file replaces previous version in S3 storage

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to update prompt for (required)
- **Request Body** (JSON):
  - prompt: Updated prompt for background sound generation (required)

**Response Format**:
- **Success (200)**:
  - scene_id: Scene identifier
  - message: Confirmation of prompt update and regeneration initiation
  - status: Updated generation status
  - estimated_completion_time: Estimated regeneration time in seconds
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user lacks access through script ownership
- **422 Validation Error**: Missing or invalid prompt in request body
- **500 Internal Server Error**: Database update errors, task queue failures, or audio service errors

**Dependencies**:
- Stable Audio service for regeneration with updated prompts
- PostgreSQL database for scene and audio_asset management
- Background task queue for asynchronous regeneration
- S3 storage for new audio file upload
- Scene ownership verification through script relationships

**Business Rules**:
- Users can only update prompts for scenes in scripts they own
- Prompt updates trigger immediate regeneration
- Previous audio versions are preserved but marked as superseded
- Regeneration uses same AI model and parameters as original generation
- Empty or whitespace-only prompts are rejected

---

### 4. POST /audio/background-music/generate

**Purpose**: Generate background music for entire script

**Description**: Creates script-level background music that spans the entire video content. Uses AI to generate cohesive musical themes that complement the script's tone and brand identity.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Analyzes script content, brand information, and video style for music context
- Generates unique task ID for tracking music generation progress
- Creates audio asset record for script-level background music
- Queues background task for AI-powered music generation using Stable Audio
- Returns task information for progress monitoring
- Background process analyzes script metadata to create appropriate musical style
- Generated music file is uploaded to S3 with metadata about generation parameters
- Script record is updated with background music asset reference

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - script_id: Script identifier for music generation (required)
  - music_style: Preferred musical style or genre (optional)
  - duration: Target music duration in seconds (optional, defaults to script duration)
  - mood: Desired mood or emotional tone (optional)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - audio_url: S3 presigned URL for generated music file
  - status: Generation status
  - message: Confirmation message
  - file_size: Generated file size in bytes
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **400 Bad Request**: Invalid duration or music parameters
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **422 Validation Error**: Request body validation failed
- **500 Internal Server Error**: Music generation failures, S3 upload errors, or database issues

**Dependencies**:
- Stable Audio service for music generation
- PostgreSQL database for script and audio_asset operations
- AWS S3 for music file storage
- Background task processing system
- Script content analysis for musical context

**Business Rules**:
- Each script can have only one active background music track
- Music duration automatically matches script duration if not specified
- Generation considers brand identity and video style for appropriate musical themes
- Failed generation attempts can be retried with different parameters
- Generated music files are optimized for video background use

---

### 5. GET /audio/background-music/status/{script_id}

**Purpose**: Check background music generation status for script

**Description**: Retrieves the current status of background music generation for a specific script, including access to the generated music file and generation metadata.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Looks up background music audio asset associated with the script
- Generates fresh S3 presigned URL for music file access
- Retrieves generation metadata including prompts and parameters
- Compiles comprehensive status information
- Returns detailed music generation status and access information

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to check music status for (required)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - status: Generation status ("pending", "generating", "completed", "failed")
  - audio_url: S3 presigned URL for music file (if completed)
  - file_size: Music file size in bytes
  - duration: Music duration in seconds
  - generation_metadata: Object containing generation parameters and AI model information
  - created_at: Generation timestamp
  - updated_at: Last status update timestamp
- **Error (404)**: Script not found or no music generation found

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist, user lacks access, or no music generation found
- **500 Internal Server Error**: Database errors or S3 access failures

**Dependencies**:
- PostgreSQL database for script and audio_asset lookup
- AWS S3 for presigned URL generation
- Music generation status tracking
- Script ownership verification

**Business Rules**:
- Users can only check music status for scripts they own
- Presigned URLs expire after 1 hour for security
- Status includes generation progress and error details
- Metadata includes AI model parameters and generation prompts
- Music files are accessible until script deletion

---

### 6. PUT /audio/background-music/update-prompt/{script_id}

**Purpose**: Update background music prompt and regenerate music

**Description**: Updates the generation parameters for script background music and triggers immediate regeneration with new creative direction. Enables iterative refinement of musical content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Updates script metadata with new music generation parameters
- Stores updated prompt and generation metadata
- Marks previous music generation as superseded
- Queues new background task for music regeneration with updated parameters
- Creates new audio asset record for the regenerated music
- Returns task information for tracking regeneration progress
- Background process generates new music using updated creative direction
- New music file replaces previous version with updated metadata

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to update music for (required)
- **Request Body** (JSON):
  - prompt: Updated prompt for music generation (required)
  - generation_metadata: Additional parameters for music generation (optional)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - message: Confirmation of prompt update and regeneration initiation
  - status: Updated generation status
  - task_id: Background task identifier for progress tracking
  - estimated_completion_time: Estimated regeneration time in seconds
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **422 Validation Error**: Missing or invalid prompt in request body
- **500 Internal Server Error**: Database update errors, task queue failures, or music service errors

**Dependencies**:
- Stable Audio service for music regeneration
- PostgreSQL database for script and audio_asset management
- Background task queue for asynchronous processing
- S3 storage for new music file upload
- Script ownership verification

**Business Rules**:
- Users can only update music prompts for scripts they own
- Prompt updates trigger immediate music regeneration
- Previous music versions are preserved but marked as superseded
- Regeneration maintains consistency with script duration and style
- Generation metadata is preserved for auditing and optimization

---

### 7. POST /audio/voiceover/scenes/generate

**Purpose**: Generate voiceover narration for multiple scenes

**Description**: Creates text-to-speech voiceover narration for specified scenes using advanced TTS technology. Generates natural-sounding speech that matches the script's narration content and style.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies all specified scene IDs exist and belong to authenticated user
- Retrieves narration text content for each scene
- Validates that scenes have narration content available for TTS conversion
- Queues background tasks for TTS generation for each scene
- Creates audio asset records for voiceover files with pending status
- Returns task information for tracking voiceover generation progress
- Background process uses TTS service to convert narration text to speech
- Generated voiceover files are uploaded to S3 with appropriate metadata
- Scene records are updated with voiceover audio asset references

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - scene_ids: Array of scene UUIDs to generate voiceovers for (required)

**Response Format**:
- **Success (200)**:
  - scene_ids: Array of scene IDs being processed for voiceover generation
  - message: Confirmation of voiceover generation initiation
  - estimated_completion_time: Estimated processing time in seconds
  - task_ids: Array of task identifiers for progress tracking
- **Error (400)**: Invalid scene IDs or scenes without narration content

**Error Handling**:
- **400 Bad Request**: Invalid scene IDs, empty scene list, or scenes lacking narration content
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: One or more scenes do not exist or user lacks access
- **422 Validation Error**: Request body validation failed
- **500 Internal Server Error**: TTS service errors, S3 upload failures, or database errors

**Dependencies**:
- Text-to-Speech service for narration conversion
- PostgreSQL database for scene and audio_asset operations
- AWS S3 for voiceover file storage
- Background task queue for asynchronous processing
- Scene narration content validation

**Business Rules**:
- Users can only generate voiceovers for scenes in scripts they own
- Scenes must have narration content to generate voiceovers
- Each scene can have multiple voiceover versions
- Voiceover generation uses consistent voice settings across scenes
- Maximum of 20 scenes can be processed in a single request

---

### 8. GET /audio/voiceover/status/{script_id}

**Purpose**: Check voiceover generation status for all scenes in script

**Description**: Retrieves comprehensive voiceover generation status for all scenes within a script, including access to generated voiceover files and generation metadata.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves all scenes associated with the script
- Checks voiceover generation status for each scene
- Generates S3 presigned URLs for completed voiceover files
- Compiles scene-by-scene voiceover status information
- Returns comprehensive voiceover status report ordered by scene number

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to check voiceover status for (required)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - scenes: Array of scene voiceover status objects containing:
    - scene_id: Scene unique identifier
    - voiceover_prompt: Original narration text used for TTS
    - voiceover_status: Generation status ("pending", "generating", "completed", "failed")
    - s3_presigned_url: URL for accessing generated voiceover file
    - voiceover_generated_at: Timestamp of successful generation
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **500 Internal Server Error**: Database errors or S3 access failures

**Dependencies**:
- PostgreSQL database for script, scene, and audio_asset lookup
- AWS S3 for presigned URL generation
- Voiceover generation status tracking
- Scene ownership verification through script relationships

**Business Rules**:
- Users can only check voiceover status for scripts they own
- Status includes scenes without voiceover generation attempts
- Presigned URLs expire after 1 hour for security
- Voiceover files remain accessible until scene or script deletion
- Status includes both successful and failed generation attempts

---

### 9. PUT /audio/voiceover/scenes/{scene_id}/prompt

**Purpose**: Update voiceover narration text and regenerate TTS

**Description**: Updates the narration text for a specific scene and triggers immediate regeneration of the voiceover using updated content. Enables refinement of narration content and TTS output.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership
- Updates scene narration content with provided text
- Validates updated narration text for TTS compatibility
- Marks previous voiceover generation as superseded
- Queues new background task for TTS generation with updated narration
- Creates new audio asset record for regenerated voiceover
- Returns task information for tracking regeneration progress
- Background process converts updated narration text to speech
- New voiceover file replaces previous version in S3 storage

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to update narration for (required)
- **Request Body** (JSON):
  - prompt: Updated narration text for TTS generation (required)

**Response Format**:
- **Success (200)**:
  - scene_id: Scene identifier
  - message: Confirmation of narration update and regeneration initiation
  - status: Updated generation status
  - task_id: Background task identifier
  - estimated_completion_time: Estimated regeneration time in seconds
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user lacks access through script ownership
- **422 Validation Error**: Missing, empty, or invalid narration text
- **500 Internal Server Error**: Database update errors, task queue failures, or TTS service errors

**Dependencies**:
- Text-to-Speech service for narration regeneration
- PostgreSQL database for scene and audio_asset management
- Background task queue for asynchronous processing
- S3 storage for new voiceover file upload
- Scene ownership verification through script relationships

**Business Rules**:
- Users can only update narration for scenes in scripts they own
- Narration updates trigger immediate voiceover regeneration
- Previous voiceover versions are preserved but marked as superseded
- TTS generation maintains consistent voice settings across regenerations
- Empty or whitespace-only narration text is rejected

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCENE_NOT_FOUND**: Requested scene does not exist or access denied
- **SCRIPT_NOT_FOUND**: Requested script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **PERMISSION_DENIED**: User does not have permission to access resource

### Service-Specific Error Codes
- **AUDIO_GENERATION_FAILED**: Audio generation process failed
- **TTS_GENERATION_FAILED**: Text-to-speech conversion failed
- **STABLE_AUDIO_ERROR**: Stable Audio service returned error
- **S3_UPLOAD_FAILED**: Audio file upload to S3 failed
- **NARRATION_CONTENT_MISSING**: Scene lacks narration content for TTS
- **AUDIO_PROCESSING_ERROR**: Audio file processing or conversion failed
- **TASK_QUEUE_FULL**: Background task queue is at capacity
- **PRESIGNED_URL_GENERATION_FAILED**: S3 presigned URL generation failed

## Integration Details

### Stable Audio Integration
- Background music and sound generation uses Stable Audio API
- Prompts are enhanced with scene context and brand information
- Generated audio is optimized for video background use
- Quality settings are configured for web streaming compatibility
- Generation parameters include duration, style, and mood controls

### Text-to-Speech Integration
- Voiceover generation uses advanced TTS technology
- Voice settings are consistent across all scenes in a script
- Narration text is preprocessed for optimal speech synthesis
- Generated speech files are normalized for consistent volume levels
- TTS output is optimized for clarity and natural delivery

### AWS S3 Storage
- All generated audio files are stored in S3 buckets
- Presigned URLs provide secure, time-limited access to audio files
- File organization follows script and scene hierarchy
- Metadata includes generation parameters and AI model information
- Automatic cleanup processes manage storage quotas and retention

## Performance Considerations

### Generation Times
- Background sound generation: 20-60 seconds per scene depending on complexity
- Background music generation: 60-180 seconds depending on script length
- Voiceover generation: 10-30 seconds per scene depending on narration length
- Batch processing optimizes resource usage for multiple scenes

### Rate Limiting
- Users limited to concurrent audio generation requests
- Organizations have daily generation quotas
- API rate limiting prevents service overload
- Background tasks are prioritized to prevent queue overflow

### Scalability Factors
- Audio generation scales horizontally with additional worker processes
- S3 storage provides unlimited capacity for audio files
- Database operations are optimized for large audio libraries
- Background task monitoring provides visibility into system performance
