# Scene Service API Documentation

## Overview

The Scene Service handles individual scene management within scripts, including scene retrieval, manual editing, AI-powered regeneration, and deletion. It provides granular control over script content at the scene level.

## API Endpoints

### 1. GET /scenes/

**Purpose**: List all scenes for a specific script

**Description**: Retrieves all scenes associated with a given script ID, ordered by scene number. Provides both scene IDs for reference and complete scene data for display and management.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JWT token
- Verifies that the specified script exists and belongs to the authenticated user
- Queries database for all scenes associated with the script ID
- Orders scenes by scene number to maintain proper sequence
- Formats scene data into simplified dictionary format for easy consumption
- Returns both array of scene IDs and array of scene objects
- Handles cases where scripts exist but have no associated scenes

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Query Parameters**:
  - script_id: UUID of the script to retrieve scenes for (required)

**Response Format**:
- **Success (200)**:
  - scene_ids: Array of scene UUID strings for reference
  - scenes: Array of scene objects containing:
    - id: Scene unique identifier
    - scene_number: Sequential scene number within script
    - title: Scene title or heading
    - description: Detailed scene description
    - visual: Visual description for video generation
    - narration: Narration text for voiceover
    - duration: Scene duration in seconds
    - location_group: Location grouping for video production
    - status: Scene processing status
    - generation_status: AI generation status
    - full_content: Complete scene content
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user does not have access to script
- **422 Validation Error**: Invalid script_id format or missing required parameter
- **500 Internal Server Error**: Database connection errors or scene retrieval failures

**Dependencies**:
- PostgreSQL database for script and scene table operations
- JWT authentication system for user validation
- Script ownership verification through user and organization checks
- Scene ordering logic based on scene_number field

**Business Rules**:
- Users can only access scenes for scripts within their own organization
- Scripts must belong to the authenticated user for scene access
- Scenes are always returned in order by scene_number
- Empty scene lists are returned as valid responses for scripts without scenes
- Scene data includes both content and metadata for management purposes

---

### 2. PUT /scenes/{scene_id}

**Purpose**: Manual scene editing and updates

**Description**: Allows direct editing of scene content including title, description, narration, and visual descriptions. Provides manual control over scene content without AI regeneration.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership check
- Validates provided update data against scene schema requirements
- Updates specified scene fields with new content
- Preserves existing fields that are not included in update request
- Updates scene modification timestamp
- Returns updated scene data with all current field values
- Handles partial updates where only some fields are modified

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to update (required)
- **Request Body** (JSON):
  - title: Updated scene title (optional)
  - description: Updated scene description (optional)
  - visual_description: Updated visual description (optional)
  - narration: Updated narration text (optional)
  - duration: Updated scene duration in seconds (optional)
  - location_group: Updated location grouping (optional)

**Response Format**:
- **Success (200)**:
  - id: Scene unique identifier
  - script_id: Associated script identifier
  - text_asset_id: Associated text asset identifier
  - scene_number: Scene number within script
  - title: Current scene title
  - description: Current scene description
  - visual_description: Current visual description
  - narration: Current narration text
  - duration: Current scene duration
  - location_group: Current location group
  - status: Scene processing status
  - generation_status: AI generation status
  - error_message: Error details if applicable
  - character_info: Character information object
  - created_at: Scene creation timestamp
  - updated_at: Last modification timestamp
  - regenerated_at: Last regeneration timestamp
  - metadata: Additional scene metadata
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user does not have access through script ownership
- **422 Validation Error**: Invalid scene update data or field validation failures
- **500 Internal Server Error**: Database update errors or transaction failures

**Dependencies**:
- PostgreSQL database for scene table updates and script ownership verification
- JWT authentication system for user validation
- Scene update validation logic for field constraints
- Transaction management for atomic updates

**Business Rules**:
- Users can only edit scenes for scripts they own within their organization
- Partial updates are supported (only specified fields are modified)
- Scene numbers cannot be changed through this endpoint
- Duration must be positive if specified
- Manual edits do not trigger AI regeneration
- Update timestamps are automatically maintained

---

### 3. POST /scenes/{scene_id}/regenerate

**Purpose**: AI-powered scene regeneration based on feedback

**Description**: Triggers AI-powered regeneration of a specific scene using user feedback and original script context. Creates background task for asynchronous processing and returns task tracking information.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership
- Creates unique task ID for tracking regeneration progress
- Stores user feedback and regeneration parameters in task queue
- Queues Celery background task for AI-powered scene regeneration
- Updates scene status to indicate regeneration in progress
- Returns task information for progress monitoring
- Background process uses Gemini AI with feedback to regenerate scene content
- Scene content is updated with new AI-generated content upon completion

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to regenerate (required)
- **Request Body** (JSON):
  - feedback: User feedback for guiding regeneration (required)

**Response Format**:
- **Success (200)**:
  - id: Task queue record identifier
  - task_id: Unique task identifier for tracking
  - task_type: Always "scene_regeneration"
  - org_id: Organization identifier
  - user_id: User identifier who requested regeneration
  - status: Initial task status "pending"
  - progress: Initial progress value (0)
  - created_at: Task creation timestamp
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user does not have access through script ownership
- **422 Validation Error**: Missing or invalid feedback in request body
- **500 Internal Server Error**: Task creation failures, database errors, or Celery task queue errors

**Dependencies**:
- Gemini AI service for intelligent scene regeneration
- PostgreSQL database for scene, script, and task_queue operations
- Celery task queue system for asynchronous processing
- JWT authentication system for user validation
- Background worker processes for AI generation

**Business Rules**:
- Users can only regenerate scenes for scripts they own
- Feedback is required to guide the regeneration process
- Regeneration runs asynchronously to prevent API timeouts
- Original scene content is preserved until regeneration completes successfully
- Failed regeneration attempts maintain original content
- Multiple regeneration requests for the same scene are queued sequentially

---

### 4. DELETE /scenes/{scene_id}

**Purpose**: Scene deletion and cleanup

**Description**: Permanently removes a scene from the database along with associated content and metadata. Provides cleanup functionality for unwanted or obsolete scenes.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership check
- Checks for any dependent resources or references to the scene
- Removes scene record from database with cascading deletes
- Cleans up associated text assets and metadata
- Updates script metadata to reflect scene removal
- Commits database transaction for permanent deletion
- Returns success confirmation without scene data

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to delete (required)

**Response Format**:
- **Success (204)**: No content (successful deletion)
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user does not have access through script ownership
- **409 Conflict**: Scene cannot be deleted due to dependent resources
- **500 Internal Server Error**: Database deletion errors or transaction failures

**Dependencies**:
- PostgreSQL database for scene deletion and cleanup operations
- JWT authentication system for user validation
- Script ownership verification through database relationships
- Cascading delete logic for associated resources

**Business Rules**:
- Users can only delete scenes for scripts they own within their organization
- Scene deletion is permanent and cannot be undone
- Associated text assets and metadata are automatically cleaned up
- Scene numbers for remaining scenes are not automatically reordered
- Deletion of scenes does not affect overall script status
- Dependent video or audio assets may need separate cleanup

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCENE_NOT_FOUND**: Requested scene does not exist or access denied
- **SCRIPT_NOT_FOUND**: Associated script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **PERMISSION_DENIED**: User does not have permission to access scene

### Service-Specific Error Codes
- **SCENE_REGENERATION_FAILED**: AI scene regeneration process failed
- **SCENE_UPDATE_FAILED**: Scene update operation failed
- **SCENE_DELETE_FAILED**: Scene deletion operation failed
- **TASK_CREATION_FAILED**: Background task creation failed
- **GEMINI_API_ERROR**: Gemini AI service returned error
- **FEEDBACK_REQUIRED**: Feedback is required for scene regeneration

## Integration Details

### AI Regeneration Process
- Scene regeneration uses Gemini AI with context from original script
- User feedback guides the regeneration direction and style
- Original scene content provides baseline for improvements
- Brand context from parent script is maintained during regeneration
- Generated content maintains consistency with overall script tone

### Task Queue Integration
- Scene regeneration uses Celery for asynchronous processing
- Task progress can be monitored through separate task endpoints
- Failed regeneration attempts are retried automatically
- Task queue provides scalability for concurrent regeneration requests

### Content Management
- Scene content is stored in text_assets table with versioning
- Original content is preserved during regeneration attempts
- Successful regeneration replaces content atomically
- Scene metadata tracks regeneration history and timestamps

## Performance Considerations

### Regeneration Performance
- Scene regeneration typically takes 15-60 seconds depending on complexity
- Feedback quality affects generation time and success rate
- Complex scenes with multiple characters may require longer processing
- Failed regenerations are retried with exponential backoff

### Database Performance
- Scene queries are optimized with proper indexing
- Script ownership checks use efficient join operations
- Scene ordering is handled at database level for performance
- Large scene lists are paginated to prevent memory issues

### Scalability Factors
- Scene operations scale independently from script generation
- Background regeneration allows high concurrent usage
- Database connections are pooled for efficient resource usage
- Task queue monitoring provides visibility into system performance
