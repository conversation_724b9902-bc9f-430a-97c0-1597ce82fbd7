# Image Overlay Service API Documentation

## Overview

The Image Overlay Service handles brand overlay and logo placement on video content. It provides multiple overlay options for adding branding elements, logos, and custom graphics to generated videos with professional quality and positioning.

## API Endpoints

### 1. POST /image-overlay/simple

**Purpose**: Apply simple logo overlay to video content

**Description**: Applies a basic logo overlay to video content with configurable positioning, size, and transparency settings. Ideal for simple branding requirements without complex animations or effects.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JWT token
- Verifies script exists and belongs to authenticated user
- Retrieves or generates text overlay video for the specified script
- Downloads logo image from provided URL or uses uploaded base64 image
- Validates image format and dimensions for overlay compatibility
- Applies simple logo overlay with specified positioning and opacity
- Uploads processed video to S3 storage with overlay metadata
- Creates video asset record for the overlaid video
- Returns S3 presigned URL for accessing the processed video

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - script_id: Script identifier for video overlay (required)
  - logo_url: URL of logo image to overlay (optional)
  - logo_base64: Base64 encoded logo image data (optional)
  - position: Logo position on video ("top-left", "top-right", "bottom-left", "bottom-right", "center") (optional, default: "bottom-right")
  - size: Logo size as percentage of video width (optional, default: 10)
  - opacity: Logo opacity from 0.0 to 1.0 (optional, default: 0.8)

**Response Format**:
- **Success (200)**:
  - success: Boolean indicating successful overlay application
  - s3_url: S3 key for the overlaid video file
  - presigned_url: S3 presigned URL for video access
  - script_id: Script identifier
  - overlay_type: Type of overlay applied ("simple")
  - processing_time: Time taken for overlay processing in seconds
  - file_size: Final video file size in bytes
- **Error (400)**: Invalid overlay parameters or missing logo data

**Error Handling**:
- **400 Bad Request**: Missing logo data, invalid position or size parameters, incompatible image format
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist, user lacks access, or text overlay video not found
- **422 Validation Error**: Request body validation failed or image processing errors
- **500 Internal Server Error**: Video processing failures, S3 upload errors, or overlay engine errors

**Dependencies**:
- Video overlay processing engine for logo application
- PostgreSQL database for script and video_asset operations
- AWS S3 for video file download, processing, and upload
- Image processing libraries for logo format conversion and optimization
- Text overlay video retrieval system

**Business Rules**:
- Users can only apply overlays to scripts they own within their organization
- Either logo_url or logo_base64 must be provided
- Logo images must be in supported formats (PNG, JPG, SVG)
- Video processing maintains original video quality while adding overlay
- Overlay positioning is relative to video dimensions
- Processing time depends on video length and logo complexity

---

### 2. POST /image-overlay/option-a

**Purpose**: Apply enhanced brand overlay with animation effects (Option A)

**Description**: Applies sophisticated brand overlay with animated entry/exit effects, custom positioning, and enhanced visual styling. Provides professional branding with smooth animations and transitions.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves text overlay video for the specified script
- Downloads and validates brand assets including logos and graphics
- Applies Option A overlay style with animated effects and transitions
- Processes video with enhanced overlay engine including timing controls
- Uploads final video to S3 with comprehensive overlay metadata
- Creates video asset record with animation details
- Returns access information for the branded video

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - script_id: Script identifier for video overlay (required)
  - logo_url: URL of primary logo image (optional)
  - logo_base64: Base64 encoded logo image data (optional)
  - brand_colors: Array of brand colors in hex format (optional)
  - animation_duration: Duration of entry/exit animations in seconds (optional, default: 2.0)
  - display_duration: Duration logo remains visible in seconds (optional, default: 5.0)
  - position: Enhanced positioning with animation options (optional)

**Response Format**:
- **Success (200)**:
  - success: Boolean indicating successful overlay application
  - s3_url: S3 key for the branded video file
  - presigned_url: S3 presigned URL for video access
  - script_id: Script identifier
  - overlay_type: Type of overlay applied ("option_a")
  - animation_details: Object containing animation timing and effects information
  - processing_time: Time taken for overlay processing in seconds
  - file_size: Final video file size in bytes
- **Error (400)**: Invalid animation parameters or brand asset issues

**Error Handling**:
- **400 Bad Request**: Invalid animation parameters, missing brand assets, or incompatible timing settings
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist, user lacks access, or required video assets not found
- **422 Validation Error**: Request body validation failed or animation processing errors
- **500 Internal Server Error**: Enhanced overlay engine errors, animation rendering failures, or S3 issues

**Dependencies**:
- Enhanced video overlay processing engine with animation capabilities
- PostgreSQL database for script and video_asset management
- AWS S3 for video asset management and final upload
- Animation rendering engine for smooth brand overlay transitions
- Brand asset validation and optimization system

**Business Rules**:
- Users can only apply enhanced overlays to scripts they own
- Animation timings must be compatible with video duration
- Brand colors are validated for proper hex format
- Enhanced overlays require higher processing resources
- Animation quality is optimized for web streaming
- Processing time is longer due to animation complexity

---

### 3. POST /image-overlay/option-b

**Purpose**: Apply advanced brand overlay with complex animations (Option B)

**Description**: Applies the most sophisticated brand overlay option with complex animations, multi-layer effects, and advanced positioning controls. Provides premium branding experience with customizable visual effects.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves text overlay video and validates video properties
- Downloads and processes multiple brand assets for layered overlay
- Applies Option B overlay style with complex multi-layer animations
- Renders advanced visual effects including gradients, shadows, and transitions
- Processes video with premium overlay engine and quality optimization
- Uploads final premium branded video to S3 with detailed metadata
- Creates video asset record with comprehensive overlay information
- Returns access information for the premium branded video

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - script_id: Script identifier for video overlay (required)
  - logo_url: URL of primary logo image (optional)
  - logo_base64: Base64 encoded logo image data (optional)
  - secondary_logo_url: URL of secondary logo or graphic (optional)
  - brand_colors: Array of brand colors for gradient effects (optional)
  - animation_style: Advanced animation style option (optional)
  - effect_intensity: Intensity of visual effects from 0.0 to 1.0 (optional, default: 0.7)
  - layer_composition: Configuration for multi-layer overlay effects (optional)

**Response Format**:
- **Success (200)**:
  - success: Boolean indicating successful overlay application
  - s3_url: S3 key for the premium branded video file
  - presigned_url: S3 presigned URL for video access
  - script_id: Script identifier
  - overlay_type: Type of overlay applied ("option_b")
  - advanced_effects: Object containing details about applied visual effects
  - layer_information: Information about multi-layer composition
  - processing_time: Time taken for overlay processing in seconds
  - file_size: Final video file size in bytes
- **Error (400)**: Invalid advanced parameters or effect configuration issues

**Error Handling**:
- **400 Bad Request**: Invalid effect parameters, incompatible layer configuration, or resource constraints
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist, user lacks access, or required assets unavailable
- **422 Validation Error**: Request body validation failed or advanced effect processing errors
- **500 Internal Server Error**: Premium overlay engine errors, complex animation failures, or rendering issues

**Dependencies**:
- Premium video overlay processing engine with advanced effects
- PostgreSQL database for script and video_asset management
- AWS S3 for multi-asset management and premium video upload
- Advanced animation and effects rendering engine
- Multi-layer composition and optimization system

**Business Rules**:
- Users can only apply premium overlays to scripts they own
- Advanced effects require significant processing resources
- Multi-layer composition is optimized for video quality
- Premium overlays have longer processing times
- Effect intensity is balanced for visual appeal and performance
- Secondary assets are optional but enhance overlay quality

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCRIPT_NOT_FOUND**: Requested script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **PERMISSION_DENIED**: User does not have permission to access resource

### Service-Specific Error Codes
- **OVERLAY_PROCESSING_FAILED**: Video overlay processing failed
- **LOGO_DOWNLOAD_FAILED**: Logo image download or processing failed
- **TEXT_OVERLAY_VIDEO_NOT_FOUND**: Required text overlay video not available
- **INVALID_IMAGE_FORMAT**: Logo image format is not supported
- **ANIMATION_RENDERING_FAILED**: Animation processing encountered errors
- **S3_UPLOAD_FAILED**: Processed video upload to S3 failed
- **VIDEO_PROCESSING_ERROR**: Video processing or conversion failed
- **BRAND_ASSET_VALIDATION_FAILED**: Brand assets failed validation checks
- **OVERLAY_ENGINE_ERROR**: Video overlay engine encountered error

## Integration Details

### Video Overlay Processing
- Multiple overlay engines handle different complexity levels
- Image processing optimizes logos for video overlay compatibility
- Animation systems provide smooth transitions and effects
- Quality optimization maintains video resolution while adding overlays
- Processing pipeline supports various video formats and resolutions

### Brand Asset Management
- Logo images are validated for format and dimension compatibility
- Base64 encoding supports direct image upload without external URLs
- Multi-asset processing enables complex layered overlay effects
- Brand color validation ensures proper hex format and visual consistency
- Asset optimization balances quality with processing performance

### AWS S3 Integration
- Processed videos are uploaded to S3 with hierarchical organization
- Presigned URLs provide secure access to branded video content
- Metadata includes overlay parameters and processing information
- Storage optimization manages large video files efficiently
- Cleanup processes maintain storage quotas and retention policies

## Performance Considerations

### Processing Times
- Simple overlay: 30-120 seconds depending on video length
- Option A overlay: 60-240 seconds including animation rendering
- Option B overlay: 120-480 seconds for complex effects processing
- Processing time scales with video resolution and overlay complexity

### Resource Management
- Video overlay processing is CPU and memory intensive
- Animation rendering requires additional GPU resources for complex effects
- Processing queue manages concurrent overlay requests
- Quality optimization balances processing time with output quality

### Scalability Factors
- Overlay processing scales with available computational resources
- Background task prioritization ensures fair resource allocation
- Monitoring systems track processing performance and queue status
- Optimization algorithms reduce processing time while maintaining quality
