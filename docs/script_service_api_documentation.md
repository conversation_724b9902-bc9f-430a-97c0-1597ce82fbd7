# Script Service API Documentation

## Overview

The Script Service handles script generation, management, and retrieval using AI-powered content creation. It integrates with Gemini AI for intelligent script generation based on user inputs and brand requirements.

## API Endpoints

### 1. POST /scripts/

**Purpose**: Create and generate new script with AI assistance

**Description**: Initiates the creation of a new script based on user-provided content, brand information, and video specifications. Uses Gemini AI to generate structured script content with scenes, narration, and visual descriptions.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JWT token
- Creates initial script record in database with pending status
- Validates input parameters including duration, aspect ratio, and video style
- Generates unique task ID for tracking the generation process
- Creates task queue entry for asynchronous script generation
- Queues Celery background task for AI-powered script generation
- Returns task information for progress tracking
- Background process uses Gemini AI to analyze input and generate script content
- Script status updates from "pending" to "completed" or "failed" based on generation results

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - project_id: Project identifier for organization (required)
  - title: Script title for identification (required)
  - text_input: Source content for script generation (required)
  - video_style: Style preference for video output (required)
  - duration: Target duration in seconds (required, 15-300 seconds)
  - aspect_ratio: Video aspect ratio (required, e.g., "16:9", "9:16", "1:1")
  - narration_type: Type of narration desired (required)
  - brand_name: Brand name for context (optional)
  - product_name: Product name for context (optional)
  - brand_description: Detailed brand description for AI context (optional)

**Response Format**:
- **Success (202)**:
  - script_id: Unique identifier for the created script
  - task_id: Task identifier for tracking generation progress
  - task_type: Always "script_generation"
  - org_id: Organization identifier
  - user_id: User identifier who created the script
  - status: Initial status "pending"
  - progress: Initial progress value (0)
  - created_at: Timestamp of script creation
- **Error (400)**: Invalid input parameters or validation failures
- **Error (401)**: Authentication required or invalid token

**Error Handling**:
- **400 Bad Request**: Invalid duration (outside 15-300 second range), invalid aspect ratio format, missing required fields
- **401 Unauthorized**: Missing or invalid authentication token
- **422 Validation Error**: Request body validation failed for required fields
- **500 Internal Server Error**: Database connection errors, task queue failures, or Celery task creation errors

**Dependencies**:
- Gemini AI service for script content generation
- PostgreSQL database for script and task_queue table operations
- Celery task queue system for asynchronous processing
- JWT authentication system for user validation
- Background worker processes for AI generation

**Business Rules**:
- Script duration must be between 15 and 300 seconds
- Each script belongs to exactly one user and organization
- Script titles are not required to be unique within organization
- Generation process runs asynchronously to prevent API timeouts
- Failed generation attempts are logged with error messages
- Scripts can be regenerated if initial generation fails

---

### 2. GET /scripts/{script_id}

**Purpose**: Retrieve script details with optional scene inclusion

**Description**: Fetches complete script information including metadata, generation status, and optionally all associated scenes. Provides comprehensive view of script content and structure.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves script record from database with all metadata
- Fetches associated text asset content if available
- Optionally retrieves all scenes associated with the script
- Orders scenes by scene number for proper sequence
- Formats and returns comprehensive script data with scenes
- Includes generation status and error information if applicable

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to retrieve (required)
- **Query Parameters**:
  - include_scenes: Boolean flag to include scenes in response (optional, default: true)

**Response Format**:
- **Success (200)**:
  - id: Script unique identifier
  - org_id: Organization identifier
  - user_id: User identifier who owns the script
  - project_id: Associated project identifier
  - text_asset_id: Associated text asset identifier
  - title: Script title
  - video_style: Specified video style
  - duration: Target duration in seconds
  - aspect_ratio: Video aspect ratio
  - narration_type: Type of narration
  - brand_name: Associated brand name
  - product_name: Associated product name
  - brand_description: Brand description used for generation
  - content: Generated script content from text asset
  - status: Current script status ("pending", "completed", "failed")
  - generation_status: Detailed generation status
  - error_message: Error details if generation failed
  - created_at: Script creation timestamp
  - updated_at: Last modification timestamp
  - completed_at: Generation completion timestamp
  - metadata: Additional script metadata
  - scenes: Array of scene objects (if include_scenes=true)
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user does not have access
- **500 Internal Server Error**: Database connection errors or text asset retrieval failures

**Dependencies**:
- PostgreSQL database for script, scene, and text_asset table operations
- JWT authentication system for user validation and authorization
- Text asset storage system for script content retrieval
- Scene ordering and formatting logic

**Business Rules**:
- Users can only access scripts within their own organization
- Scripts must belong to the authenticated user for access
- Scene inclusion is optional and controlled by query parameter
- Scenes are returned in order by scene_number
- Content is retrieved from associated text assets when available
- Error messages are included for failed generation attempts

---

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCRIPT_NOT_FOUND**: Requested script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **GENERATION_FAILED**: AI script generation process failed
- **TASK_CREATION_FAILED**: Background task creation failed
- **DATABASE_ERROR**: Database operation failed

### Service-Specific Error Codes
- **INVALID_DURATION**: Duration outside allowed range (15-300 seconds)
- **INVALID_ASPECT_RATIO**: Aspect ratio format is invalid
- **GEMINI_API_ERROR**: Gemini AI service returned error
- **TEXT_ASSET_NOT_FOUND**: Associated text asset could not be retrieved
- **SCENE_GENERATION_FAILED**: Individual scene generation failed
- **QUEUE_FULL**: Task queue is at capacity

## Integration Details

### Gemini AI Integration
- Script generation uses Gemini AI for content analysis and creation
- Input text is analyzed for themes, tone, and key messages
- Brand information provides context for consistent messaging
- Generated content includes structured scenes with narration and visual descriptions
- AI responses are parsed and formatted into database-compatible structure

### Task Queue System
- Celery is used for asynchronous script generation
- Tasks are queued with unique identifiers for tracking
- Progress updates are stored in task_queue table
- Failed tasks include detailed error information
- Task status can be checked via separate task monitoring endpoints

### Content Storage
- Generated script content is stored in text_assets table
- Scripts reference text assets via text_asset_id
- Content is stored as structured text with scene breakdowns
- Metadata includes generation parameters and AI model information

## Performance Considerations

### Generation Times
- Script generation typically takes 30-120 seconds depending on complexity
- Longer content requires more processing time
- Brand context and detailed descriptions may increase generation time
- Failed generations are retried automatically up to 3 times

### Rate Limiting
- Users are limited to concurrent script generation requests
- Organizations have daily generation quotas
- API rate limiting applies to all script endpoints
- Background tasks are prioritized to prevent queue overflow

### Scalability
- Script generation scales horizontally with additional worker processes
- Database queries are optimized for large script libraries
- Text asset storage supports efficient content retrieval
- Task queue monitoring provides visibility into system load
