# Image Service API Documentation

## Overview

The Image Service handles AI-powered image generation, management, and prompt optimization using FLUX image generation technology. It creates high-quality images based on scene descriptions and provides comprehensive image asset management capabilities.

## API Endpoints

### 1. POST /images/generate

**Purpose**: Generate images for multiple scenes using AI

**Description**: Initiates AI-powered image generation for specified scenes using FLUX image generation technology. Creates high-quality images based on scene visual descriptions, brand context, and style preferences.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JWT token
- Verifies all specified scene IDs exist and belong to authenticated user
- Retrieves scene visual descriptions and associated script metadata
- Validates that scenes have sufficient visual content for image generation
- Enhances scene descriptions with brand context and style information
- Generates optimized prompts for FLUX image generation
- Creates image asset records with pending status in database
- Queues background tasks for AI-powered image generation
- Returns task information and estimated completion times
- Background process uses FLUX to generate images based on enhanced prompts
- Generated image files are uploaded to S3 storage with metadata
- Scene records are updated with image asset references and generation status

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - scene_ids: Array of scene UUIDs to generate images for (required)
  - image_style: Preferred image style or aesthetic (optional)
  - aspect_ratio: Image aspect ratio preference (optional, e.g., "16:9", "1:1")
  - quality: Image quality setting (optional, default: "high")
  - additional_prompts: Additional creative direction for image generation (optional)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier containing the processed scenes
  - scene_ids: Array of scene IDs being processed for image generation
  - message: Confirmation of image generation initiation
  - estimated_completion_time: Estimated processing time in seconds
  - task_ids: Array of background task identifiers
  - generation_status: Initial generation status
- **Error (400)**: Invalid scene IDs or insufficient visual descriptions

**Error Handling**:
- **400 Bad Request**: Invalid scene IDs, empty scene list, or scenes lacking visual descriptions
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: One or more scenes do not exist or user lacks access
- **422 Validation Error**: Request body validation failed for required fields
- **429 Too Many Requests**: Rate limit exceeded for image generation
- **500 Internal Server Error**: FLUX service errors, S3 upload failures, or database errors

**Dependencies**:
- FLUX image generation service for AI-powered image creation
- PostgreSQL database for scene, script, and image_asset operations
- AWS S3 for image file storage and management
- Background task queue for asynchronous processing
- Image prompt enhancement and optimization system

**Business Rules**:
- Users can only generate images for scenes within their own scripts and organization
- Scenes must have visual descriptions to generate images
- Each scene can have multiple image versions with different parameters
- Image generation considers brand context and script style for consistency
- Maximum of 10 scenes can be processed in a single request
- Generation process runs asynchronously to prevent API timeouts

---

### 2. POST /images/regenerate

**Purpose**: Regenerate images for scenes with updated parameters

**Description**: Triggers regeneration of images for specified scenes using updated prompts, style parameters, or generation settings. Enables iterative refinement of visual content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies all specified scene IDs exist and belong to authenticated user
- Retrieves current scene visual descriptions and image generation history
- Applies updated generation parameters and prompt enhancements
- Marks previous image generations as superseded
- Creates new image asset records for regenerated images
- Queues background tasks for FLUX image regeneration
- Returns task information for tracking regeneration progress
- Background process generates new images using updated parameters
- New image files replace previous versions with updated metadata
- Scene records are updated with new image asset references

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - scene_ids: Array of scene UUIDs to regenerate images for (required)
  - updated_prompts: Updated visual descriptions or creative direction (optional)
  - image_style: Updated image style preference (optional)
  - quality: Updated quality setting (optional)
  - force_regenerate: Boolean flag to force regeneration even if recent images exist (optional)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier containing the processed scenes
  - scene_ids: Array of scene IDs being processed for regeneration
  - message: Confirmation of image regeneration initiation
  - estimated_completion_time: Estimated regeneration time in seconds
  - task_ids: Array of background task identifiers
  - regeneration_status: Initial regeneration status
- **Error (400)**: Invalid regeneration parameters or scene constraints

**Error Handling**:
- **400 Bad Request**: Invalid scene IDs, incompatible regeneration parameters, or scenes without existing images
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: One or more scenes do not exist or user lacks access
- **422 Validation Error**: Request body validation failed or conflicting parameters
- **429 Too Many Requests**: Rate limit exceeded for image regeneration
- **500 Internal Server Error**: FLUX service errors, database errors, or task queue failures

**Dependencies**:
- FLUX image generation service for regeneration with updated parameters
- PostgreSQL database for scene and image_asset management
- Background task queue for asynchronous regeneration processing
- S3 storage for new image file upload and management
- Image generation history tracking and versioning

**Business Rules**:
- Users can only regenerate images for scenes in scripts they own
- Regeneration preserves previous image versions for comparison
- Updated parameters are merged with existing scene descriptions
- Regeneration queue is prioritized to prevent long wait times
- Force regeneration bypasses recent generation checks
- Image versioning maintains audit trail of generation attempts

---

### 3. GET /images/{script_id}

**Purpose**: Retrieve image generation details for all scenes in script

**Description**: Fetches comprehensive image information for all scenes within a specified script, including generation status, image metadata, and access URLs for generated content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves all scenes associated with the script
- Looks up image asset records for each scene
- Generates fresh S3 presigned URLs for image file access
- Compiles comprehensive image data for each scene
- Calculates overall script image generation progress
- Returns detailed image information ordered by scene number

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to retrieve image details for (required)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - overall_status: Overall image generation status for the script
  - progress_percentage: Percentage of scenes with completed image generation
  - scenes: Array of scene image details containing:
    - scene_id: Scene unique identifier
    - scene_number: Sequential scene number within script
    - image_assets: Array of image asset objects with:
      - asset_id: Image asset unique identifier
      - image_url: S3 presigned URL for image file access
      - status: Generation status ("pending", "generating", "completed", "failed")
      - file_size: Image file size in bytes
      - resolution: Image resolution (e.g., "1024x1024")
      - format: Image file format (e.g., "png", "jpg")
      - created_at: Generation timestamp
      - generation_metadata: Object containing generation parameters
    - visual_description: Scene visual description used for generation
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **500 Internal Server Error**: Database errors, S3 access failures, or image compilation errors

**Dependencies**:
- PostgreSQL database for script, scene, and image_asset lookup
- AWS S3 for presigned URL generation and image file access
- Image generation status tracking and progress calculation
- Script ownership verification and scene enumeration

**Business Rules**:
- Users can only access image details for scripts they own
- Presigned URLs are generated with 1-hour expiration for security
- Image metadata includes both current and historical generation attempts
- Multiple image versions per scene are supported with version tracking
- Progress calculation considers scenes with sufficient visual descriptions
- Image files remain accessible until scene or script deletion

---

### 4. GET /images/status/{script_id}

**Purpose**: Check image generation status overview for script

**Description**: Provides streamlined overview of image generation status for all scenes within a script, optimized for status monitoring and progress tracking.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves image generation status for all scenes in the script
- Calculates overall generation progress and completion statistics
- Generates S3 presigned URLs for completed images
- Compiles scene-by-scene status information
- Returns comprehensive status overview ordered by scene number

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to check image status for (required)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - scenes: Array of scene image status objects containing:
    - id: Scene unique identifier
    - scene_number: Sequential scene number within script
    - generation_status: Current generation status
    - presigned_url: S3 URL for accessing generated image (if completed)
    - error_message: Error description for failed generations
    - image_prompt: Visual description used for image generation
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **500 Internal Server Error**: Database errors or S3 access failures

**Dependencies**:
- PostgreSQL database for script, scene, and image_asset lookup
- AWS S3 for presigned URL generation
- Image generation status tracking system
- Script ownership verification

**Business Rules**:
- Users can only check image status for scripts they own
- Status includes scenes without image generation attempts
- Presigned URLs expire after 1 hour for security
- Status reflects most recent generation attempt per scene
- Error messages provide specific debugging information
- Generation progress excludes scenes without visual descriptions

---

### 5. PUT /images/update-prompt/{scene_id}

**Purpose**: Update image generation prompt and regenerate image

**Description**: Updates the visual description and generation parameters for a specific scene and triggers immediate image regeneration with the new creative direction. Enables fine-tuning of visual content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership
- Updates scene visual description and image generation parameters
- Validates updated prompts for FLUX image generation compatibility
- Marks previous image generation as superseded
- Generates unique task ID for image regeneration tracking
- Queues new background task for FLUX image generation with updated parameters
- Creates new image asset record for the regenerated image
- Returns task information for tracking regeneration progress
- Background process uses updated prompts to generate new image content
- New image file replaces previous version with updated metadata

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to update image prompt for (required)
- **Request Body** (JSON):
  - visual_description: Updated visual description for image generation (required)
  - image_style: Updated image style preference (optional)
  - additional_prompts: Additional creative direction for image generation (optional)
  - quality: Updated quality setting (optional)

**Response Format**:
- **Success (200)**:
  - scene_id: Scene identifier
  - task_id: Background task identifier for regeneration tracking
  - message: Confirmation of prompt update and regeneration initiation
  - status: Updated generation status
  - estimated_completion_time: Estimated regeneration time in seconds
  - updated_visual_description: The updated visual description
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user lacks access through script ownership
- **422 Validation Error**: Missing, empty, or invalid visual description
- **429 Too Many Requests**: Rate limit exceeded for image regeneration
- **500 Internal Server Error**: Database update errors, task queue failures, or image service errors

**Dependencies**:
- FLUX image generation service for regeneration with updated prompts
- PostgreSQL database for scene and image_asset management
- Background task queue for asynchronous regeneration processing
- S3 storage for new image file upload and management
- Scene ownership verification through script relationships

**Business Rules**:
- Users can only update image prompts for scenes in scripts they own
- Prompt updates trigger immediate image regeneration
- Previous image versions are preserved but marked as superseded
- Regeneration uses same technical parameters unless explicitly updated
- Visual descriptions must contain sufficient detail for image generation
- Regeneration queue is prioritized to prevent long wait times

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCENE_NOT_FOUND**: Requested scene does not exist or access denied
- **SCRIPT_NOT_FOUND**: Requested script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **PERMISSION_DENIED**: User does not have permission to access resource

### Service-Specific Error Codes
- **IMAGE_GENERATION_FAILED**: Image generation process failed
- **FLUX_API_ERROR**: FLUX image generation service returned error
- **INSUFFICIENT_VISUAL_DESCRIPTION**: Scene lacks visual description for image generation
- **S3_UPLOAD_FAILED**: Image file upload to S3 failed
- **IMAGE_PROCESSING_ERROR**: Image file processing or conversion failed
- **PROMPT_ENHANCEMENT_FAILED**: Image prompt optimization failed
- **TASK_QUEUE_FULL**: Background task queue is at capacity
- **PRESIGNED_URL_GENERATION_FAILED**: S3 presigned URL generation failed
- **IMAGE_FORMAT_ERROR**: Generated image format is incompatible

## Integration Details

### FLUX Image Generation Integration
- Image generation uses FLUX API for high-quality AI image creation
- Scene visual descriptions are enhanced with brand context and style preferences
- Generation parameters include style, quality, aspect ratio, and creative direction
- Image output is optimized for web display and video production use
- Quality settings balance generation time with image resolution and detail

### Image Prompt Enhancement
- Scene descriptions are automatically enhanced with contextual information
- Brand identity and script style are incorporated into generation prompts
- Visual consistency is maintained across scenes within the same script
- Prompt optimization improves generation success rate and quality
- Style templates provide consistent aesthetic across image generations

### AWS S3 Image Storage
- Image files are stored in S3 with hierarchical organization by script and scene
- Presigned URLs provide secure, time-limited access to image content
- Image metadata includes generation parameters and technical specifications
- Storage optimization supports various image formats and resolutions
- Automatic cleanup processes manage storage quotas and retention policies

## Performance Considerations

### Generation Times
- Individual scene image generation: 15-60 seconds depending on complexity and quality settings
- Batch image generation: Optimized for parallel processing of multiple scenes
- Quality settings significantly impact generation time and output file size
- High-resolution images require longer generation times but provide better quality

### Resource Management
- Image generation is GPU-intensive and limited by concurrent processing capacity
- Memory management handles large image files during processing and storage
- Queue prioritization ensures fair resource allocation across users
- Generation load balancing distributes requests across available GPU resources

### Scalability Factors
- Image generation scales with available GPU processing capacity
- Horizontal scaling supports increased concurrent image generation requests
- Background task prioritization ensures responsive user experience
- Monitoring systems track performance metrics and resource utilization
- Caching mechanisms optimize repeated generation requests
