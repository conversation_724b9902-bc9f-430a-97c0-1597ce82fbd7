# VidFlux Backend API Documentation Index

## Overview

This document provides an index of all API documentation for the VidFlux backend microservices. Each service handles specific aspects of the video production pipeline, from content generation to final video output.

## Service Documentation

### 1. Authentication Service
**File**: `auth_service_api_documentation.md`

**Purpose**: User authentication, authorization, and account management

**Endpoints**:
- POST /auth/request-otp - Registration initiation with OTP verification
- POST /auth/verify-otp - Account creation with JWT token generation
- POST /auth/login - Email and password authentication
- POST /auth/refresh - Token refresh mechanism
- GET /auth/me - User profile access

**Key Features**:
- OTP-based user registration
- JWT token management (access and refresh tokens)
- Secure password hashing with bcrypt
- Organization management
- User profile and account status

---

### 2. Script Service
**File**: `script_service_api_documentation.md`

**Purpose**: AI-powered script generation and management using Gemini AI

**Endpoints**:
- POST /scripts/ - Create and generate new script with AI assistance
- GET /scripts/{script_id} - Retrieve script details with optional scene inclusion

**Key Features**:
- Gemini AI integration for intelligent script generation
- Brand context and style integration
- Asynchronous script generation with progress tracking
- Structured script content with scenes and narration
- Script metadata and generation status management

---

### 3. Scene Service
**File**: `scene_service_api_documentation.md`

**Purpose**: Individual scene management within scripts

**Endpoints**:
- GET /scenes/ - List all scenes for a specific script
- PUT /scenes/{scene_id} - Manual scene editing and updates
- POST /scenes/{scene_id}/regenerate - AI-powered scene regeneration based on feedback
- DELETE /scenes/{scene_id} - Scene deletion and cleanup

**Key Features**:
- Scene-level content management
- AI-powered scene regeneration with user feedback
- Manual scene editing capabilities
- Scene ordering and organization
- Scene deletion with cleanup

---

### 4. Audio Service
**File**: `audio_service_api_documentation.md`

**Purpose**: Comprehensive audio generation including background music, ambient sounds, and voiceovers

**Endpoints**:
- POST /audio/background-sound/generate - Generate background sounds for multiple scenes
- GET /audio/background-sound/status/{script_id} - Check background sound generation status
- PUT /audio/background-sound/update-prompt/{scene_id} - Update background sound prompt and regenerate
- POST /audio/background-music/generate - Generate background music for entire script
- GET /audio/background-music/status/{script_id} - Check background music generation status
- PUT /audio/background-music/update-prompt/{script_id} - Update background music prompt and regenerate
- POST /audio/voiceover/scenes/generate - Generate voiceover narration for multiple scenes
- GET /audio/voiceover/status/{script_id} - Check voiceover generation status
- PUT /audio/voiceover/scenes/{scene_id}/prompt - Update voiceover narration and regenerate

**Key Features**:
- Stable Audio integration for background music and ambient sounds
- Text-to-speech for voiceover generation
- Scene-level and script-level audio management
- Prompt-based audio generation with regeneration capabilities
- S3 storage for audio assets with presigned URLs

---

### 5. Image Service
**File**: `image_service_api_documentation.md`

**Purpose**: AI-powered image generation using FLUX technology

**Endpoints**:
- POST /images/generate - Generate images for multiple scenes using AI
- POST /images/regenerate - Regenerate images with updated parameters
- GET /images/{script_id} - Retrieve image generation details for all scenes in script
- GET /images/status/{script_id} - Check image generation status overview
- PUT /images/update-prompt/{scene_id} - Update image generation prompt and regenerate

**Key Features**:
- FLUX AI integration for high-quality image generation
- Scene-based image generation with visual descriptions
- Image regeneration with updated prompts and parameters
- Brand context integration for consistent visual style
- S3 storage for image assets with metadata

---

### 6. Video Service
**File**: `video_service_api_documentation.md`

**Purpose**: AI-powered video generation and stitching using LTX Video technology

**Endpoints**:
- POST /videos/generate - Generate videos for multiple scenes using AI
- GET /videos/{scene_id} - Retrieve video details and status for specific scene
- PUT /videos/update-prompt/{scene_id} - Update video generation prompt and regenerate
- GET /videos/status/{script_id} - Check video generation status for all scenes in script
- POST /stitching/stitch - Stitch multiple scene videos into final combined video
- GET /stitching/status - Check video stitching service health and status

**Key Features**:
- LTX Video integration for AI-powered video generation
- Scene-based video creation with visual descriptions
- Video regeneration with updated prompts
- Video stitching for final production-ready content
- S3 storage for video assets with presigned URLs

---

### 7. Image Overlay Service
**File**: `image_overlay_service_api_documentation.md`

**Purpose**: Brand overlay and logo placement on video content

**Endpoints**:
- POST /image-overlay/simple - Apply simple logo overlay to video content
- POST /image-overlay/option-a - Apply enhanced brand overlay with animation effects
- POST /image-overlay/option-b - Apply advanced brand overlay with complex animations

**Key Features**:
- Multiple overlay complexity levels (simple, enhanced, advanced)
- Logo and brand asset integration
- Animation effects and transitions
- Professional branding with customizable positioning
- Video processing with overlay optimization

---

### 8. Text Overlay Service
**File**: `text_overlay_service_api_documentation.md`

**Purpose**: Text overlays, captions, and textual elements on video content

**Endpoints**:
- POST /text-overlay/process - Apply text overlays with custom styling and animations

**Key Features**:
- Custom text styling with multiple font options
- Animation effects for text appearance
- Synchronized text timing with video content
- Professional typography and readability optimization
- Scene narration integration for automatic text overlay

---

## Common Integration Patterns

### Authentication
All services (except health checks) require JWT authentication via Bearer token in the Authorization header. Authentication is handled by the Auth Service which provides:
- User registration with OTP verification
- JWT access tokens (24-hour expiration)
- JWT refresh tokens (7-day expiration)
- Organization-based access control

### Asynchronous Processing
Most content generation services use asynchronous processing patterns:
- Background task queues (Celery) for resource-intensive operations
- Task progress tracking with unique task IDs
- Status endpoints for monitoring generation progress
- Error handling and retry mechanisms

### AWS S3 Storage
Generated assets are stored in AWS S3 with consistent patterns:
- Hierarchical organization by organization, script, and scene
- Presigned URLs for secure, time-limited access
- Metadata storage for generation parameters
- Automatic cleanup and retention management

### AI Service Integration
Multiple AI services are integrated for content generation:
- **Gemini AI**: Script and scene content generation
- **Stable Audio**: Background music and ambient sound generation
- **FLUX**: High-quality image generation
- **LTX Video**: AI-powered video creation
- **TTS Services**: Voiceover and narration generation

### Database Design
PostgreSQL with SQLAlchemy ORM provides:
- Async database operations
- Multi-tenant architecture with organization isolation
- Asset tracking and versioning
- Generation status and metadata storage
- User and permission management

## Error Handling Standards

### HTTP Status Codes
- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **202 Accepted**: Request accepted for async processing
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required or invalid
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **422 Validation Error**: Request body validation failed
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server-side errors
- **503 Service Unavailable**: Service temporarily unavailable

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required
- **AUTH_INVALID**: Invalid authentication credentials
- **PERMISSION_DENIED**: Insufficient permissions
- **VALIDATION_ERROR**: Input validation failed
- **GENERATION_FAILED**: AI generation process failed
- **S3_UPLOAD_FAILED**: File upload to S3 failed
- **TASK_QUEUE_FULL**: Background task queue at capacity

## Performance Considerations

### Rate Limiting
- API endpoints have rate limiting to prevent abuse
- Generation services have additional quotas due to resource intensity
- Background task queues manage concurrent processing load

### Scalability
- Services are designed for horizontal scaling
- Background workers can be scaled independently
- Database connection pooling for efficient resource usage
- S3 storage provides unlimited capacity

### Monitoring
- Health check endpoints for service status monitoring
- Performance metrics tracking for optimization
- Error logging and alerting for operational visibility

## Getting Started

1. **Authentication**: Start with the Auth Service to create user accounts and obtain JWT tokens
2. **Script Creation**: Use the Script Service to generate AI-powered script content
3. **Asset Generation**: Generate images, audio, and videos using respective services
4. **Post-Processing**: Apply text and image overlays for professional finishing
5. **Final Production**: Use video stitching to create final production-ready content

Each service documentation provides detailed endpoint specifications, examples, and integration guidance for building comprehensive video production workflows.
