# AWS SSM Parameter Store Integration with .env Fallback

This document explains how to use AWS Systems Manager Parameter Store for managing environment variables in VidFlux Backend, with .env files serving as fallback.

## Overview

The application uses a hybrid approach for configuration management:
1. **AWS SSM Parameter Store** (primary) - for production and secure environments
2. **Environment-specific .env files** (fallback) - for local development and when SSM is unavailable

Parameters are named using the format: `{environment}_{PARAMETER_NAME}`

### Example Parameter Names:
- `dev_GEMINI_API_KEY`
- `prod_SECRET_KEY`
- `local_DATABASE_URL`

## Configuration Priority Order

The application follows this priority order when loading configuration:

1. **SSM Parameters** (highest priority)
2. **Environment Variables** (set in shell)
3. **Environment-specific .env files** (`.env.local`, `.env.dev`, `.env.prod`)
4. **Default values** (lowest priority)

## Setup Instructions

### 1. Install Dependencies

```bash
pip install boto3
```

### 2. Configure AWS Credentials

```bash
aws configure
```

Or set environment variables:
```bash
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-east-2
```

### 3. Create SSM Parameters

#### Option A: Using the Setup Script

```bash
# Show what parameters will be created
python scripts/setup_ssm_parameters.py show-keys dev

# Create parameters with default values
python scripts/setup_ssm_parameters.py create dev

# Create parameters with custom values from JSON file
python scripts/setup_ssm_parameters.py create dev --config-file config/dev_params.json
```

#### Option B: Manual Creation via AWS Console

1. Go to AWS Systems Manager → Parameter Store
2. Create parameters with the naming convention: `{environment}_{PARAMETER_NAME}`
3. Use `SecureString` type for sensitive data (SECRET_KEY, JWT_SECRET_KEY, GEMINI_API_KEY)

### 4. Set Environment Variable

Set the `ENVIRONMENT` variable to tell the app which environment to use:

```bash
export ENVIRONMENT=dev
```

### 5. Create .env Files (Optional Fallback)

Create environment-specific .env files for local development:

```bash
# Copy example file
cp env.local.example .env.local

# Edit with your values
nano .env.local
```

## Environment-Specific .env Files

The application automatically loads the appropriate .env file based on the `ENVIRONMENT` variable:

- `ENVIRONMENT=local` → `.env.local`
- `ENVIRONMENT=dev` → `.env.dev`
- `ENVIRONMENT=prod` → `.env.prod`

### Example .env.local Structure

```env
# Environment
ENVIRONMENT=local

# Application secrets
SECRET_KEY=local_secret_key_for_development_only
JWT_SECRET_KEY=local_jwt_secret_key_for_development_only

# Database URLs
DATABASE_URL=postgresql+asyncpg://vidflux_user:vidflux_password@localhost:5432/vidflux_local
DATABASE_URL_SYNC=postgresql://vidflux_user:vidflux_password@localhost:5432/vidflux_local

# Gemini AI
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL_NAME=gemini-pro

# Redis/Celery
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Application settings
DEBUG=true
LOG_LEVEL=debug
```

## Parameter List

The application reads the following parameters from SSM (and falls back to .env files):

### Application Secrets
- `SECRET_KEY` - Secret key for JWT and encryption
- `JWT_SECRET_KEY` - JWT secret key

### Database Configuration
- `DATABASE_URL` - Async database URL
- `DATABASE_URL_SYNC` - Sync database URL for migrations
- `DB_POOL_SIZE` - Database connection pool size
- `DB_MAX_OVERFLOW` - Database connection pool max overflow
- `DB_POOL_TIMEOUT` - Database connection pool timeout
- `DB_POOL_RECYCLE` - Database connection pool recycle time

### Gemini AI
- `GEMINI_API_KEY` - Gemini API key
- `GEMINI_MODEL_NAME` - Gemini model name

### Redis/Celery
- `REDIS_URL` - Redis URL
- `CELERY_BROKER_URL` - Celery broker URL
- `CELERY_RESULT_BACKEND` - Celery result backend URL
- `CELERY_WORKER_CONCURRENCY` - Celery worker concurrency

### JWT Configuration
- `JWT_ALGORITHM` - JWT algorithm
- `JWT_ACCESS_TOKEN_EXPIRE_MINUTES` - JWT access token expiry

### Application Settings
- `DEBUG` - Debug mode
- `LOG_LEVEL` - Log level
- `BACKEND_CORS_ORIGINS` - CORS origins
- `RATE_LIMIT_REQUESTS` - Rate limit requests per minute
- `RATE_LIMIT_WINDOW` - Rate limit window in seconds

### Google OAuth
- `GOOGLE_CLIENT_ID` - Google OAuth client ID

## Environment-Specific Examples

### Development Environment
```bash
# SSM Parameter names for dev environment
dev_SECRET_KEY
dev_JWT_SECRET_KEY
dev_GEMINI_API_KEY
dev_DATABASE_URL
# ... etc

# Or use .env.dev file as fallback
```

### Production Environment
```bash
# SSM Parameter names for prod environment
prod_SECRET_KEY
prod_JWT_SECRET_KEY
prod_GEMINI_API_KEY
prod_DATABASE_URL
# ... etc

# Or use .env.prod file as fallback
```

## Usage Scenarios

### Scenario 1: Production with SSM
- All parameters stored in AWS SSM
- No .env files needed
- Maximum security and scalability

### Scenario 2: Development with SSM + .env Fallback
- Primary configuration from SSM
- .env files for local overrides
- Easy local development

### Scenario 3: Local Development Only
- No AWS credentials configured
- Uses .env files exclusively
- Simple setup for local development

### Scenario 4: Hybrid Approach
- Critical secrets in SSM
- Non-sensitive config in .env files
- Best of both worlds

## Usage in Application

The application automatically:
1. Detects the environment from `ENVIRONMENT` variable
2. Attempts to fetch parameters from SSM
3. Falls back to .env files if SSM is not available
4. Uses environment variables as override
5. Uses default values as last resort

### Priority Order:
1. SSM parameters (highest priority)
2. Environment variables (set in shell)
3. Environment-specific .env files
4. Default values (lowest priority)

## Management Commands

### List Parameters
```bash
python scripts/setup_ssm_parameters.py list dev
```

### Delete Parameters
```bash
python scripts/setup_ssm_parameters.py delete dev
```

### Show Parameter Keys
```bash
python scripts/setup_ssm_parameters.py show-keys dev
```

## Security Best Practices

1. **Use SecureString for sensitive data**: SECRET_KEY, JWT_SECRET_KEY, GEMINI_API_KEY
2. **Limit IAM permissions**: Only grant SSM read access to the application
3. **Use different parameters per environment**: Never share production secrets with development
4. **Rotate secrets regularly**: Update sensitive parameters periodically
5. **Don't commit .env files**: Add them to .gitignore
6. **Use .env files for local development only**: Keep production secrets in SSM

## Troubleshooting

### Common Issues

1. **"AWS credentials not found"**
   - App will fall back to .env files
   - Run `aws configure` or set environment variables
   - Check IAM permissions

2. **"SSM parameter not found"**
   - App will use .env file values
   - Verify parameter names follow the convention: `{environment}_{PARAMETER_NAME}`
   - Check if parameters exist in the correct AWS region

3. **"SSM not available"**
   - App will use .env files and environment variables
   - Check AWS credentials and permissions
   - Verify network connectivity to AWS

### Debug Mode

Enable debug logging to see configuration loading:
```bash
export LOG_LEVEL=debug
```

You'll see logs like:
```
Using SSM parameter: SECRET_KEY
Using local environment variable: DEBUG
Using .env file value: DATABASE_URL
Using default value for: LOG_LEVEL
```

## Migration Strategies

### From .env Files Only
1. Create SSM parameters with the same values
2. Set the `ENVIRONMENT` variable
3. Keep .env files as fallback
4. Gradually move sensitive data to SSM

### From SSM Only
1. Create .env files for local development
2. Set the `ENVIRONMENT` variable
3. App will use SSM in production, .env in local

### Hybrid Migration
1. Keep critical secrets in SSM
2. Use .env files for non-sensitive configuration
3. Set appropriate priorities based on security needs

## Cost Considerations

- SSM Parameter Store charges per parameter per month
- Standard parameters: $0.05 per parameter per month
- Advanced parameters: $0.25 per parameter per month
- API calls: $0.05 per 10,000 API calls

For typical usage, costs are minimal (< $1/month for most applications).

## Benefits of Hybrid Approach

1. **Flexibility**: Works with or without AWS
2. **Security**: Sensitive data in SSM, non-sensitive in .env
3. **Development Friendly**: Easy local setup with .env files
4. **Production Ready**: Scalable SSM for production
5. **Gradual Migration**: Can migrate incrementally
6. **Fallback Safety**: Always has a working configuration 