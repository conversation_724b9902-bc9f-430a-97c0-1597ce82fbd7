# Video Service API Documentation

## Overview

The Video Service handles video generation, management, and stitching using AI-powered video creation technology. It integrates with LTX Video for high-quality video generation and provides comprehensive video processing capabilities.

## API Endpoints

### 1. POST /videos/generate

**Purpose**: Generate videos for multiple scenes using AI

**Description**: Initiates AI-powered video generation for specified scenes using LTX Video technology. Creates high-quality videos based on scene descriptions, visual prompts, and associated image assets.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information from JWT token
- Verifies all specified scene IDs exist and belong to authenticated user
- Retrieves scene descriptions, visual content, and associated image assets
- Validates that scenes have sufficient content for video generation
- Generates unique task IDs for tracking video generation progress
- Creates video asset records with pending status in database
- Queues background tasks for AI-powered video generation using LTX Video
- Returns task information and estimated completion times
- Background process analyzes scene content and generates video prompts
- LTX Video service creates videos based on enhanced prompts and image inputs
- Generated video files are uploaded to S3 storage with metadata
- Scene records are updated with video asset references and generation status

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - scene_ids: Array of scene UUIDs to generate videos for (required)
  - video_style: Preferred video style or aesthetic (optional)
  - duration_per_scene: Target duration for each scene in seconds (optional)
  - resolution: Video resolution preference (optional, default: "1080p")
  - fps: Frames per second setting (optional, default: 24)

**Response Format**:
- **Success (201)**:
  - scene_ids: Array of scene IDs being processed for video generation
  - task_ids: Array of background task identifiers
  - message: Confirmation of video generation initiation
  - estimated_completion_time: Estimated processing time in seconds
  - status: Initial generation status
- **Error (400)**: Invalid scene IDs or insufficient scene content

**Error Handling**:
- **400 Bad Request**: Invalid scene IDs, empty scene list, or scenes lacking visual descriptions
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: One or more scenes do not exist or user lacks access
- **422 Validation Error**: Request body validation failed for required fields
- **429 Too Many Requests**: Rate limit exceeded for video generation
- **500 Internal Server Error**: LTX Video service errors, S3 upload failures, or database errors

**Dependencies**:
- LTX Video service for AI-powered video generation
- PostgreSQL database for scene, image_asset, and video_asset operations
- AWS S3 for video file storage and management
- Background task queue for asynchronous processing
- Image asset integration for video input enhancement

**Business Rules**:
- Users can only generate videos for scenes within their own scripts and organization
- Scenes must have visual descriptions to generate videos
- Each scene can have multiple video versions with different parameters
- Video generation considers associated image assets as input material
- Maximum of 5 scenes can be processed in a single request due to resource intensity
- Generation process runs asynchronously to prevent API timeouts

---

### 2. GET /videos/{scene_id}

**Purpose**: Retrieve video details and status for specific scene

**Description**: Fetches comprehensive video information for a specific scene including generation status, video metadata, and access URLs for generated video content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to authenticated user through script ownership
- Retrieves video asset records associated with the scene
- Generates fresh S3 presigned URLs for video file access
- Compiles video metadata including generation parameters and file information
- Returns detailed video information with access URLs and status
- Includes generation history and error information if applicable

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to retrieve video details for (required)

**Response Format**:
- **Success (200)**:
  - scene_id: Scene unique identifier
  - video_assets: Array of video asset objects containing:
    - asset_id: Video asset unique identifier
    - video_url: S3 presigned URL for video file access
    - status: Generation status ("pending", "generating", "completed", "failed")
    - file_size: Video file size in bytes
    - duration: Video duration in seconds
    - resolution: Video resolution (e.g., "1920x1080")
    - fps: Frames per second
    - format: Video file format (e.g., "mp4")
    - created_at: Generation timestamp
    - generation_metadata: Object containing generation parameters
  - scene_metadata: Scene information including visual description and prompts
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user lacks access through script ownership
- **500 Internal Server Error**: Database errors, S3 access failures, or presigned URL generation issues

**Dependencies**:
- PostgreSQL database for scene and video_asset lookup
- AWS S3 for presigned URL generation and video file access
- Video generation status tracking system
- Scene ownership verification through script relationships

**Business Rules**:
- Users can only access video details for scenes in scripts they own
- Presigned URLs are generated with 2-hour expiration for video streaming
- Video metadata includes both current and historical generation attempts
- Multiple video versions per scene are supported with version tracking
- Failed generations include specific error messages for debugging

---

### 3. PUT /videos/update-prompt/{scene_id}

**Purpose**: Update video generation prompt and regenerate video

**Description**: Updates the visual description and generation parameters for a specific scene and triggers immediate video regeneration with the new creative direction. Enables iterative refinement of video content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies scene exists and belongs to user through script ownership
- Updates scene visual description and video generation parameters
- Validates updated prompts for video generation compatibility
- Marks previous video generation as superseded
- Generates unique task ID for video regeneration tracking
- Queues new background task for LTX Video generation with updated parameters
- Creates new video asset record for the regenerated video
- Returns task information for tracking regeneration progress
- Background process uses updated prompts to generate new video content
- New video file replaces previous version with updated metadata

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - scene_id: UUID of the scene to update video prompt for (required)
- **Request Body** (JSON):
  - visual_description: Updated visual description for video generation (required)
  - video_style: Updated video style preference (optional)
  - duration: Updated target duration in seconds (optional)
  - additional_prompts: Additional creative direction for video generation (optional)

**Response Format**:
- **Success (200)**:
  - scene_id: Scene identifier
  - task_id: Background task identifier for regeneration tracking
  - message: Confirmation of prompt update and regeneration initiation
  - status: Updated generation status
  - estimated_completion_time: Estimated regeneration time in seconds
  - updated_prompts: Object containing the updated generation parameters
- **Error (404)**: Scene not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Scene does not exist or user lacks access through script ownership
- **422 Validation Error**: Missing, empty, or invalid visual description
- **429 Too Many Requests**: Rate limit exceeded for video regeneration
- **500 Internal Server Error**: Database update errors, task queue failures, or video service errors

**Dependencies**:
- LTX Video service for video regeneration with updated prompts
- PostgreSQL database for scene and video_asset management
- Background task queue for asynchronous regeneration processing
- S3 storage for new video file upload and management
- Scene ownership verification through script relationships

**Business Rules**:
- Users can only update video prompts for scenes in scripts they own
- Prompt updates trigger immediate video regeneration
- Previous video versions are preserved but marked as superseded
- Regeneration uses same technical parameters unless explicitly updated
- Visual descriptions must contain sufficient detail for video generation
- Regeneration queue is prioritized to prevent long wait times

---

### 4. GET /videos/status/{script_id}

**Purpose**: Check video generation status for all scenes in script

**Description**: Retrieves comprehensive video generation status for all scenes within a specified script, providing overview of video production progress and access to generated content.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves all scenes associated with the script
- Checks video generation status for each scene
- Generates S3 presigned URLs for completed video files
- Calculates overall script video generation progress
- Compiles scene-by-scene video status information
- Returns comprehensive video generation status report ordered by scene number

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Path Parameters**:
  - script_id: UUID of the script to check video status for (required)

**Response Format**:
- **Success (200)**:
  - script_id: Script identifier
  - overall_status: Overall video generation status for the script
  - progress_percentage: Percentage of scenes with completed video generation
  - scenes: Array of scene video status objects containing:
    - scene_id: Scene unique identifier
    - scene_number: Sequential scene number within script
    - video_status: Generation status ("pending", "generating", "completed", "failed")
    - video_url: S3 presigned URL for video file (if completed)
    - file_size: Video file size in bytes
    - duration: Video duration in seconds
    - error_message: Error description for failed generations
    - generated_at: Timestamp of successful video generation
- **Error (404)**: Script not found or access denied

**Error Handling**:
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **500 Internal Server Error**: Database errors, S3 access failures, or status compilation errors

**Dependencies**:
- PostgreSQL database for script, scene, and video_asset lookup
- AWS S3 for presigned URL generation and video file access
- Video generation status tracking and progress calculation
- Script ownership verification and scene enumeration

**Business Rules**:
- Users can only check video status for scripts they own
- Status includes scenes without video generation attempts
- Presigned URLs expire after 2 hours for video streaming compatibility
- Progress calculation excludes scenes without visual descriptions
- Overall status reflects completion state of all eligible scenes
- Video files remain accessible until scene or script deletion

---

### 5. POST /stitching/stitch

**Purpose**: Stitch multiple scene videos into final combined video

**Description**: Combines individual scene videos into a single cohesive final video with proper transitions, audio synchronization, and consistent formatting. Creates production-ready video content from scene components.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates user authentication and extracts user information
- Verifies script exists and belongs to authenticated user
- Retrieves all scene videos associated with the script in proper order
- Validates that all required scene videos are available and accessible
- Checks video format compatibility and resolution consistency
- Queues background task for video stitching and rendering
- Creates final video asset record with processing status
- Returns task information for tracking stitching progress
- Background process downloads scene videos from S3 storage
- Video stitching engine combines scenes with transitions and audio
- Final rendered video is uploaded to S3 with comprehensive metadata
- Script record is updated with final video asset reference

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **Request Body** (JSON):
  - script_id: Script identifier for video stitching (required)
  - include_transitions: Boolean flag for scene transitions (optional, default: true)
  - output_format: Desired output video format (optional, default: "mp4")
  - quality: Output quality setting (optional, default: "high")

**Response Format**:
- **Success (200)**:
  - success: Boolean indicating successful stitching initiation
  - message: Confirmation of video stitching completion or initiation
  - script_id: Script identifier
  - output_path: Path to the final stitched video file
  - task_id: Background task identifier for progress tracking
  - estimated_completion_time: Estimated stitching time in seconds
- **Error (400)**: Insufficient scene videos or incompatible formats

**Error Handling**:
- **400 Bad Request**: Insufficient scene videos, incompatible video formats, or missing required scenes
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Script does not exist or user lacks access
- **422 Validation Error**: Invalid stitching parameters or output format
- **500 Internal Server Error**: Video stitching failures, S3 access errors, or rendering issues

**Dependencies**:
- Video stitching and rendering engine (FFmpeg or similar)
- PostgreSQL database for script, scene, and video_asset operations
- AWS S3 for scene video download and final video upload
- Background task queue for asynchronous video processing
- Audio synchronization and transition effects system

**Business Rules**:
- Users can only stitch videos for scripts they own
- All scenes must have generated videos before stitching
- Video stitching maintains scene order based on scene numbers
- Final video includes background music and voiceover audio tracks
- Stitching process optimizes video quality while managing file size
- Failed stitching attempts preserve individual scene videos

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication token
- **SCENE_NOT_FOUND**: Requested scene does not exist or access denied
- **SCRIPT_NOT_FOUND**: Requested script does not exist or access denied
- **VALIDATION_ERROR**: Input validation failed for required fields
- **PERMISSION_DENIED**: User does not have permission to access resource

### Service-Specific Error Codes
- **VIDEO_GENERATION_FAILED**: Video generation process failed
- **LTX_VIDEO_ERROR**: LTX Video service returned error
- **INSUFFICIENT_SCENE_CONTENT**: Scene lacks visual description for video generation
- **VIDEO_STITCHING_FAILED**: Video stitching process failed
- **S3_UPLOAD_FAILED**: Video file upload to S3 failed
- **INCOMPATIBLE_VIDEO_FORMATS**: Scene videos have incompatible formats for stitching
- **VIDEO_PROCESSING_ERROR**: Video file processing or conversion failed
- **RENDERING_ENGINE_ERROR**: Video rendering engine encountered error
- **PRESIGNED_URL_GENERATION_FAILED**: S3 presigned URL generation failed

## Integration Details

### LTX Video Integration
- Video generation uses LTX Video API for high-quality AI video creation
- Scene descriptions and image assets are combined for enhanced video prompts
- Generation parameters include style, duration, resolution, and creative direction
- Video output is optimized for web streaming and production use
- Quality settings balance generation time with output quality

### Video Processing Pipeline
- Scene videos are processed through standardized rendering pipeline
- Format standardization ensures compatibility across different generation attempts
- Video metadata includes generation parameters and technical specifications
- Quality optimization balances file size with visual fidelity
- Audio integration supports background music and voiceover synchronization

### AWS S3 Video Storage
- Video files are stored in S3 with hierarchical organization by script and scene
- Presigned URLs provide secure, time-limited access to video content
- Video metadata includes generation parameters and technical specifications
- Storage optimization manages large video file sizes efficiently
- Automatic cleanup processes manage storage quotas and retention policies

## Performance Considerations

### Generation Times
- Individual scene video generation: 120-600 seconds depending on duration and complexity
- Video stitching: 60-300 seconds depending on number of scenes and final video length
- Quality settings significantly impact generation time and output file size
- Batch processing optimizes resource usage for multiple scene generations

### Resource Management
- Video generation is resource-intensive and limited by concurrent processing capacity
- GPU resources are allocated dynamically based on generation queue load
- Memory management handles large video files during processing and stitching
- Storage optimization manages video file sizes while maintaining quality

### Scalability Factors
- Video generation scales with available GPU processing capacity
- Horizontal scaling supports increased concurrent video generation requests
- Background task prioritization ensures fair resource allocation
- Monitoring systems track performance metrics and resource utilization
