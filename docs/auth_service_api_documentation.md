# Authentication Service API Documentation

## Overview

The Authentication Service handles user registration, login, token management, and user profile access. It provides secure authentication mechanisms including OTP verification, JWT token management, and OAuth integration.

## API Endpoints

### 1. POST /auth/request-otp

**Purpose**: Registration initiation with OTP verification

**Description**: Initiates the user registration process by generating and sending a 6-digit OTP to the provided email address. User data is temporarily stored in the pending_user table until OTP verification is completed.

**Authentication**: Not required (public endpoint)

**Process Flow**:
- Validates that the email address is not already registered with an active account
- Removes any existing pending registration for the same email address
- Hashes the provided password using secure bcrypt algorithm
- Stores user data temporarily in the pending_user table
- Generates a random 6-digit numeric OTP code
- Sets OTP expiration time to 10 minutes from generation
- Stores OTP in the system (currently returns in response for development)
- In production, sends OTP via email to the provided address

**Request Parameters**:
- **Request Body** (JSON):
  - email: Valid email address (required, must be unique)
  - password: Password string (required, minimum 8 characters)
  - first_name: User's first name (required)
  - last_name: User's last name (required)
  - org_name: Organization name (required)

**Response Format**:
- **Success (200)**: 
  - message: Confirmation message
  - otp: 6-digit code (development only)
  - email: Email address where OTP was sent
- **Error (400)**: Email already registered with active account
- **Error (422)**: Validation errors for input fields

**Error Handling**:
- **400 Bad Request**: Email address is already registered with an active user account
- **422 Validation Error**: Invalid email format, password too short, or missing required fields
- **500 Internal Server Error**: Database connection errors, password hashing failures, or OTP generation issues

**Dependencies**:
- PostgreSQL database for user and pending_user table operations
- Password hashing utility using bcrypt algorithm
- OTP generation and storage system with expiration management
- Email validation and format checking

**Business Rules**:
- Email addresses must be unique across the entire system
- Passwords must contain at least 8 characters
- OTP codes expire automatically after 10 minutes
- Only one pending registration is allowed per email address at any time
- Existing pending registrations are replaced when new OTP is requested
- Organization names are stored but not validated for uniqueness

---

### 2. POST /auth/verify-otp

**Purpose**: Account creation with JWT token generation

**Description**: Completes the user registration process by verifying the OTP code and creating the actual user account. Generates JWT access and refresh tokens for immediate authentication.

**Authentication**: Not required (public endpoint)

**Process Flow**:
- Validates the provided OTP against stored OTP for the email address
- Checks that OTP has not expired (10-minute window)
- Retrieves pending user data from temporary storage
- Generates unique organization ID with "org-" prefix
- Creates new organization record in the database
- Creates new user account with verified status
- Creates initial signin record for tracking
- Removes pending user data from temporary storage
- Generates JWT access token with 24-hour expiration
- Generates JWT refresh token with 7-day expiration
- Stores refresh token in database with expiration tracking
- Returns both tokens for immediate authentication

**Request Parameters**:
- **Request Body** (JSON):
  - email: Email address used in registration (required)
  - otp: 6-digit OTP code received via email (required)

**Response Format**:
- **Success (200)**:
  - access_token: JWT token for API authentication
  - refresh_token: JWT token for token renewal
  - token_type: Always "bearer"
  - expires_in: Access token expiration time in seconds
  - user_info: Object containing user details
  - refresh_token_created_at: Timestamp of refresh token creation
  - refresh_token_updated_at: Timestamp of refresh token last update
- **Error (400)**: Invalid or expired OTP code
- **Error (404)**: Pending user data not found

**Error Handling**:
- **400 Bad Request**: Invalid OTP code, expired OTP, or OTP verification failed
- **404 Not Found**: Pending user data not found (may have expired or been removed)
- **422 Validation Error**: Invalid email format or OTP format
- **500 Internal Server Error**: Database errors during account creation, organization creation, or token generation

**Dependencies**:
- OTP verification system with expiration checking
- PostgreSQL database for user, organization, signin, and refresh_token tables
- JWT token generation system with configurable expiration times
- UUID generation for unique identifiers

**Business Rules**:
- OTP codes can only be used once and expire after 10 minutes
- Each successful verification creates exactly one user account
- Organization IDs are auto-generated with "org-" prefix plus 8 random characters
- User accounts are created as active and verified immediately
- Access tokens expire in 24 hours, refresh tokens in 7 days
- Signin records are created for user activity tracking

---

### 3. POST /auth/login

**Purpose**: Email and password authentication

**Description**: Authenticates existing users using email and password credentials. Generates new JWT token pair for authenticated access to the API.

**Authentication**: Not required (public endpoint)

**Process Flow**:
- Looks up user account by email address
- Verifies provided password against stored password hash using bcrypt
- Checks that user account is active and not disabled
- Creates new signin record for tracking user activity
- Generates new JWT access token with 24-hour expiration
- Generates new JWT refresh token with 7-day expiration
- Stores refresh token in database with expiration tracking
- Returns token pair with user information

**Request Parameters**:
- **Request Body** (JSON):
  - email: Registered email address (required)
  - password: User's password (required)

**Response Format**:
- **Success (200)**:
  - access_token: JWT token for API authentication
  - refresh_token: JWT token for token renewal
  - token_type: Always "bearer"
  - expires_in: Access token expiration time in seconds
  - user_info: Object containing user details (org_id, user_id, email, first_name, last_name)
  - refresh_token_created_at: Timestamp of refresh token creation
  - refresh_token_updated_at: Timestamp of refresh token last update
- **Error (401)**: Invalid credentials or disabled account

**Error Handling**:
- **401 Unauthorized**: Invalid email or password combination
- **401 Unauthorized**: User account is disabled or inactive
- **422 Validation Error**: Invalid email format or missing required fields
- **500 Internal Server Error**: Database connection errors, password verification failures, or token generation issues

**Dependencies**:
- Password verification system using bcrypt algorithm
- PostgreSQL database for user, signin, and refresh_token operations
- JWT token generation system with configurable expiration
- User account status validation

**Business Rules**:
- Email and password must match exactly with stored credentials
- Only active user accounts can authenticate successfully
- Each login creates a new signin record for audit purposes
- Access tokens expire in 24 hours, refresh tokens in 7 days
- Multiple active sessions are allowed per user account
- Password verification uses secure timing-resistant comparison

---

### 4. POST /auth/refresh

**Purpose**: Token refresh mechanism

**Description**: Generates new access and refresh tokens using a valid refresh token. Maintains authenticated sessions without requiring users to re-enter credentials.

**Authentication**: Requires valid refresh token

**Process Flow**:
- Validates and decodes the provided refresh token
- Verifies token signature and expiration status
- Checks that token type is specifically "refresh"
- Looks up refresh token in database to ensure it hasn't been revoked
- Verifies that associated user account is still active
- Generates new access token with fresh 24-hour expiration
- Generates new refresh token with fresh 7-day expiration
- Updates refresh token record in database
- Marks old refresh token as used/revoked
- Returns new token pair

**Request Parameters**:
- **Request Body** (JSON):
  - refresh_token: Valid JWT refresh token (required)

**Response Format**:
- **Success (200)**:
  - access_token: New JWT token for API authentication
  - refresh_token: New JWT token for future renewal
  - token_type: Always "bearer"
  - expires_in: Access token expiration time in seconds
  - user_info: Object containing user details
  - refresh_token_created_at: Timestamp of new refresh token creation
  - refresh_token_updated_at: Timestamp of new refresh token creation
- **Error (401)**: Invalid, expired, or revoked refresh token

**Error Handling**:
- **401 Unauthorized**: Invalid refresh token format or signature
- **401 Unauthorized**: Expired refresh token
- **401 Unauthorized**: Token type is not "refresh"
- **401 Unauthorized**: Refresh token not found in database or already revoked
- **401 Unauthorized**: Associated user account is disabled or inactive
- **422 Validation Error**: Missing refresh_token in request body
- **500 Internal Server Error**: Database errors during token refresh or user lookup

**Dependencies**:
- JWT token verification and generation system
- PostgreSQL database for refresh_token and user table operations
- Token signature validation with secret key
- User account status verification

**Business Rules**:
- Refresh tokens are single-use (new one generated each time)
- Old refresh tokens are marked as revoked after successful refresh
- Access tokens expire in 24 hours, refresh tokens in 7 days
- Disabled user accounts cannot refresh tokens
- Only "refresh" type tokens are accepted for this endpoint
- Token signatures must be valid and generated by the system

---

### 5. GET /auth/me

**Purpose**: User profile access

**Description**: Retrieves the authenticated user's complete profile information including personal details, organization information, and account status. Serves as a template for implementing protected endpoints.

**Authentication**: Required (Bearer token in Authorization header)

**Process Flow**:
- Validates JWT access token from Authorization header
- Extracts user and organization IDs from token payload
- Looks up complete user record in database
- Retrieves associated organization information
- Formats and returns comprehensive user profile data
- Excludes sensitive information like password hashes

**Request Parameters**:
- **Headers**:
  - Authorization: Bearer {access_token} (required)
- **No request body required**

**Response Format**:
- **Success (200)**:
  - user_id: Unique user identifier
  - email: User's email address
  - first_name: User's first name
  - last_name: User's last name
  - org_id: Organization identifier
  - org_name: Organization name
  - is_active: Account active status
  - is_verified: Email verification status
  - created_at: Account creation timestamp
- **Error (401)**: Invalid or expired access token
- **Error (404)**: User account not found

**Error Handling**:
- **401 Unauthorized**: Missing Authorization header
- **401 Unauthorized**: Invalid or malformed JWT token
- **401 Unauthorized**: Expired access token
- **404 Not Found**: User account no longer exists in database
- **500 Internal Server Error**: Database errors during profile lookup

**Dependencies**:
- JWT token validation system with signature verification
- PostgreSQL database for user and organization data retrieval
- Authentication middleware for token extraction and validation
- User account status checking

**Business Rules**:
- Only returns data for the authenticated user (no cross-user access)
- Sensitive information like password hashes are excluded from response
- Organization information is included if user belongs to an organization
- Account status and verification status are included for client decision making
- Token must be valid and not expired for successful access

## Error Code Standards

### Common Error Codes
- **AUTH_REQUIRED**: Authentication token required but not provided
- **AUTH_INVALID**: Invalid or malformed authentication credentials
- **AUTH_EXPIRED**: Authentication token has expired
- **PERMISSION_DENIED**: Insufficient permissions for requested operation
- **USER_NOT_FOUND**: User account does not exist
- **EMAIL_ALREADY_EXISTS**: Email address is already registered
- **INVALID_CREDENTIALS**: Email/password combination is incorrect
- **ACCOUNT_DISABLED**: User account has been disabled
- **OTP_INVALID**: OTP code is incorrect or expired
- **OTP_EXPIRED**: OTP code has exceeded 10-minute expiration window
- **TOKEN_REVOKED**: Refresh token has been revoked or already used

### Service-Specific Error Codes
- **SIGNUP_DATA_NOT_FOUND**: Pending user registration data not found
- **REFRESH_TOKEN_INVALID**: Refresh token is invalid or not found in database
- **ORGANIZATION_CREATION_FAILED**: Error creating organization during registration
- **PASSWORD_HASH_FAILED**: Error during password hashing process
- **TOKEN_GENERATION_FAILED**: Error generating JWT tokens

## Security Considerations

### Password Security
- Passwords are hashed using bcrypt algorithm with salt
- Minimum password length of 8 characters enforced
- Password verification uses timing-resistant comparison
- Password hashes are never returned in API responses

### Token Security
- JWT tokens use HS256 algorithm with secret key
- Access tokens have short expiration (24 hours)
- Refresh tokens have longer expiration (7 days) but are single-use
- Token signatures are validated on every request
- Refresh tokens are stored in database for revocation capability

### Data Protection
- Email addresses are unique and case-insensitive
- User data includes soft delete capabilities
- Audit trails are maintained through signin records
- Sensitive operations require valid authentication
- Cross-user data access is prevented through ownership checks
