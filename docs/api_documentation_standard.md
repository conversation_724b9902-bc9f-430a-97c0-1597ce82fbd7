# VidFlux API Documentation Standard

## 📋 Overview

This document defines the standard format for documenting all API endpoints in the VidFlux backend. Consistent documentation ensures maintainability, ease of use, and better developer experience.

## 🎯 Documentation Template

### 1. **Endpoint Function Docstring Structure**

```python
@router.post("/service/action", 
    summary="Brief one-line description",
    description="Detailed description of what this endpoint does",
    response_description="Description of successful response",
    tags=["Service Category"]
)
async def endpoint_function_name(
    request: RequestModel,
    user_info: tuple = Depends(require_auth),
    session: AsyncSession = Depends(get_database_session)
):
    """
    🎯 **Purpose**: Clear description of what this endpoint accomplishes
    
    📝 **Description**: 
    Detailed explanation of the endpoint's functionality, business logic, 
    and any important behavioral details.
    
    🔐 **Authentication**: Required/Optional (specify type: Bearer token, API key, etc.)
    
    🔄 **Process Flow**:
    1. Step-by-step description of what happens
    2. Include validation steps
    3. External API calls (Gemini, Stable Audio, etc.)
    4. Database operations
    5. File storage operations
    6. Response generation
    
    📥 **Request Parameters**:
    - **Path Parameters**: Description of URL path variables
    - **Query Parameters**: Description of URL query parameters  
    - **Request Body**: Description of JSON payload requirements
    
    📤 **Response Format**:
    - **Success (200)**: Description of successful response
    - **Error Codes**: List of possible error scenarios
    
    ⚠️ **Error Handling**:
    - **400**: Bad Request - Invalid input parameters
    - **401**: Unauthorized - Authentication required
    - **403**: Forbidden - Insufficient permissions
    - **404**: Not Found - Resource doesn't exist
    - **500**: Internal Server Error - Server-side failures
    
    🔗 **Dependencies**:
    - External services (Gemini AI, Stable Audio, S3, etc.)
    - Database tables
    - Other internal services
    
    📊 **Business Rules**:
    - Any specific business logic constraints
    - Rate limiting information
    - Usage quotas or restrictions
    
    💡 **Examples**:
    
    **Request Example**:
    ```json
    {
        "field1": "example_value",
        "field2": 123,
        "optional_field": "optional_value"
    }
    ```
    
    **Success Response Example**:
    ```json
    {
        "status": "success",
        "data": {
            "result_field": "result_value",
            "url": "https://example.com/resource"
        },
        "message": "Operation completed successfully"
    }
    ```
    
    **Error Response Example**:
    ```json
    {
        "status": "error", 
        "message": "Descriptive error message",
        "error_code": "SPECIFIC_ERROR_CODE"
    }
    ```
    
    📅 **Version**: v2.0.0
    📚 **Related Endpoints**: Links to related functionality
    """
```

### 2. **Pydantic Model Documentation**

```python
class RequestModel(BaseModel):
    """
    📝 **Description**: Request model for [endpoint name]
    
    🔍 **Validation Rules**: List any special validation requirements
    """
    
    field1: str = Field(
        ..., 
        description="Clear description of what this field represents",
        example="example_value",
        min_length=1,
        max_length=255
    )
    
    field2: int = Field(
        ...,
        description="Numeric field description with constraints",
        example=30,
        ge=1,  # Greater than or equal to 1
        le=3600  # Less than or equal to 3600
    )
    
    optional_field: Optional[str] = Field(
        None,
        description="Optional field description with default behavior",
        example="optional_example"
    )
    
    class Config:
        """Configuration for the Pydantic model"""
        schema_extra = {
            "example": {
                "field1": "example_value",
                "field2": 30,
                "optional_field": "optional_example"
            }
        }

class ResponseModel(BaseModel):
    """
    📝 **Description**: Response model for [endpoint name]
    
    🎯 **Purpose**: Standardized response format
    """
    
    status: str = Field(
        ...,
        description="Response status: 'success' or 'error'",
        example="success"
    )
    
    data: Optional[Dict[str, Any]] = Field(
        None,
        description="Response data containing the requested information",
        example={"result": "example_data"}
    )
    
    message: Optional[str] = Field(
        None,
        description="Human-readable message about the operation",
        example="Operation completed successfully"
    )
    
    error_code: Optional[str] = Field(
        None,
        description="Specific error code for debugging (only present on errors)",
        example="VALIDATION_ERROR"
    )
```

### 3. **Service Categories & Tags**

#### **Audio Service** 🎵
- `Background Music` - Script-level background music generation
- `Background Sound` - Scene-level ambient sounds  
- `Text-to-Speech` - Voiceover generation
- `Audio Management` - Audio asset operations

#### **Video Service** 🎬
- `Video Generation` - LTX video creation
- `Video Stitching` - Combining video segments
- `Video Management` - Video asset operations

#### **Image Service** 🖼️
- `Image Generation` - FLUX image creation
- `Image Management` - Image asset operations
- `Image Overlay` - Image composition

#### **Script Service** 📝
- `Script Generation` - Gemini script creation
- `Script Management` - Script CRUD operations
- `Scene Management` - Scene operations

#### **Auth Service** 🔐
- `Authentication` - Login/logout operations
- `Authorization` - Permission management
- `User Management` - User operations

#### **Utility Services** 🛠️
- `Health Checks` - System status
- `File Operations` - Upload/download
- `Text Overlay` - Text composition

### 4. **Error Code Standards**

#### **Format**: `SERVICE_CATEGORY_ERROR`

#### **Common Error Codes**:
- `AUTH_REQUIRED` - Authentication needed
- `AUTH_INVALID` - Invalid credentials
- `PERMISSION_DENIED` - Insufficient permissions
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `VALIDATION_ERROR` - Input validation failed
- `GENERATION_FAILED` - AI generation failed
- `UPLOAD_FAILED` - File upload failed
- `QUOTA_EXCEEDED` - Rate limit or quota exceeded
- `EXTERNAL_SERVICE_ERROR` - Third-party service failure

#### **Service-Specific Codes**:
- `AUDIO_GENERATION_FAILED` - Audio generation specific error
- `VIDEO_GENERATION_FAILED` - Video generation specific error
- `SCRIPT_ANALYSIS_FAILED` - Gemini analysis error
- `S3_UPLOAD_FAILED` - S3 storage error

### 5. **Documentation Checklist**

#### **Before Documenting**:
- [ ] Understand the endpoint's complete functionality
- [ ] Test the endpoint with various inputs
- [ ] Identify all possible error scenarios
- [ ] Document external dependencies
- [ ] Create example requests/responses

#### **Documentation Requirements**:
- [ ] Clear, descriptive summary
- [ ] Detailed process flow
- [ ] All parameters documented
- [ ] Error codes and scenarios
- [ ] Request/response examples
- [ ] Business rules and constraints
- [ ] Dependencies and integrations

#### **Quality Checks**:
- [ ] Examples are accurate and tested
- [ ] Error messages are helpful
- [ ] Field descriptions are clear
- [ ] Tags are appropriate
- [ ] Related endpoints are linked

### 6. **Implementation Guidelines**

#### **FastAPI Router Configuration**:
```python
router = APIRouter(
    prefix="/service",
    tags=["Service Category"],
    dependencies=[Depends(some_dependency)],
    responses={
        400: {"description": "Bad Request"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal Server Error"}
    }
)
```

#### **Endpoint Decorator Best Practices**:
```python
@router.post("/endpoint",
    summary="Brief description (< 60 chars)",
    description="Detailed description of functionality",
    response_description="Description of successful response",
    status_code=200,
    responses={
        400: {
            "description": "Validation error",
            "content": {
                "application/json": {
                    "example": {"status": "error", "message": "Invalid input"}
                }
            }
        }
    }
)
```

### 7. **Documentation Maintenance**

#### **Version Control**:
- Update documentation when endpoints change
- Include version information in docstrings
- Document breaking changes clearly

#### **Review Process**:
- Peer review documentation changes
- Test examples before committing
- Validate Swagger UI output

#### **Continuous Improvement**:
- Gather feedback from API users
- Update examples based on common use cases
- Improve error messages based on support tickets

---

## 🚀 Next Steps

1. **Apply this template** to existing Audio Service endpoints
2. **Create service-specific** documentation guides
3. **Implement documentation** for each service systematically
4. **Validate Swagger UI** output for completeness
5. **Establish review process** for documentation updates

---

**📝 Note**: This standard should be followed consistently across all VidFlux API endpoints to ensure a uniform and professional documentation experience.
