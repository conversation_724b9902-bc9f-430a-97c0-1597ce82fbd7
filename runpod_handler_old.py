"""
RunPod Serverless Handler for VidFlux Video Stitching
Handles both video stitching and audio mixing operations
"""

import runpod
import os
import sys
import asyncio
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional

# Add the src directory to Python path for imports
sys.path.append("/app/src")

# Import our existing services
from src.video_service.services.video_stitcher import VideoStitcher
from src.video_service.services.audio_stitcher import EnhancedVideoAudioMixer
from src.shared.utils.s3_client import S3Client
from src.shared.models.database_models import VideoAsset, Scene, AudioAsset, TaskQueue, Script
from src.shared.config.database import db_manager
from sqlalchemy import and_, select, desc, update
from sqlalchemy.ext.asyncio import AsyncSession

# Initialize services
video_stitcher = VideoStitcher()
audio_mixer = EnhancedVideoAudioMixer(output_dir="/tmp/final_videos")
s3_client = S3Client(os.getenv("AWS_DEFAULT_REGION", "us-east-2"))


async def update_task_progress(
    task_id: str, progress: int, status: str = None, error_message: str = None, result: dict = None
):
    """Update task progress in database"""
    try:
        async for session in db_manager.get_session():
            query = select(TaskQueue).where(TaskQueue.task_id == task_id)
            result_obj = await session.execute(query)
            task = result_obj.scalar_one_or_none()

            if task:
                task.progress = progress
                if status:
                    task.status = status
                if error_message:
                    task.error_message = error_message
                if result:
                    task.result = result
                if status == "completed":
                    task.completed_at = datetime.utcnow()
                elif status == "processing" and not task.started_at:
                    task.started_at = datetime.utcnow()

                await session.commit()
                print(f"Updated task {task_id}: progress={progress}, status={status}")
            break  # Exit the async generator after first iteration
    except Exception as e:
        print(f"Failed to update task progress: {str(e)}")


async def stitch_videos_async(
    script_id: str,
    org_id: str,
    task_id: str,
    enable_ducking: bool = True,
    enable_ai_enhancement: bool = False,
    auto_audio_stitch: bool = True,
) -> Dict[str, Any]:
    """
    Perform video stitching with optional audio mixing
    """
    try:
        # Update task status to processing
        await update_task_progress(task_id, 10, "processing")
        runpod.serverless.progress_update({"id": task_id}, "Starting video stitching...")

        # Step 1: Get script and scenes from database
        async for session in db_manager.get_session():
            # Get script
            script_query = select(Script).where(
                and_(Script.id == script_id, Script.org_id == org_id)
            )
            result = await session.execute(script_query)
            script = result.scalar_one_or_none()

            if not script:
                raise Exception(f"Script {script_id} not found")

            # Get scenes
            scenes_query = (
                select(Scene).where(Scene.script_id == script_id).order_by(Scene.scene_number)
            )

            result = await session.execute(scenes_query)
            scenes = result.scalars().all()

            if not scenes:
                raise Exception("No scenes found for script")

            await update_task_progress(task_id, 20, "processing")
            runpod.serverless.progress_update(
                {"id": task_id}, f"Found {len(scenes)} scenes to stitch"
            )

            # Step 2: Download video files from S3
            video_paths = []
            s3_bucket = os.getenv("AWS_S3_BUCKET", "assets-vidflux")

            for i, scene in enumerate(scenes):
                # Get the latest video asset for this scene
                video_query = (
                    select(VideoAsset)
                    .where(
                        and_(
                            VideoAsset.scene_id == scene.id,
                            VideoAsset.org_id == org_id,
                            VideoAsset.deleted_at.is_(None),
                        )
                    )
                    .order_by(desc(VideoAsset.created_at))
                )

                result = await session.execute(video_query)
                video_asset = result.scalar_one_or_none()

                if not video_asset or not video_asset.s3_url:
                    raise Exception(f"No video found for scene {scene.scene_number}")

                # Download video file
                local_path = f"/tmp/videos/scene_{scene.scene_number}_{scene.id}.mp4"
                os.makedirs(os.path.dirname(local_path), exist_ok=True)

                # Extract S3 key from URL
                key = video_asset.s3_url.replace(
                    f"https://{s3_bucket}.s3.{os.getenv('AWS_DEFAULT_REGION', 'us-east-2')}.amazonaws.com/",
                    "",
                )

                print(f"Downloading video {i+1}/{len(scenes)}: {key}")
                s3_client.download_file(s3_bucket, key, local_path)
                video_paths.append(local_path)

                # Update progress
                progress = 20 + (i + 1) * 30 // len(scenes)
                await update_task_progress(task_id, progress, "processing")
                runpod.serverless.progress_update(
                    {"id": task_id}, f"Downloaded video {i+1}/{len(scenes)}"
                )

            break  # Exit the async generator after processing
            await update_task_progress(task_id, 50, "processing")
            runpod.serverless.progress_update({"id": task_id}, "Stitching videos together...")

            output_filename = f"stitched_video_{script_id}_{int(datetime.now().timestamp())}.mp4"
            output_path = f"/tmp/stitched_videos/{output_filename}"
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            print(f"Stitching {len(video_paths)} videos...")
            stitched_path = video_stitcher.stitch_videos(video_paths, output_path)

            await update_task_progress(task_id, 60, "processing")
            runpod.serverless.progress_update({"id": task_id}, "Video stitching completed")

            # Step 4: Upload stitched video to S3
            s3_key = f"Vidflux-Assets/stitched-videos/{output_filename}"
            s3_url = s3_client.upload_file(stitched_path, s3_bucket, s3_key)

            # Save stitched video to database
            video_asset = VideoAsset(
                org_id=org_id,
                s3_url=s3_url,
                generation_method="rendered",
                script_id=script_id,
                local_path=None,  # Don't store local path since this is temporary
            )

            session.add(video_asset)
            await session.commit()

            result_data = {
                "video_stitching": {
                    "status": "completed",
                    "video_url": s3_url,
                    "asset_id": video_asset.asset_id,
                    "scenes_count": len(scenes),
                }
            }

            await update_task_progress(task_id, 70, "processing")
            runpod.serverless.progress_update({"id": task_id}, "Uploaded stitched video to S3")

            # Step 5: Audio stitching (if enabled)
            if auto_audio_stitch:
                runpod.serverless.progress_update({"id": task_id}, "Starting audio mixing...")

                # Get voiceover audio files
                voiceover_files = []
                for scene in scenes:
                    audio_query = (
                        select(AudioAsset)
                        .where(
                            and_(
                                AudioAsset.org_id == org_id,
                                AudioAsset.scene_id == scene.id,
                                AudioAsset.source_type == "voiceover",
                            )
                        )
                        .order_by(desc(AudioAsset.created_at))
                    )

                    result = await session.execute(audio_query)
                    audio_asset = result.scalar_one_or_none()

                    if audio_asset and audio_asset.s3_url:
                        # Download voiceover
                        local_audio_path = f"/tmp/audio/voiceover_{scene.id}.mp3"
                        os.makedirs(os.path.dirname(local_audio_path), exist_ok=True)

                        # Extract S3 key from URL
                        key = audio_asset.s3_url.replace(
                            f"https://{s3_bucket}.s3.{os.getenv('AWS_DEFAULT_REGION', 'us-east-2')}.amazonaws.com/",
                            "",
                        )
                        s3_client.download_file(s3_bucket, key, local_audio_path)
                        voiceover_files.append(local_audio_path)

                await update_task_progress(task_id, 80, "processing")
                runpod.serverless.progress_update(
                    {"id": task_id}, f"Downloaded {len(voiceover_files)} voiceover files"
                )

                # Perform audio mixing
                final_output_filename = (
                    f"final_video_{script_id}_{int(datetime.now().timestamp())}.mp4"
                )
                final_output_path = f"/tmp/final_videos/{final_output_filename}"
                os.makedirs(os.path.dirname(final_output_path), exist_ok=True)

                if voiceover_files:
                    print(f"Mixing audio with {len(voiceover_files)} voiceover files...")
                    final_video_path = audio_mixer.mix_video_with_audio(
                        video_path=stitched_path,
                        voiceover_files=voiceover_files,
                        output_path=final_output_path,
                        enable_ducking=enable_ducking,
                        enable_ai_enhancement=enable_ai_enhancement,
                    )
                else:
                    # No voiceovers, just copy the stitched video
                    import shutil

                    shutil.copy2(stitched_path, final_output_path)
                    final_video_path = final_output_path

                await update_task_progress(task_id, 90, "processing")
                runpod.serverless.progress_update({"id": task_id}, "Audio mixing completed")

                # Upload final video to S3
                final_s3_key = f"Vidflux-Assets/final-videos/{final_output_filename}"
                final_s3_url = s3_client.upload_file(final_video_path, s3_bucket, final_s3_key)

                # Update video asset with final version
                video_asset.s3_url = final_s3_url
                await session.commit()

                result_data["audio_stitching"] = {
                    "status": "completed",
                    "final_video_url": final_s3_url,
                    "voiceover_count": len(voiceover_files),
                    "ducking_enabled": enable_ducking,
                    "ai_enhancement_enabled": enable_ai_enhancement,
                }

                runpod.serverless.progress_update({"id": task_id}, "Uploaded final video to S3")
            else:
                result_data["audio_stitching"] = {"status": "skipped"}

            # Final success update
            await update_task_progress(task_id, 100, "completed", result=result_data)

            # Cleanup temporary files
            cleanup_files = video_paths + ([stitched_path] if auto_audio_stitch else [])
            if auto_audio_stitch:
                cleanup_files.extend(voiceover_files)
                if "final_video_path" in locals():
                    cleanup_files.append(final_video_path)

            for temp_file in cleanup_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except Exception as e:
                    print(f"Warning: Failed to cleanup {temp_file}: {str(e)}")

            return {
                "status": "completed",
                "script_id": script_id,
                "task_id": task_id,
                **result_data,
            }

    except Exception as e:
        error_msg = f"Video stitching failed: {str(e)}"
        print(f"ERROR: {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")

        await update_task_progress(task_id, 0, "failed", error_message=error_msg)
        raise Exception(error_msg)


def handler(job):
    """
    RunPod serverless handler for video stitching

    Expected input format:
    {
        "input": {
            "task_id": "uuid-string",
            "script_id": "uuid-string",
            "org_id": "org-string",
            "enable_ducking": true,
            "enable_ai_enhancement": false,
            "auto_audio_stitch": true
        }
    }
    """
    try:
        print("🎬 VidFlux Video Stitching Handler Started")

        job_input = job["input"]

        # Extract required parameters
        task_id = job_input.get("task_id")
        script_id = job_input.get("script_id")
        org_id = job_input.get("org_id")

        # Extract optional parameters
        enable_ducking = job_input.get("enable_ducking", True)
        enable_ai_enhancement = job_input.get("enable_ai_enhancement", False)
        auto_audio_stitch = job_input.get("auto_audio_stitch", True)

        # Validate required inputs
        if not task_id:
            raise ValueError("Missing required parameter: task_id")
        if not script_id:
            raise ValueError("Missing required parameter: script_id")
        if not org_id:
            raise ValueError("Missing required parameter: org_id")

        print(f"Processing job - Task: {task_id}, Script: {script_id}, Org: {org_id}")
        print(
            f"Options - Ducking: {enable_ducking}, AI Enhancement: {enable_ai_enhancement}, Auto Audio: {auto_audio_stitch}"
        )

        # Run the async stitching function
        result = asyncio.run(
            stitch_videos_async(
                script_id=script_id,
                org_id=org_id,
                task_id=task_id,
                enable_ducking=enable_ducking,
                enable_ai_enhancement=enable_ai_enhancement,
                auto_audio_stitch=auto_audio_stitch,
            )
        )

        print("✅ Video stitching completed successfully")
        return result

    except Exception as e:
        error_msg = f"Handler error: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")

        return {"error": error_msg, "status": "failed"}


# Start the serverless function
if __name__ == "__main__":
    runpod.serverless.start({"handler": handler})
